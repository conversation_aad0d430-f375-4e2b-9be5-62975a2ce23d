/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 09-17-2025
 * @last modified by  : <EMAIL>
**/
@IsTest
private class CC_cloneCaseCallMeBackAgenziaTest {

    @IsTest
    static void testCloneCase() {
        RecordType rt = [
            SELECT Id, Name 
            FROM RecordType 
            WHERE SObjectType = 'Case' AND Name = 'CallMeBackUnica' 
            LIMIT 1
        ];
        
        Case c = new Case(
            Subject = 'Caso di test',
            Status = 'New',
            Origin = 'Phone',
            RecordTypeId = rt.Id
        );
        insert c;

        CC_cloneCaseCallMeBackAgenzia.CloneCaseRequest req = new CC_cloneCaseCallMeBackAgenzia.CloneCaseRequest();
        req.caseId = c.Id;

        Test.startTest();
        try{
            List<CC_cloneCaseCallMeBackAgenzia.CloneCaseResponse> results = CC_cloneCaseCallMeBackAgenzia.cloneCase(new List<CC_cloneCaseCallMeBackAgenzia.CloneCaseRequest>{req});
        }catch(Exception ex){}
        Test.stopTest();

    }
}