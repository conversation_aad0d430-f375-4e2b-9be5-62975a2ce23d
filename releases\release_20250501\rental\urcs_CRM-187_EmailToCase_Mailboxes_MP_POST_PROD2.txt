------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
This manual procedure aims to configure the missing email address 
<EMAIL> for the NBTAssociazioniEConferme queue.
This email was identified as missing from the original US 187 implementation
after comparing the complete email list with the existing configuration.
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------
 
1. Login to Salesforce
2. Open Setup - Search for "Email-to-Case"
3. Configure Email-to-Case email address:
   - Click "New" and configure:
   - Routing Name: <EMAIL>
   - Email Address: <EMAIL>
   - Controlled by Permission Set: Checked
   - Case Owner: Queue -> NBTAssociazioniEConferme
   - Case Priority: Medium
   - Case Origin: Email Service
   - Case Record Type: ur_CaseES
   - Save
   - Request the mailbox owner to complete verification process

===============================================================================
Permission Set Configuration
===============================================================================

5. Update Permission Set for the new email address:
   - Open Setup - Search for "Permission Sets"
   - Search for: "Unipol Rental CS Manage Case" and select
   - Find setting for: "Routing" and select "Email-to-Case Routing Address Access"
   - Click edit and add "<EMAIL>" to Selected Email-to-Case Routing Addresses
   - Save

