import { LightningElement, api, wire, track } from 'lwc';
import { getRecord } from 'lightning/uiRecordApi';
import ACCOUNT_FIELD from '@salesforce/schema/Case.AccountId';
// import PHONE_FIELD from '@salesforce/schema/Account.AWSPhone__c';
import getCustomerByCaseId from '@salesforce/apex/CustomerUtils.getCustomerByCaseId';

export default class CC_avvioManualeChiamataDial extends LightningElement {
    @api recordId;   
    @track customerData;

    phoneNumber;
    accountId;

    connectedCallback() {
        this.loadCustomerData();
        console.log('CC_avvioManualeChiamataDial : connectedCallback : ', this.recordId);
    }

    async loadCustomerData() {
    try {
        const data = await getCustomerByCaseId({ caseId: this.recordId });
        this.customerData = data ? { ...data } : null;
        console.log('CustomerData loaded: ', JSON.stringify(this.customerData));
        console.log('LoadcustomerDataPhone: ', JSON.stringify(this.customerData.accountDetails?.AWSPhone__c));
        this.phoneNumber = this.customerData.accountDetails?.AWSPhone__c;
        } catch (error) {
            console.error('Error loading customer data:', error);
        }
    }

    @wire(getRecord, { recordId: '$recordId', fields: [ACCOUNT_FIELD] })
    caseRecord({ data }) {
        if (data) {
            this.accountId = data.fields.AccountId.value;
        }
    }

    // @wire(getRecord, { recordId: '$accountId', fields: [PHONE_FIELD] })
    // accountRecord({ data }) {
    //     if (data) {
    //         this.phoneNumber = data.fields.AWSPhone__c.value;
    //     }
    // }



}