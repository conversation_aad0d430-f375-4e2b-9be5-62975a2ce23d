<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{&quot;recordId&quot;:&quot;0019X00001LEBfWQAX&quot;,&quot;userId&quot;:&quot;0059X00000PlDpqQAF&quot;}</customJavaScript>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>Uni_Sinistri/GetData</name>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>AddUrl</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;createUrlSinistri&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;SinistriHandler&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;HTTPAction&quot; : &quot;%HTTPAction%&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>14.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>DeserializeHTTPResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%HTTPActionTemp% != \&quot;[]\&quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;deserializedResp&quot; : &quot;=DESERIALIZE(%HTTPActionTemp%)&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetAccount</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;idToSearch&quot;,
    &quot;element&quot; : &quot;recordId&quot;
  }, {
    &quot;inputParam&quot; : &quot;UserId&quot;,
    &quot;element&quot; : &quot;userId&quot;
  } ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;uniGetAccountSinistriNew&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>HTTPActionGetSinistri</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : true,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;result&quot;,
  &quot;integrationProcedureKey&quot; : &quot;IntegrationService_IntegrationService&quot;,
  &quot;responseJSONNode&quot; : &quot;HTTPActionTemp&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;integrationId&quot; : &quot;=\&quot;GetSinistri\&quot;&quot;,
    &quot;body&quot; : &quot;=&quot;,
    &quot;params&quot; : &quot;=SERIALIZE(params)&quot;
  },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>HTTPActionGetSinistri_old</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;isActive&quot; : false,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;retryCount&quot; : 0.0,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;varIsDev:isDev!=true&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/sinistri&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;sendBody&quot; : true,
    &quot;params&quot; : {
      &quot;codiceAgenzia&quot; : &quot;%Request:codiceAgenzia%&quot;,
      &quot;codiceFiscale&quot; : &quot;%Request:codiceFiscale%&quot;,
      &quot;userId&quot; : &quot;%Request:userId%&quot;,
      &quot;codiceCompagnia&quot; : &quot;%Request:codiceCompagnia%&quot;
    },
    &quot;timeout&quot; : 0.0,
    &quot;isCompressed&quot; : false
  }
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>Rest Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>%HTTPAction|1% = null   ISBLANK(%HTTPAction%)</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Log_HTTPActionGetSinistri</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;call&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;UniLogIntegrationProcedureSubmit&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;Message&quot; : &quot;HTTP Action log Sinistri Chiusi&quot;,
    &quot;ClassName&quot; : &quot;Sinistri&quot;,
    &quot;Payload&quot; : &quot;%HTTPAction%&quot;,
    &quot;MethodName&quot; : &quot;GetData&quot;
  },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetValues2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;url&quot;,
  &quot;responseJSONNode&quot; : &quot;HTTPAction:url&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;url&quot; : &quot;=CONCAT(\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=\&quot;, %HTTPAction:compagniaSinistro%,\&quot;-\&quot;,%HTTPAction:agenziaSinistro%,\&quot;-\&quot;,%HTTPAction:esercizioSinistro%,\&quot;-\&quot;,%HTTPAction:numeroSinistro%)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>LoopBlock1</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%HTTPAction|1% != null&quot;,
  &quot;loopList&quot; : &quot;HTTPAction&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;loopOutput&quot; : {
    &quot;HTTPAction&quot; : &quot;%HTTPAction%&quot;
  }
}</propertySetConfig>
        <sequenceNumber>12.0</sequenceNumber>
        <type>Loop Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>NetworkUserExtract</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;UserId&quot;,
    &quot;element&quot; : &quot;userId&quot;
  }, {
    &quot;inputParam&quot; : &quot;societa&quot;,
    &quot;element&quot; : &quot;&apos;SOC_1&apos;&quot;
  } ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;NetworkUserExtract&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseAction1</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;contatoreGenerale&quot; : &quot;=%contatoriValues:contatoreGenerale%&quot;,
    &quot;contatoreChiusi&quot; : &quot;=%contatoriValues:contatoreChiusi%&quot;,
    &quot;contatoreAperti&quot; : &quot;=%contatoriValues:contatoreAperti%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;ListDef&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>15.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseToList</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;deserializedResp&quot;,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%HTTPActionTemp% != \&quot;[]\&quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;deserializedResp&quot; : &quot;=LIST(DeserializeHTTPResponse:deserializedResp)&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetQueryParams</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;params:Params:QueryParam&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;codiceAgenzia&quot; : &quot;=%GetAccount:Account:codiceAgenzia%&quot;,
    &quot;userId&quot; : &quot;=%NetworkUserExtract:NetUser%&quot;,
    &quot;codiceCompagnia&quot; : &quot;=%GetAccount:Account:societa%&quot;,
    &quot;codiceFiscale&quot; : &quot;=%GetAccount:Account:codiceFiscale%&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetValues3</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;List&quot;,
  &quot;responseJSONNode&quot; : &quot;ListDef:Lista&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%HTTPAction|1% != null&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;List&quot; : &quot;=LIST(LoopBlock1:HTTPAction)&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>13.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>Set Values Action</description>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SinistroToList</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;elementValueMap&quot; : {
    &quot;SinistroToList&quot; : &quot;=LIST(%HTTPAction%)&quot;
  },
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;responseJSONPath&quot; : &quot;SinistroToList:SinistroToList&quot;,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;%HTTPAction:1% = null&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;restOptions&quot; : { }
}</propertySetConfig>
        <sequenceNumber>11.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>varIsDev</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;isDev&quot; : &quot;=false&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>varResponseMock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;responseMock&quot;,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;varIsDev:isDev=true&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;responseMock&quot; : &quot;=DESERIALIZE(&apos;[    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;1853\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8101\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2024-03-24T23:00:00Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2024-03-22T07:28:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2024-03-22T07:28:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;996\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;SARC ID DEBITORI\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2024\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2024/585176\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;167347191\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;0227625\&quot;,       \&quot;pagato\&quot;:\&quot;0\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;030\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;RCA\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;senza_seguito\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Senza seguito\&quot;,       \&quot;subAgenziaPolizza\&quot;:\&quot;800\&quot;,       \&quot;targaSinistro\&quot;:\&quot;FN286RP\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8101-2024-0227625\&quot;    },    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;1853\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8101\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2023-10-18T22:00:00Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2023-10-18T05:20:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2023-10-18T05:20:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;8867\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;GESTIONE CANALIZZAZIONE UNIPOLSERVICE\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2023\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2023/2369546\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;195422245\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;952687\&quot;,       \&quot;pagato\&quot;:\&quot;1430.65\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;030\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;RCA\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;liquidato_totale\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Liquidato totale\&quot;,       \&quot;subAgenziaPolizza\&quot;:\&quot;800\&quot;,       \&quot;targaSinistro\&quot;:\&quot;GN545HM\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8101-2023-952687\&quot;    },    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;1853\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8101\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2022-10-17T10:36:03Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2022-10-15T06:20:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2022-10-15T06:20:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;8867\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;GESTIONE CANALIZZAZIONE UNIPOLSERVICE\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2022\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2022/2088133\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;167347191\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;0815594\&quot;,       \&quot;pagato\&quot;:\&quot;682.98\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;030\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;RCA\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;liquidato_totale\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Liquidato totale\&quot;,       \&quot;subAgenziaPolizza\&quot;:\&quot;800\&quot;,       \&quot;targaSinistro\&quot;:\&quot;FN286RP\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8101-2022-0815594\&quot;    },    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;1853\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8001\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2022-03-14T23:00:00Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2022-03-09T23:00:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2022-03-10T14:00:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;857\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;CALL CENTER SINISTRI\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2022\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2022/466472\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;170174875\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;0067980\&quot;,       \&quot;pagato\&quot;:\&quot;97.6\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;016\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;Cristalli veicoli\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;liquidato_totale\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Liquidato totale\&quot;,       \&quot;subAgenziaPolizza\&quot;:\&quot;800\&quot;,       \&quot;targaSinistro\&quot;:\&quot;FR868ZZ\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8001-2022-0067980\&quot;    },    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;1853\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8001\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2022-03-11T23:00:00Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2022-02-27T23:00:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2022-02-28T10:00:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;857\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;CALL CENTER SINISTRI\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2022\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2022/442015\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;167347191\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;0066274\&quot;,       \&quot;pagato\&quot;:\&quot;97.6\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;016\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;Cristalli veicoli\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;liquidato_totale\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Liquidato totale\&quot;,       \&quot;subAgenziaPolizza\&quot;:\&quot;800\&quot;,       \&quot;targaSinistro\&quot;:\&quot;FN286RP\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8001-2022-0066274\&quot;    },    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;1853\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8101\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2019-11-27T14:14:27Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2019-11-24T17:40:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2019-11-24T17:40:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;8867\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;GESTIONE CANALIZZAZIONE UNIPOLSERVICE\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2019\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2019/2393903\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;170174875\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;1007635\&quot;,       \&quot;pagato\&quot;:\&quot;719.95\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;030\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;RCA\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;liquidato_totale\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Liquidato totale\&quot;,       \&quot;subAgenziaPolizza\&quot;:\&quot;800\&quot;,       \&quot;targaSinistro\&quot;:\&quot;FR868ZZ\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8101-2019-1007635\&quot;    },    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;33139\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8101\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2015-06-29T14:11:04Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2015-06-28T00:45:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2015-06-28T00:45:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;996\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;SARC ID DEBITORI\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2015\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2015/470450\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;116457162\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;0234146\&quot;,       \&quot;pagato\&quot;:\&quot;0\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;030\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;RCA\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;senza_seguito\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Senza seguito\&quot;,       \&quot;targaSinistro\&quot;:\&quot;DT519DX\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8101-2015-0234146\&quot;    } ]&apos;)&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessKey>Sinistri_GetData</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;transientValues&quot; : {
    &quot;deactivateConsent&quot; : false
  }
}</propertySetConfig>
    <subType>GetData</subType>
    <type>Sinistri</type>
    <uniqueName>Sinistri_GetData_Procedure_12</uniqueName>
    <versionNumber>12.0</versionNumber>
    <webComponentKey>82f07cf3-7624-f1c4-5590-ae4ebe6a4182</webComponentKey>
</OmniIntegrationProcedure>
