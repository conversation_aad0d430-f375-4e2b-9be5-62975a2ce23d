/**
 * @File Name         : urcs_SendEmail.cls
 * @Description       : 
 * <AUTHOR> ACN DEV TEAM
 * @Group             : 
 * @Last Modified On  : 02-09-2025
 * @Last Modified By  : ACN DEV TEAM
 * @cicd_tests  urcs_SendEmail_Test
**/
public with sharing class urcs_SendEmail {
    
    /******************************************************************************************
    * @description  This method send an email to a specific email address
    * @param  caseId      
    * @param  templateName      
    * @param  targetEmail
    * @return void       
    *******************************************************************************************/
 
	 public static void sendEmailToTargetAddress(String templateName, Id whoId, Id whatId, String targetAddress){
        
        try{
            /*
            String targetEmail  = '<EMAIL>';
            Id caseId           = '5009O00000dSBIIQA4'; 
            String templateName = 'urcs_CaseNotificaSollecito'; 
			urcs_SendEmail.sendEmailOnCase(caseId, templateName, targetEmail);
            */
            if (templateName != null && targetAddress != null){
                
                List<EmailTemplate> et = [SELECT Id FROM EmailTemplate WHERE Name =: templateName LIMIT 1];
                List<OrgWideEmailAddress> owea = [SELECT Id FROM OrgWideEmailAddress where DisplayName ='<EMAIL>' and IsVerified = true LIMIT 1];
                
                if (!et.isEmpty() && !owea.isEmpty()){
                    //Messaging.renderStoredEmailTemplate(String templateId, String whoId, String whatId)
                    Messaging.SingleEmailMessage renderedMail = Messaging.renderStoredEmailTemplate(et.get(0).Id, whoId, whatId);
                    renderedMail.setOrgWideEmailAddressId(owea.get(0).Id);
                    // Aggiornamento destinatario
                    renderedMail.setToAddresses(new String[] { targetAddress });
                    renderedMail.setSaveAsActivity(true);
                    
                    Messaging.SendEmailResult[] results = Messaging.sendEmail(new Messaging.SingleEmailMessage[] { renderedMail });
                    System.debug('Email inviata? ' + results[0].isSuccess());
                  
                }
            }
        }
        catch(Exception e){
            System.debug('Exception on method urcs_SendEmail.sendEmail: '+e);    
        } 
 
    }
    
    /******************************************************************************************
    * @description  This method send an email to a specific email address
    * @param  List<InvocableParameters> ipList 
    * @return void       
    *******************************************************************************************/
    @InvocableMethod(label='sendEmailToTargetAddressInvocable')
	public static void sendEmailToTargetAddressInvocable(List<InvocableParameters> ipList){
        if (!ipList.isEmpty()){
            Id whoId             = ipList.get(0).whoId;
            Id whatId            = ipList.get(0).whatId;
            String templateName  = ipList.get(0).templateName;
            String targetAddress = ipList.get(0).targetAddress;
            
            sendEmailToTargetAddress(templateName, whoId, whatId, targetAddress);
        }
         
    }
    
    public class InvocableParameters {
        @InvocableVariable public Id     whoId;
        @InvocableVariable public Id     whatId;
        @InvocableVariable public String templateName;
        @InvocableVariable public String targetAddress;
    }
}