/*
* @description 
* @cicd_tests CtrlFeiUserSelect_Test
*/
public without sharing class CtrlFeiUserSelect {

    public class NetworkUserWrapper{
        @AuraEnabled public String label            {get; set;}
        @AuraEnabled public String value            {get; set;}
    }
    
    /*
    * @description 
    * @param String permissionSetName String with permission set names, from FEI_Settings__mdt configuration
    * @return List<String> List of UNIPOL User Ids
    */
    @AuraEnabled(cacheable=true)
    public static List<NetworkUserWrapper> getPicklistUsers(String permissionSetName, String society){
        
        System.debug('ps --> '+permissionSetName);
        System.debug('soc --> '+society);

        User currentUser = [SELECT Id, FederationIdentifier, UCA_Permissions__c FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];

        System.debug('fi --> '+currentUser.FederationIdentifier);

        UserProvisioningUpdate.UCAResult ucaResponse = (String.isBlank(currentUser.UCA_Permissions__c) || currentUser.UCA_Permissions__c == null) ? null : (UserProvisioningUpdate.UCAResult)JSON.deserialize(currentUser.UCA_Permissions__c, UserProvisioningUpdate.UCAResult.class);

        List<NetworkUserWrapper> result = new List<NetworkUserWrapper>();
        NetworkUserWrapper n = new NetworkUserWrapper();
        n.label = '';
        n.value = '';
        result.add(n);

        //String soc = (String.isNotBlank(society)) ? society : 'SOC_1';

        try{

            if(String.isNotBlank(society) && !'null'.equalsIgnoreCase(society)){

                System.debug('1');

                List<NetworkUser__c> defaultNU = [SELECT Id, NetworkUser__c, Society__c, Agency__c, Agency__r.AgencyCode__c FROM NetworkUser__c WHERE FiscalCode__c = :currentUser.FederationIdentifier AND IsActive__c = true AND Preferred__c = true AND Society__c = :society LIMIT 1];
                if(!defaultNU.isEmpty()){

                    System.debug('1.1');
                    
                    Set<Id> agencyIds = new Set<Id>();
                    for(NetworkUser__c nu : defaultNU){
                        agencyIds.add(nu.Agency__c);
                    }
                    
                    Map<String, String> mapAgencyCode = getMapAgencyCode(society, agencyIds);

                    if(UserProvisioningUtils.isEligible(defaultNU[0].NetworkUser__c, permissionSetName, ucaResponse)){
                        NetworkUserWrapper nuw = new NetworkUserWrapper();
                        nuw.label = defaultNU[0].NetworkUser__c;
                        nuw.value = defaultNU[0].NetworkUser__c;
                        //nuw.value += '$$' + String.valueOf(Integer.valueOf(defaultNU[0].NetworkUser__c.substring(1,6)));
                        //nuw.value += '$$' + defaultNU[0].Agency__r.AgencyCode__c;
                        nuw.value += '$$' + mapAgencyCode.get(defaultNU[0].Agency__c);
                        nuw.value += (String.isNotBlank(defaultNU[0].Society__c)) ? '$$' + defaultNU[0].Society__c.split('_')[1] : '$$' + defaultNU[0].NetworkUser__c.substring(0,1);
                        result.add(nuw);   
                    }else{
                        result = new List<NetworkUserWrapper>();
                        NetworkUserWrapper nuw = new NetworkUserWrapper();
                        nuw.label = 'ERR-100'; // ERR-100 --> User default not eligible
                        nuw.value = 'ERR-100'; // ERR-100 --> User default not eligible
                        result.add(nuw);
                    }

                }else{

                    System.debug('1.2');

                    List<NetworkUser__c> nuList = UserProvisioningUtils.getAllowedNetworkUser(currentUser.FederationIdentifier, permissionSetName, society, ucaResponse);
                    
                    Set<Id> agencyIds = new Set<Id>();
                    for(NetworkUser__c nu : nuList){
                        agencyIds.add(nu.Agency__c);
                    }
                    
                    Map<String, String> mapAgencyCode = getMapAgencyCode(society, agencyIds);

                    if(nuList.isEmpty()){

                        result = new List<NetworkUserWrapper>();
                        NetworkUserWrapper nuw = new NetworkUserWrapper();
                        nuw.label = 'ERR-200'; // ERR-200 --> No User eligible
                        nuw.value = 'ERR-200'; // ERR-200 --> No User eligible
                        result.add(nuw);

                    }else{

                        System.debug('nuList --> '+nuList);

                        for(NetworkUser__c nu : nuList){

                            NetworkUserWrapper nuw = new NetworkUserWrapper();
                            nuw.label = nu.NetworkUser__c;
                            nuw.value = nu.NetworkUser__c;
                            //nuw.value += '$$' + String.valueOf(Integer.valueOf(nu.NetworkUser__c.substring(1,6)));
                            //nuw.value += '$$' + nu.Agency__r.AgencyCode__c;
                            nuw.value += '$$' + mapAgencyCode.get(nu.Agency__c);
                            nuw.value += (String.isNotBlank(nu.Society__c)) ? '$$' + nu.Society__c.split('_')[1] : '$$' + nu.NetworkUser__c.substring(0,1);
                            result.add(nuw); 

                        }

                    }

                }

            }else{

                System.debug('2');

                List<NetworkUser__c> nuList = UserProvisioningUtils.getAllowedNetworkUser(currentUser.FederationIdentifier, permissionSetName, society, ucaResponse);
                
                Set<Id> agencyIds = new Set<Id>();
                for(NetworkUser__c nu : nuList){
                    agencyIds.add(nu.Agency__c);
                }
                
                Map<String, String> mapAgencyCode = getMapAgencyCode(null, agencyIds);

                if(nuList.isEmpty()){

                    System.debug('2.1');

                    result = new List<NetworkUserWrapper>();
                    NetworkUserWrapper nuw = new NetworkUserWrapper();
                    nuw.label = 'ERR-200'; // ERR-200 --> No User eligible
                    nuw.value = 'ERR-200'; // ERR-200 --> No User eligible
                    result.add(nuw);

                }else{

                    System.debug('2.2');

                    for(NetworkUser__c nu : nuList){

                        NetworkUserWrapper nuw = new NetworkUserWrapper();
                        nuw.label = nu.NetworkUser__c;
                        nuw.value = nu.NetworkUser__c;
                        //nuw.value += '$$' + String.valueOf(Integer.valueOf(nu.NetworkUser__c.substring(1,6)));
                        nuw.value += '$$' + mapAgencyCode.get(nu.Society__c+'_' + nu.Agency__c);
                        nuw.value += (String.isNotBlank(nu.Society__c)) ? '$$' + nu.Society__c.split('_')[1] : '$$' + nu.NetworkUser__c.substring(0,1);
                        result.add(nuw); 

                    }

                }

            }
            
            System.debug('@@@ --> '+result);

        }catch(Exception ex){
            System.debug('Exception --> '+ex.getMessage());
            System.debug('Exception --> '+ex.getStackTraceString());
        }
        return result;
        
    }
    
    public static Map<String, String> getMapAgencyCode(String society, Set<Id> agencyIds){
        
        Map<String, String> result = new Map<String, String>();
        
        if(agencyIds == null || agencyIds.isEmpty()) return result;
        
        if(String.isNotBlank(society)){
	        List<FinServ__AccountAccountRelation__c> relationList = [Select Id, FinServ__Account__c, RelatedAccount_ExternalId__c, Identifier__c from FinServ__AccountAccountRelation__c where RecordType.DeveloperName ='AgencySociety' and FinServ__Account__c IN :agencyIds and RelatedAccount_ExternalId__c = :society];
    	    for(FinServ__AccountAccountRelation__c aar : relationList){
        	    result.put(aar.FinServ__Account__c, aar.Identifier__c);
	        }
            
        }else{
            
            List<FinServ__AccountAccountRelation__c> relationList = [Select Id, FinServ__Account__c, RelatedAccount_ExternalId__c, Identifier__c from FinServ__AccountAccountRelation__c where RecordType.DeveloperName ='AgencySociety' and FinServ__Account__c IN :agencyIds];
    	    for(FinServ__AccountAccountRelation__c aar : relationList){
        	    result.put(aar.RelatedAccount_ExternalId__c + '_' + aar.FinServ__Account__c, aar.Identifier__c);
	        }
            
        }
        
        return result;
        
    }
    
}