public with sharing class TitoliFeiPermissionHelper {
    
    //TOBE MOVED TO CUSTOM METADATA MAYBE
    private static final Map<String, List<String>> actionCustomPermissionMap = new Map<String, List<String>>{
        'incassa' => new List<String>{'X183_401010000','X183_401010100','X183_401040000','X183_401040200','X183_401040300'},
        'sostituzione' => new List<String>{'X184_201010000','X184_206110100','X184_206110200'},
        'incassoMultiplo' => new List<String>{'X186_401010000','X186_401010100','X186_401040000','X186_401040200','X186_401040300'},
        'incasso' => new List<String>{'X187_401010000','X187_401010100','X187_401040000','X187_401040200','X187_401040300'},
        'variazione' => new List<String>{'X191_201030100','X191_201030100','X191_201030200','X191_201030300','X191_201030400','X191_201030500','X191_201030600','X191_201030700','X191_201030800','X191_201030900','X191_201031000'},
        'rettifica' => new List<String>{'X193_401010200'}
    };  
    
    @AuraEnabled
    public static Map<String, Object> getTitoliInScadenzaFromIntegrationProcedure(String accountId){
        Map<String, Object> ipInput = new Map<String, Object>{'AccountId' => accountId, 'UserId' => UserInfo.getUserId(), 'isAbbinato' => false};
        Map<String, Object> ipOutput = (Map<String, Object>) omnistudio.IntegrationProcedureService.runIntegrationService('UniDS_TitoliInScadenza', ipInput, null);   
         return ipOutput;
    }

    @AuraEnabled
    public static Map<String, Object> getPrt(Map<String, Object> inputParams){
        try {
        Map<String, Object> ipOutput = (Map<String, Object>) omnistudio.IntegrationProcedureService.runIntegrationService('Titoli_RecuperaPrt', inputParams, null);   
         return ipOutput;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Map<String, Object> getPostalizzatoreMotiviRistampa(){
        try {
            Map<String, Object> ipOutput = (Map<String, Object>) omnistudio.IntegrationProcedureService.runIntegrationService('Titoli_AbilitazionePostalizzatore', null, null);   
            return ipOutput;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Map<String, Object> invioRemoto(Map<String, Object> inputParams){
        try {
            Map<String, Object> ipOutput = (Map<String, Object>) omnistudio.IntegrationProcedureService.runIntegrationService('Titoli_InvioRemoto', inputParams, null);   
            return ipOutput;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Map<String, Object> abilitaAzioneRiprendi(Map<String, Object> inputParams){
        try {
            Map<String, Object> ipOutput = (Map<String, Object>) omnistudio.IntegrationProcedureService.runIntegrationService('Titoli_AbilitaAzioneRiprendi', inputParams, null);
             return ipOutput;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Map<String, Boolean> getUserMandati(String accId) {
        Boolean hasFinAccountRelUnipol = [SELECT Id FROM FinServ__AccountAccountRelation__c WHERE FinServ__Account__c =: accId AND RelatedAccount_ExternalId__c = 'SOC_1' LIMIT 1].isEmpty() == false;
        Boolean hasFinAccountRelUnisalute = [SELECT Id FROM FinServ__AccountAccountRelation__c WHERE FinServ__Account__c =: accId AND RelatedAccount_ExternalId__c = 'SOC_4' LIMIT 1].isEmpty() == false;
        Boolean hasPermissionSetUnipol = [SELECT Id FROM PermissionSetAssignment WHERE AssigneeId =: UserInfo.getUserId() AND PermissionSet.Name = 'MandatoUnipolSai' LIMIT 1].isEmpty() == false;
        Boolean hasPermissionSetUnisalute = [SELECT Id FROM PermissionSetAssignment WHERE AssigneeId =: UserInfo.getUserId() AND PermissionSet.Name = 'MandatoUnisalute' LIMIT 1].isEmpty() == false;
        return new Map<String, Boolean>{'hasMandatoUnipol' => hasFinAccountRelUnipol && hasPermissionSetUnipol, 'hasMandatoUnisalute' => hasFinAccountRelUnisalute && hasPermissionSetUnisalute};
    }
    
    @AuraEnabled
    public static Map<String, String> getParamsForFei(String feiId) {
        String permissionSetName = [
            SELECT Id, UCA_Permission_Name__c FROM FEI_Settings__mdt 
            WHERE Label =: feiId AND Environment__c =: FEI_Environment__c.getInstance().Environment__c LIMIT 1]?.UCA_Permission_Name__c;

        String fiscalCode = [SELECT FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1]?.FederationIdentifier;
            return new Map<String, String>{'permissionSetName' => permissionSetName, 'fiscalCode' => fiscalCode};
        }
    
    @AuraEnabled
    public static List<Action> enableActionsAsNeeded(String actionsJSON) {
        List<Action> updatedActions = new List<Action>();
        List<Action> actionList = (List<Action>)  JSON.deserialize(actionsJSON, List<Action>.class);
        for(Action action : actionList){
            String actionName = action.name;
            if(actionCustomPermissionMap.keySet().contains(actionName) == false){
                action.disabled = false;
                updatedActions.add(action);
                continue;
        	}
            for(String customPermissionDeveloperName : actionCustomPermissionMap.get(actionName)) {
                if(FeatureManagement.checkPermission(customPermissionDeveloperName)){
                    action.disabled = false;
                    break;
                }
                action.disabled = true;
            }
            updatedActions.add(action);
        }
                
        return updatedActions;
    }

    @AuraEnabled
    public static Account getAccountData(String accountId){
        try {
            
            return [SELECT Id, ExternalId__c FROM Account WHERE Id = :accountId LIMIT 1];
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Map<String, AccountDetails__c> getAccountDetailsData(String accountId){
        try {
            
            AccountDetails__c detailsUnipol = [SELECT Id, SourceSystemIdentifier__c 
                                 FROM AccountDetails__c 
                                 WHERE Relation__r.FinServ__Account__c = :accountId 
                                 AND Relation__r.FinServ__RelatedAccount__r.Name = 'UNIPOL' LIMIT 1];

            AccountDetails__c detailsUniSalute = [SELECT Id, SourceSystemIdentifier__c 
                                 FROM AccountDetails__c 
                                 WHERE Relation__r.FinServ__Account__c = :accountId 
                                 AND Relation__r.FinServ__RelatedAccount__r.Name = 'UNISALUTE' LIMIT 1];

            Map<String, AccountDetails__c> detailsMap = new Map<String, AccountDetails__c>();
            
            if (detailsUnipol != null) {
                detailsMap.put('Unipol', detailsUnipol);
            }
            if (detailsUniSalute != null) {
                detailsMap.put('UniSalute', detailsUniSalute);
            }

            return detailsMap;

        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    
    @testVisible
	private class Action { 
        @AuraEnabled public String label { get; set;}
        @AuraEnabled public String name { get; set; }
        @AuraEnabled public Boolean disabled { get; set; }

        public Action(String label, String name, Boolean disabled) {
            this.label = label;
            this.name = name;
            this.disabled = disabled;
        }
	}

}