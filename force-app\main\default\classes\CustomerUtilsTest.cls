@IsTest
private class CustomerUtilsTest {

    @TestSetup
    static void setupData() {
        // Account con RecordType standard (prendiamo il primo disponibile)
        Account acc = new Account(
            Name = 'Test Account'
        );
        insert acc;
        
        Account acc2 = new Account(
            Name = 'Test Account 2'
        );
        insert acc2;

        // Contact collegato (simula PersonContactId)
        Contact con = new Contact(
            LastName = 'Rossi',
            FirstName = 'Mario',
            AccountId = acc.Id,
            Email = '<EMAIL>'
        );
        insert con;

        // Case collegato all’account
        Case c = new Case(
            Subject = 'Test Case',
            AccountId = acc.Id
        );
        insert c;

        // Opportunity
        Opportunity opp = new Opportunity(
            Name = 'Test Opp',
            AccountId = acc.Id,
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(10)
        );
        insert opp;

        // Quote collegato
        Quote q = new Quote(
            Name = 'Test Quote',
            OpportunityId = opp.Id,
            Status = 'Draft'
        );
        insert q;

        // Coverage
        OpportunityCoverage__c cov = new OpportunityCoverage__c(
            Name = 'Coverage Test',
            Quote__c = q.Id,
            AreaOfNeed__c = 'Casa'
        );
        insert cov;

        // Asset con ExternalId
        Asset a = new Asset(
            Name = 'Test Asset',
            AccountId = acc.Id,
            ExternalId__c = 'X_SOC_123_CODE'
        );
        insert a;
        
        FinServ__ReciprocalRole__c role1 = new FinServ__ReciprocalRole__c();
        role1.Name = 'TEST';
        role1.FinServ__RelationshipType__c = 'All';
        role1.FinServ__InverseRole__c = 'TEST2';
        insert role1;
        
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c();
        role.Name = 'TEST2';
        role.FinServ__RelationshipType__c = 'All';
        role.FinServ__InverseRole__c = 'TEST';
        role.FinServ__InverseRelationship__c = role1.Id;
        insert role;
        
        role1.FinServ__InverseRelationship__c = role.Id;
        update role1;

        // Relazione Finanziaria
        FinServ__AccountAccountRelation__c rel = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = acc.Id,
            FinServ__RelatedAccount__c = acc2.Id,
			FinServ__Role__c = role.Id,
            FinServ__ExternalId__c = 'X_SOC_123'
        );
        insert rel;

        // AccountDetails
        AccountDetails__c det = new AccountDetails__c(
            Name = 'Detail',
            Relation__c = rel.Id,
            Account__c = acc.Id
        );
        insert det;

        // AccountDetailsNPI
        AccountDetailsNPI__c detNpi = new AccountDetailsNPI__c(
            Name = 'DetailNPI',
            Relation__c = rel.Id
        );
        insert detNpi;
    }

    @IsTest
    static void testBuildByCaseId() {
        Case c = [SELECT Id FROM Case LIMIT 1];
        new CustomerUtils().buildByCaseId(c.Id);

        // Static wrapper
        CustomerUtils.getCustomerByCaseId(c.Id);
    }

    @IsTest
    static void testBuildByAccountId() {
        Account acc = [SELECT Id FROM Account LIMIT 1];
        new CustomerUtils().buildByAccountId(acc.Id);

        // Static wrapper
        CustomerUtils.getCustomerByAccountId(acc.Id);
    }

    @IsTest
    static void testGetCustomerByInputWithCaseId() {
        Case c = [SELECT Id FROM Case LIMIT 1];
        String inputJson = '{"caseId":"' + c.Id + '"}';
        CustomerUtils.getCustomerByInput(inputJson);
    }

    @IsTest
    static void testGetCustomerByInputWithAccountId() {
        Account acc = [SELECT Id FROM Account LIMIT 1];
        String inputJson = '{"accountId":"' + acc.Id + '"}';
        CustomerUtils.getCustomerByInput(inputJson);
    }

    @IsTest
    static void testUtilityMethods() {
        Account acc = [SELECT Id, PersonContactId FROM Account LIMIT 1];
        Opportunity opp = [SELECT Id, AccountId FROM Opportunity LIMIT 1];
        Quote q = [SELECT Id FROM Quote LIMIT 1];

        // Wrappers diretti
        CustomerUtils.getAccount(acc.Id);
        CustomerUtils.getContact(acc.PersonContactId);
        CustomerUtils.getOpportunitiesByAccount(acc.Id);
        CustomerUtils.getQuotesByAccount(acc.Id);
        CustomerUtils.getQuotesByOpportunityIds(new Set<Id>{opp.Id});
        CustomerUtils.getQuotesByOpportunities(new List<Opportunity>{opp});
        CustomerUtils.getOpportunityCoveragesByQuotes(new List<Quote>{q});
        CustomerUtils.getOpportunityCoveragesByQuoteIds(new Set<Id>{q.Id});
        CustomerUtils.getAssetsByAccountId(acc.Id);
    }
}