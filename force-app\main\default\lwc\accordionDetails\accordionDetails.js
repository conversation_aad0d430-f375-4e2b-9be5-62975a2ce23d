import { LightningElement, api } from 'lwc';
import areas_of_need_images from '@salesforce/resourceUrl/areas_of_need';

const ESSIG_AUTO_IMG = areas_of_need_images + '/areas_of_need/on/essigauto.png';
// Define the AccordionDetails class that extends LightningElement
export default class AccordionDetails extends LightningElement {
    @api opportunityCoverage; // Public property to hold the opportunity coverage data
    @api source; // Parent opportunity source channel
    get isPrevidenza()
    {
        return this.source == 'Preventivatore Previdenza';
    }

    // Getter to determine if the title should be displayed
    get displayTitle() { 
        return this.opportunityCoverage.isFirst; 
    }
    
    // Getter to retrieve the image for the area of need
get areaOfNeedImage() {
        const dt = (this.opportunityCoverage?.domainType || '').trim().toUpperCase();

        if (dt === 'ESSIG_AUTO') {
            console.log('🖼️ Child → ESSIG_AUTO rilevato, forzo immagine:', ESSIG_AUTO_IMG);
            return ESSIG_AUTO_IMG;
        }

        return this.opportunityCoverage.areaOfNeedImage;
    }
    // Getter to retrieve the asset items
    get assetItems() { 
        return this.opportunityCoverage.assetItems; 
    }
    
    // Getter to retrieve the description items
    get descriptionItems() { 
        return this.opportunityCoverage.descriptionItems; 
    }
    
    // Getter to retrieve the amount
    get amount() { 
        return this.opportunityCoverage.amount; 
    }
    
    // Getter to retrieve the stage
    get stage() { 
        return this.opportunityCoverage.stage; 
    }
    
    // Getter to retrieve the fractionation details
    get fractionation() { 
        return this.opportunityCoverage.fractionation; 
    }
    
    // Getter to retrieve the conventions
   get conventions() { 
        if(this.opportunityCoverage.conventions != '00000') {
            return this.opportunityCoverage.conventions; 
        } else {
            return '-';
        }
    }
    
    // Getter to retrieve the full name
    get fullName() { 
        return this.opportunityCoverage.fullName; 
    }

    get monthlyContribution()
    {
        return this.opportunityCoverage.monthlyContribution;
    }

    get ral()
    {
        return this.opportunityCoverage.ral;
    }

    get yearlyGrowth()
    {
        return this.opportunityCoverage.yearlyGrowth;
    }

    get previdentialGap()
    {
        return this.opportunityCoverage.previdentialGap;
    }

    get retirementYear()
    {
        return this.opportunityCoverage.retirementYear;
    }

    get targetProduct()
    {
        return this.opportunityCoverage.targetProduct;
    }

    get numOfChildren()
    {
        return this.opportunityCoverage.numOfChildren;
    }

    get sector()
    {
        return this.opportunityCoverage.sector;
    }

    // accordionDetails.js
renderedCallback() {
  if (this.opportunityCoverage && !this._logged) {
    console.log('🧩 coverage in child:', JSON.stringify(this.opportunityCoverage));
    console.log('isPrevidenza?', this.isPrevidenza);
    this._logged = true;
  }
}

}