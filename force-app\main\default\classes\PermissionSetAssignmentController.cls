public with sharing class PermissionSetAssignmentController {
    /**
     * Aggiorna il campo Personas__c per uno user
     */
    @AuraEnabled
    public static void updateUserPersonas(String userId, String personasValue) {
        System.debug('updateUserPersonas: userId=' + userId + ', personasValue=' + personasValue);
        if (String.isBlank(userId)) {
            throw new AuraHandledException('UserId mancante.');
        }
        try {
            User u = [SELECT Id, Personas__c FROM User WHERE Id = :userId LIMIT 1];
            // Pulizia: rimuovi solo spazi iniziali e finali e virgolette
            String personasLabel = '';
            String personasApiName = '';
            if (personasValue != null) {
                personasApiName = personasValue;
                // Rimuovi solo spazi iniziali e finali
                personasApiName = personasApiName.trim();
                // Rimuovi virgolette iniziali e finali se presenti
                if (personasApiName.startsWith('"') && personasApiName.endsWith('"')) {
                    personasApiName = personasApiName.substring(1, personasApiName.length() - 1);
                }
                // Se il valore è quello placeholder, non aggiornare
                if (personasApiName == '{Session.selectedProfile}') {
                    System.debug('Personas__c non aggiornata: valore placeholder rilevato.');
                    return;
                }
            }
            System.debug('Personas__c label tu update-> personasValue: ' +personasValue+', personasLabel: '+ personasLabel + ', API Name: ' + personasApiName);
            u.Personas__c = personasApiName;
            update u;
            System.debug('Personas__c label salvata: ' + personasLabel + ', API Name: ' + personasApiName);
        } catch (Exception e) {
            System.debug('Errore updateUserPersonas: ' + e.getMessage());
            throw new AuraHandledException('Errore durante l\'aggiornamento del campo Personas__c: ' + e.getMessage());
        }
    }
    /**
     * Recupera i nomi dei PermissionSet assegnati a uno user
     */
    @AuraEnabled
    public static List<String> getAssignedPermissionSetNames(String userId) {
        System.debug('@@DEBUG userId in getAssignedPermissionSetNames: ' + userId);
        // Query su PermissionSetAssignment
        List<PermissionSetAssignment> assignments = [
            SELECT PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId = :userId
        ];
        List<String> names = new List<String>();
        for (PermissionSetAssignment psa : assignments) {
            names.add(psa.PermissionSet.Name);
        }
        return names;
    }

    /**
     * Rimuove in bulk le PermissionSetAssignment per uno user e una lista di PermissionSet Name
     */
    @AuraEnabled
    public static void removePermissionSetAssignments(Id assigneeId, List<String> permissionSetNames) {
        if (assigneeId == null || permissionSetNames == null || permissionSetNames.isEmpty()) {
            throw new AuraHandledException('AssigneeId o lista PermissionSetNames vuota.');
        }
        try {
            // Recupera gli Id dei PermissionSet da nome
            Map<String, Id> nameToId = new Map<String, Id>();
            for (PermissionSet ps : [SELECT Id, Name FROM PermissionSet WHERE Name IN :permissionSetNames]) {
                nameToId.put(ps.Name, ps.Id);
            }
            if (nameToId.isEmpty()) {
                return;
            }
            // Recupera tutte le PermissionSetAssignment da cancellare in un'unica query
            List<PermissionSetAssignment> toDelete = [
                SELECT Id FROM PermissionSetAssignment
                WHERE AssigneeId = :assigneeId
                AND PermissionSetId IN :nameToId.values()
            ];
            if (!toDelete.isEmpty()) {
                delete toDelete;
            }
        } catch (Exception e) {
            throw new AuraHandledException('Errore durante la rimozione dei PermissionSetAssignment: ' + e.getMessage());
        }
    }

        @AuraEnabled(cacheable=true)
    public static List<String> getPermissionSetIdsByName(List<String> names) {
        List<String> ids = new List<String>();
        if (names == null || names.isEmpty()) {
            return ids;
        }
        for (PermissionSet ps : [SELECT Id, Name FROM PermissionSet WHERE Name IN :names]) {
            ids.add((String)ps.Id);
        }
        return ids;
    }

    @AuraEnabled
    public static void saveAssignments(List<PermissionSetAssignmentWrapper> assignments) {
        if (assignments == null || assignments.isEmpty()) {
            throw new AuraHandledException('La lista degli assignment è vuota.');
        }

        try {
            // Raccoglie tutti i PermissionSetId da processare
            Set<String> permissionSetIds = new Set<String>();
            for (PermissionSetAssignmentWrapper wrapper : assignments) {
                permissionSetIds.add(wrapper.PermissionSetId);
            }

            // Recupera gli assignment esistenti per l'utente
            Map<String, PermissionSetAssignment> existingAssignments = new Map<String, PermissionSetAssignment>();
            List<PermissionSetAssignment> existingRecords = [
                SELECT Id, PermissionSetId
                FROM PermissionSetAssignment
                WHERE AssigneeId = :assignments[0].AssigneeId
                AND PermissionSetId IN :permissionSetIds
            ];
            for (PermissionSetAssignment psa : existingRecords) {
                existingAssignments.put(psa.PermissionSetId, psa);
            }

            List<PermissionSetAssignment> toInsert = new List<PermissionSetAssignment>();
            List<PermissionSetAssignment> toDelete = new List<PermissionSetAssignment>();

            for (PermissionSetAssignmentWrapper wrapper : assignments) {
                Boolean isAssigned = existingAssignments.containsKey(wrapper.PermissionSetId);
                if (wrapper.Checked && !isAssigned) {
                    toInsert.add(new PermissionSetAssignment(
                        AssigneeId = wrapper.AssigneeId,
                        PermissionSetId = wrapper.PermissionSetId
                    ));
                } else if (!wrapper.Checked && isAssigned) {
                    toDelete.add(existingAssignments.get(wrapper.PermissionSetId));
                }
            }

            if (!toInsert.isEmpty()) {
                insert toInsert;
            }
            if (!toDelete.isEmpty()) {
                delete toDelete;
            }

        } catch (Exception e) {
            throw new AuraHandledException('Errore durante il salvataggio dei PermissionSetAssignment: ' + e.getMessage());
        }
    }

    public class PermissionSetAssignmentWrapper {
        @AuraEnabled public String assigneeId;
        @AuraEnabled public String permissionSetId;
        @AuraEnabled public Boolean checked;
    }

    @AuraEnabled
    public static void savePermissionSetAssignments(Id assigneeId, List<Id> permissionSetIds) {
        List<PermissionSetAssignment> psaList = new List<PermissionSetAssignment>();
        for (Id psId : permissionSetIds) {
            psaList.add(new PermissionSetAssignment(AssigneeId = assigneeId, PermissionSetId = psId));
        }
        insert psaList;
    }
}