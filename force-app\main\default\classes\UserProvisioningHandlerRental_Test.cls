@isTest
public class UserProvisioningHandlerRental_Test {
    @testSetup
    static void test_setup() {
        Test.startTest();
        PermissionSet p = new PermissionSet();
        p.Name = 'UNIT_TEST_CLASS';
        p.Label = 'UNIT_TEST_CLASS';
        insert p;
        Test.stopTest();
    }

    @isTest
    static void test_update() {
        Test.startTest();
        NetworkUser__c nu = new NetworkUser__c();
        nu.NetworkUser__c = 'TEST';
        nu.FiscalCode__c = 'TEST';
        nu.IsActive__c = true;
        insert nu;
        User u = new User();
        u.FirstName = 'Test2';
        u.LastName = 'TestLastName2';
        u.FederationIdentifier = 'TEST';
        u.ProfileId = [SELECT Id FROM Profile WHERE Name = 'Unipol Rental CS Standard User' LIMIT 1].Id;
        u.Email = '<EMAIL>';
        u.Username = 'test' + System.now().getTime() + '@test.it.invalid3.proto';
        u.Alias = 'ttst2';
        u.TimeZoneSidKey = 'Europe/Rome';
        u.EmailEncodingKey = 'UTF-8';
        u.LanguageLocaleKey = 'en_US';
        u.LocaleSidKey = 'it_IT';
        insert u;
        Map<String, String> attributes = new Map<String, String>();
        attributes.put('CF', 'TEST');
        UserProvisioningHandler uph = new UserProvisioningHandler();
        uph.updateUser(u.Id, null, null, null, 'TEST', attributes, null);
        Test.stopTest();
    }

}