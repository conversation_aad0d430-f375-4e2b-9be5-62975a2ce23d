<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>urcs_CaseEditOggetto</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
	$Setup.urcs_GeneralSettings__c.SkipValidationRule__c == false,
    NOT(ISNEW()),
    ISCHANGED(Subject),
    OR(
        AND(
            RecordType.DeveloperName == &quot;ur_CaseCRM&quot;,
            OR(
                $User.Id != UtAssegnatario__c,
                IsClosed = true
            )
        ),
        OR(
            RecordType.DeveloperName == &quot;ur_CasePQ&quot;,
            RecordType.DeveloperName == &quot;ur_CaseSitoWeb&quot;,
            RecordType.DeveloperName == &quot;ur_CaseAR&quot;,
            RecordType.DeveloperName == &quot;ur_CaseES&quot;
        )
    )
)</errorConditionFormula>
    <errorDisplayField>Subject</errorDisplayField>
    <errorMessage>Non è possibile modificare il valore questo campo.</errorMessage>
</ValidationRule>
