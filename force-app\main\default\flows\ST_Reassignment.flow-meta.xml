<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <assignments>
        <name>Add_Domain_to_Collection</name>
        <label>Add Domain to Collection</label>
        <locationX>270</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>domainCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Products.DomainType__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Products</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_Id_to_Collection</name>
        <label>Add Id to Collection</label>
        <locationX>732</locationX>
        <locationY>3620</locationY>
        <assignmentItems>
            <assignToReference>productIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Products_for_Assignment.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Product_Assignment</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Choice_to_Channel</name>
        <label>Assign Choice to Channel</label>
        <locationX>50</locationX>
        <locationY>1640</locationY>
        <assignmentItems>
            <assignToReference>assignmentChannel</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>AssignmentChannelSelection</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Assignment_Channel</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Choice_to_Channel_No_CC</name>
        <label>Assign Choice to Channel No CC</label>
        <locationX>314</locationX>
        <locationY>1640</locationY>
        <assignmentItems>
            <assignToReference>assignmentChannel</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>AssignmentChannelSelectionNoCC</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Assignment_Channel</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Group_ID</name>
        <label>Assign Group ID</label>
        <locationX>908</locationX>
        <locationY>3020</locationY>
        <assignmentItems>
            <assignToReference>SingleOwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>{!GroupDataTable.firstSelectedRow.PublicGroupId__c}  </stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ReassignmentTarget</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Group</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Group_Owner</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Owner_IDs</name>
        <label>Assign Owner IDs</label>
        <locationX>380</locationX>
        <locationY>2912</locationY>
        <assignmentItems>
            <assignToReference>SingleOwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>OwnerDataTable.firstSelectedRow.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ReassignmentTarget</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Single_Owner</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Product_to_Agent</name>
        <label>Assign Product to Agent</label>
        <locationX>864</locationX>
        <locationY>3836</locationY>
        <assignmentItems>
            <assignToReference>Loop_Products_for_Assignment.AssignedGroup__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_Products_for_Assignment.AssignedTo__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>SingleOwnerId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>templateRecordProductToUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Products_for_Assignment</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>populate_Collecttion_Product_To_Update</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Product_to_Group</name>
        <label>Assign Product to Group</label>
        <locationX>600</locationX>
        <locationY>3836</locationY>
        <assignmentItems>
            <assignToReference>Loop_Products_for_Assignment.AssignedGroup__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Retrive_Group_Custom.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_Products_for_Assignment.AssignedTo__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>templateRecordProductToUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Products_for_Assignment</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>populate_Collecttion_Product_To_Update</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Single_Opportunity_To_List</name>
        <label>Assign Single Opportunity To List</label>
        <locationX>512</locationX>
        <locationY>5252</locationY>
        <assignmentItems>
            <assignToReference>OpportunityForUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>GetOpportunityRecord</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ownerIdCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>SingleOwnerId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>SelectedOwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>SingleOwnerId</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>Disallow_Contact_Center_Assigment</name>
        <label>Disallow Contact Center Assigment</label>
        <locationX>446</locationX>
        <locationY>1082</locationY>
        <assignmentItems>
            <assignToReference>contactCenterAssignmentAllowed</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Is_Contact_Center_Allowed</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>populate_Collecttion_Product_To_Update</name>
        <label>populate Collecttion Product To Update</label>
        <locationX>732</locationX>
        <locationY>4028</locationY>
        <assignmentItems>
            <assignToReference>productCollectionTemplateRecordToUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>templateRecordProductToUpdate</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Products_for_Assignment</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Reset_Warning_Message</name>
        <label>Reset Warning Message</label>
        <locationX>512</locationX>
        <locationY>2372</locationY>
        <assignmentItems>
            <assignToReference>ShowWarningMessage</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Handle_Event_Update_SLA_Expiry_Date_Opportunity_Subflow</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Show_warning_message</name>
        <label>Show warning message</label>
        <locationX>116</locationX>
        <locationY>2912</locationY>
        <assignmentItems>
            <assignToReference>ShowWarningMessage</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>SelectNewOwner_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Show_warning_message_for_Group</name>
        <label>Show warning message for Group</label>
        <locationX>644</locationX>
        <locationY>2912</locationY>
        <assignmentItems>
            <assignToReference>ShowWarningMessage</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>SelectOwningGroup</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>Agente</name>
        <choiceText>Singolo utente</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Agente</stringValue>
        </value>
    </choices>
    <choices>
        <name>Agenzia</name>
        <choiceText>Agenzia</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Agenzia</stringValue>
        </value>
    </choices>
    <choices>
        <name>ContactCenter</name>
        <choiceText>Contact Center</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Contact Center</stringValue>
        </value>
    </choices>
    <choices>
        <name>Gruppo</name>
        <choiceText>Collaborativo</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Gruppo</stringValue>
        </value>
    </choices>
    <decisions>
        <name>Check_Action</name>
        <label>Check Action</label>
        <locationX>776</locationX>
        <locationY>2804</locationY>
        <defaultConnector>
            <targetReference>Retrive_Group_Custom</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Next</defaultConnectorLabel>
        <rules>
            <name>Return_to_Selection</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>CheckEmptyGroupDataTable</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Show_warning_message_for_Group</targetReference>
            </connector>
            <label>Return to Selection</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Assignment_Channel</name>
        <label>Check Assignment Channel</label>
        <locationX>182</locationX>
        <locationY>1832</locationY>
        <defaultConnector>
            <targetReference>Get_Group_Record_Type</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Agenzia</defaultConnectorLabel>
        <rules>
            <name>Contact_Center</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>assignmentChannel</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Contact Center</stringValue>
                </rightValue>
            </conditions>
            <label>Contact Center</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Assignment_Mode</name>
        <label>Check Assignment Mode</label>
        <locationX>512</locationX>
        <locationY>2588</locationY>
        <defaultConnector>
            <targetReference>SelectOwningGroup</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Group</defaultConnectorLabel>
        <rules>
            <name>AgentSelected</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>OwnershipMode</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Agente</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SelectNewOwner_Screen</targetReference>
            </connector>
            <label>AgentSelected</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Case_Assignment_Channel</name>
        <label>Check Case Assignment Channel</label>
        <locationX>512</locationX>
        <locationY>4328</locationY>
        <defaultConnector>
            <targetReference>Group_Engine_Reassignment_CMB</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Assign To Group</defaultConnectorLabel>
        <rules>
            <name>Assign_to_Agent</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ReassignmentTarget</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Agent</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Agent_Engine_Reassignment_CMB</targetReference>
            </connector>
            <label>Assign to Agent</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Note_Inserted</name>
        <label>Check Note Inserted</label>
        <locationX>512</locationX>
        <locationY>4736</locationY>
        <defaultConnector>
            <targetReference>Get_Event_Contact_History_Type</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Notes</defaultConnectorLabel>
        <rules>
            <name>Note_Inserted</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>noteArea</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Assignment_Note</targetReference>
            </connector>
            <label>Note Inserted</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Outcome</name>
        <label>Check Outcome</label>
        <locationX>248</locationX>
        <locationY>2804</locationY>
        <defaultConnector>
            <targetReference>Assign_Owner_IDs</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Next</defaultConnectorLabel>
        <rules>
            <name>Back_to_selection</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>CheckForEmptyDataTable</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Show_warning_message</targetReference>
            </connector>
            <label>Back to selection</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Product_Assignment</name>
        <label>Check Product Assignment</label>
        <locationX>732</locationX>
        <locationY>3728</locationY>
        <defaultConnector>
            <targetReference>Assign_Product_to_Agent</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Assign to Agent</defaultConnectorLabel>
        <rules>
            <name>Assign_to_Group</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ReassignmentTarget</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Group</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Product_to_Group</targetReference>
            </connector>
            <label>Assign to Group</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Contact_Center_Allowed</name>
        <label>Is Contact Center Allowed</label>
        <locationX>182</locationX>
        <locationY>1424</locationY>
        <defaultConnector>
            <targetReference>ChannelSelectionScreenNoCC</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Allowed</defaultConnectorLabel>
        <rules>
            <name>Is_Allowed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>contactCenterAssignmentAllowed</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ChannelSelectionScreen</targetReference>
            </connector>
            <label>Is Allowed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Contact_Center_Assignment_Allowed</name>
        <label>Is Contact Center Assignment Allowed</label>
        <locationX>358</locationX>
        <locationY>974</locationY>
        <defaultConnector>
            <targetReference>Disallow_Contact_Center_Assigment</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Allowed</defaultConnectorLabel>
        <rules>
            <name>Assignment_Allowed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Configurations.ContactCenterAssignment__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Configurations</targetReference>
            </connector>
            <label>Assignment Allowed</label>
        </rules>
    </decisions>
    <description>Fix Data e Ora SLA expiring date + Rimozione Notifica Push (Defect 1448373)</description>
    <environments>Default</environments>
    <formulas>
        <name>CheckEmptyGroupDataTable</name>
        <dataType>Boolean</dataType>
        <expression>ISBLANK({!GroupDataTable.firstSelectedRow.Name})</expression>
    </formulas>
    <formulas>
        <name>CheckForEmptyDataTable</name>
        <dataType>Boolean</dataType>
        <expression>ISBLANK({!OwnerDataTable.firstSelectedRow.FirstName})</expression>
    </formulas>
    <formulas>
        <name>CurrentUserFullname</name>
        <dataType>String</dataType>
        <expression>&apos;Assegnata da &apos; &amp; {!$User.FirstName} &amp; &apos; &apos; &amp; {!$User.LastName} &amp; &apos;;&apos;</expression>
    </formulas>
    <formulas>
        <name>GroupRefNameFormula</name>
        <dataType>String</dataType>
        <expression>&quot;R_AGE_&quot;</expression>
    </formulas>
    <formulas>
        <name>historyDescription</name>
        <dataType>String</dataType>
        <expression>&apos;Riassegnazione a &apos; &amp; {!executor}</expression>
    </formulas>
    <formulas>
        <name>Linebreakformula</name>
        <dataType>String</dataType>
        <expression>BR()</expression>
    </formulas>
    <formulas>
        <name>NewOwnerFormula</name>
        <dataType>String</dataType>
        <expression>IF(ISNULL({!Get_Owner_Info.Id}), &apos;Assegnata a &apos; &amp;  {!Get_Group_Owner_Info.PublicGroupId__c}, &apos;Assegnata a &apos; &amp; {!Get_Owner_Info.Name})</expression>
    </formulas>
    <formulas>
        <name>SlaExpiryDateFormula</name>
        <dataType>DateTime</dataType>
        <expression>IF(
    {!GetOpportunityRecord.EngagementPoint__c} = &apos;Canale digitale&apos;,
   DATETIMEVALUE(TODAY()) + (21/24) + (59/24/60)+ {!SlaRemainingDays},
    DATETIMEVALUE(TODAY()) + (21/24) + (59/24/60)+ 180
)</expression>
    </formulas>
    <interviewLabel>ST - Reassignment {!$Flow.CurrentDateTime}</interviewLabel>
    <label>ST - Reassignment</label>
    <loops>
        <name>Loop_Configurations</name>
        <label>Loop Configurations</label>
        <locationX>182</locationX>
        <locationY>866</locationY>
        <collectionReference>Get_Product_Configurations</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Is_Contact_Center_Assignment_Allowed</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Is_Contact_Center_Allowed</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Products</name>
        <label>Loop Products</label>
        <locationX>182</locationX>
        <locationY>458</locationY>
        <collectionReference>Get_Product_Records</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_Domain_to_Collection</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Product_Configurations</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Products_for_Assignment</name>
        <label>Loop Products for Assignment</label>
        <locationX>512</locationX>
        <locationY>3512</locationY>
        <collectionReference>Get_Product_Records</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_Id_to_Collection</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Product_Assignment</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Assignment_Note</name>
        <label>Create Assignment Note</label>
        <locationX>380</locationX>
        <locationY>4844</locationY>
        <connector>
            <targetReference>Get_Event_Contact_History_Type</targetReference>
        </connector>
        <inputAssignments>
            <field>Body</field>
            <value>
                <elementReference>noteArea</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ParentId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Title</field>
            <value>
                <stringValue>Riassegna in agenzia</stringValue>
            </value>
        </inputAssignments>
        <object>Note</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Reassignment_Contact_History</name>
        <label>Create Reassignment Contact History</label>
        <locationX>512</locationX>
        <locationY>5144</locationY>
        <connector>
            <targetReference>Assign_Single_Opportunity_To_List</targetReference>
        </connector>
        <inputAssignments>
            <field>Description__c</field>
            <value>
                <elementReference>historyDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Executor__c</field>
            <value>
                <elementReference>NewOwnerFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <stringValue>Trattativa assegnata</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity__c</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <object>ContactHistory__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Agents</name>
        <label>Get Agents</label>
        <locationX>512</locationX>
        <locationY>2048</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Groups</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IdAzienda__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetOpportunityRecord.Agency__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>GetOpportunityRecord.AssignedTo__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Event_Contact_History_Type</name>
        <label>Get Event Contact History Type</label>
        <locationX>512</locationX>
        <locationY>5036</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Reassignment_Contact_History</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Event</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ContactHistory__c</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Group_Owner_Info</name>
        <label>Get Group Owner Info</label>
        <locationX>908</locationX>
        <locationY>3236</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Products_for_Assignment</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Retrive_Group_Custom.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Group_Record_Type</name>
        <label>Get Group Record Type</label>
        <locationX>512</locationX>
        <locationY>1940</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Agents</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Group__c</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>UserGroup</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Groups</name>
        <label>Get Groups</label>
        <locationX>512</locationX>
        <locationY>2156</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ModeSelectionScreen</targetReference>
        </connector>
        <filterLogic>1 AND 2 AND NOT(3) AND 4</filterLogic>
        <filters>
            <field>Agenzia__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetOpportunityRecord.Agency__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>GetOpportunityRecord.AssignedGroup__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Name</field>
            <operator>StartsWith</operator>
            <value>
                <elementReference>GroupRefNameFormula</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Group_Record_Type.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Group__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Opportunity_Product_Record_Type</name>
        <label>Get Opportunity Product Record Type</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Product_Records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Prodotto</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Opportunity</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Owner_Info</name>
        <label>Get Owner Info</label>
        <locationX>380</locationX>
        <locationY>3128</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Products_for_Assignment</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>SingleOwnerId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Product_Configurations</name>
        <label>Get Product Configurations</label>
        <locationX>182</locationX>
        <locationY>758</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Configurations</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DomainType__c</field>
            <operator>In</operator>
            <value>
                <elementReference>domainCollection</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ProductConfiguration__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Product_Records</name>
        <label>Get Product Records</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Products</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Opportunity_Product_Record_Type.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Parent__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetOpportunityRecord</name>
        <label>Get Opportunity Record</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Opportunity_Product_Record_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Retrive_Group_Custom</name>
        <label>Retrive Group Custom</label>
        <locationX>908</locationX>
        <locationY>2912</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Group_ID</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>PublicGroupId__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GroupDataTable.firstSelectedRow.PublicGroupId__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Assign_Group_Owner</name>
        <label>Assign Group Owner</label>
        <locationX>908</locationX>
        <locationY>3128</locationY>
        <connector>
            <targetReference>Get_Group_Owner_Info</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>AssignedGroup__c</field>
            <value>
                <elementReference>Retrive_Group_Custom.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>AssignedTo__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>WorkingSLAExpiryDate__c</field>
            <value>
                <elementReference>SlaExpiryDateFormula</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Assign_Single_Owner</name>
        <label>Assign Single Owner</label>
        <locationX>380</locationX>
        <locationY>3020</locationY>
        <connector>
            <targetReference>Get_Owner_Info</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>AssignedGroup__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>AssignedTo__c</field>
            <value>
                <elementReference>SingleOwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WorkingSLAExpiryDate__c</field>
            <value>
                <elementReference>SlaExpiryDateFormula</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Product_Assignment</name>
        <label>Update Product Assignment</label>
        <locationX>512</locationX>
        <locationY>4220</locationY>
        <connector>
            <targetReference>Check_Case_Assignment_Channel</targetReference>
        </connector>
        <inputReference>productCollectionTemplateRecordToUpdate</inputReference>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <name>assignNoteScreen</name>
        <label>assignNoteScreen</label>
        <locationX>512</locationX>
        <locationY>4628</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Check_Note_Inserted</targetReference>
        </connector>
        <fields>
            <name>assignNoteTitle</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Riassegna&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>noteArea</name>
            <fieldText>Inserisci qui eventuali note da allegare alla trattativa</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>ChannelSelectionScreen</name>
        <label>ChannelSelectionScreen</label>
        <locationX>50</locationX>
        <locationY>1532</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Indietro</backButtonLabel>
        <connector>
            <targetReference>Assign_Choice_to_Channel</targetReference>
        </connector>
        <fields>
            <name>AssignmentChannelTitle</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Riassegna Trattativa&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;______________________________________________________________________________&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>choosehowtoreassignee</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;Scegli  se riassegnare la trattativa all&apos;Agenzia o al Contact Center&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AssignmentChannelSelection</name>
            <choiceReferences>Agenzia</choiceReferences>
            <choiceReferences>ContactCenter</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Scegli a chi assegnare la trattativa</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>ChannelSelectionScreenNoCC</name>
        <label>Riassegna Trattativa</label>
        <locationX>314</locationX>
        <locationY>1532</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Indietro</backButtonLabel>
        <connector>
            <targetReference>Assign_Choice_to_Channel_No_CC</targetReference>
        </connector>
        <fields>
            <name>Copy_1_of_AssignmentChannelTitle</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Riassegna Trattativa&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;________________________________________________________________________________________________&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Choosewhortoeassignee</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Scegli  se riassegnare la trattativa all&apos;Agenzia o al Contact Center&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AssignmentChannelSelectionNoCC</name>
            <choiceReferences>Agenzia</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Scegli a chi assegnare la trattativa</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>CCNotAllowedWarning</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(199, 0, 0);&quot;&gt;Uno o più prodotti legati a questa trattativa non permettono la riassegnazione al Contact Center. Qualora si volesse assegnare una linea di prodotto al Contact Center,è necessario separare le linee di prodotto.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>ModeSelectionScreen</name>
        <label>ModeSelectionScreen</label>
        <locationX>512</locationX>
        <locationY>2264</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>Reset_Warning_Message</targetReference>
        </connector>
        <fields>
            <name>ModeSelectionTitle</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px; color: rgb(0, 0, 0);&quot;&gt;Riassegna Trattative&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(68, 68, 68);&quot;&gt;﻿&lt;/span&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;________________________________________________________________________________________________&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>selectNewOwner</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(27, 22, 22);&quot;&gt;Seleziona il nuovo assegnatario per la trattativa&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>OwnershipMode</name>
            <choiceReferences>Gruppo</choiceReferences>
            <choiceReferences>Agente</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Scegli a chi  assegnare la trattativa?</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>SelectNewOwner_Screen</name>
        <label>Select New Owner</label>
        <locationX>248</locationX>
        <locationY>2696</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Indietro</backButtonLabel>
        <connector>
            <targetReference>Check_Outcome</targetReference>
        </connector>
        <fields>
            <name>SelectionMessage</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255); font-size: 16px;&quot;&gt;Riassegna Trattative&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;________________________________________________________________________________________________&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>selectOwner</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(37, 31, 31); font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Seleziona l&apos;utente a cui assegnare la trattativa&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>OwnerDataTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>User</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Seleziona l&apos;utente per l&apos;assegnazione</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Agents</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;FirstName&quot;,&quot;guid&quot;:&quot;column-21bb&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Nome&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;First Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;LastName&quot;,&quot;guid&quot;:&quot;column-f898&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Cognome&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Last Name&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Avanti</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>SelectOwningGroup</name>
        <label>Select Owning Group</label>
        <locationX>776</locationX>
        <locationY>2696</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Indietro</backButtonLabel>
        <connector>
            <targetReference>Check_Action</targetReference>
        </connector>
        <fields>
            <name>optName</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Riassegna Trattative&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;________________________________________________________________________________________________&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>GroupSelectionErrorMessage</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 14px; color: rgb(37, 31, 31);&quot;&gt;Seleziona il collaborativo a cui assegnare la trattativa&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>GroupDataTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Group__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Seleziona il collaborativo per l&apos;assegnazione</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Groups</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-0ee1&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Nome Collaborativo&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Group Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;&quot;,&quot;guid&quot;:&quot;column-b218&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Avanti</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetOpportunityRecord</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Agent_Engine_Reassignment_CMB</name>
        <label>Agent_Engine_Reassignment_CMB</label>
        <locationX>380</locationX>
        <locationY>4436</locationY>
        <connector>
            <targetReference>assignNoteScreen</targetReference>
        </connector>
        <flowName>Engine_Assignment_And_Reassignment_CMB</flowName>
        <inputAssignments>
            <name>AgencyId</name>
            <value>
                <elementReference>GetOpportunityRecord.Agency__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>assignToId</name>
            <value>
                <elementReference>SingleOwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Group_Engine_Reassignment_CMB</name>
        <label>Group_Engine_Reassignment_CMB</label>
        <locationX>644</locationX>
        <locationY>4436</locationY>
        <connector>
            <targetReference>assignNoteScreen</targetReference>
        </connector>
        <flowName>Engine_Assignment_And_Reassignment_CMB</flowName>
        <inputAssignments>
            <name>AgencyId</name>
            <value>
                <elementReference>GetOpportunityRecord.Agency__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>groupId</name>
            <value>
                <elementReference>Retrive_Group_Custom.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Handle_Event_Update_SLA_Expiry_Date_Opportunity_Subflow</name>
        <label>Handle Event - Update SLA Expiry Date Opportunity Subflow</label>
        <locationX>512</locationX>
        <locationY>2480</locationY>
        <connector>
            <targetReference>Check_Assignment_Mode</targetReference>
        </connector>
        <flowName>Handle_Event_Update_SLA_Expiry_Date_Opportunity_Subflow</flowName>
        <inputAssignments>
            <name>OpportunityInput</name>
            <value>
                <elementReference>GetOpportunityRecord</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>SlaRemainingDays</assignToReference>
            <name>remainingDaysSLA</name>
        </outputAssignments>
    </subflows>
    <variables>
        <name>activeContacts</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>AssignedUsersCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>assigneeCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>assignmentChannel</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>AssignTeamIds</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>AssignTeamNames</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>callMeBackCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>cipAssignmentUserIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>collectionGroupMembersId</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>GroupMember</objectType>
    </variables>
    <variables>
        <name>collectionOfUsersMemberGroup</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ConcatenatedNames</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>contactCenterAssignmentAllowed</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>true</booleanValue>
        </value>
    </variables>
    <variables>
        <name>contacts</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>currentItemFromSourceCollection</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>domainCollection</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>executor</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>FirstOpportunityId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>isBack</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>IsPreviousClicked</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>OpportunityForUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>ownerIdCollection</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>OwnerIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>productCollectionTemplateRecordToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>productIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ReassignmentTarget</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>RecipientIds</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>SelectedData</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>SelectedOwnerId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>ShowWarningMessage</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>SingleOwnerId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>SlaRemainingDays</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
        <value>
            <numberValue>60.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>templateRecordProductToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
</Flow>
