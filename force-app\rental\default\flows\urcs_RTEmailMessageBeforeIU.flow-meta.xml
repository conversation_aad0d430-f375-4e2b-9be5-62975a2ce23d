<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customErrors>
        <name>Custom_Error_Message_1</name>
        <label>Show error msg no assegnatario</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <customErrorMessages>
            <errorMessage>Per effettuare l&apos;operazione è necessario prendere in carico il case.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>Show_error_msg_stato</name>
        <label>Show error msg stato</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <customErrorMessages>
            <errorMessage>Non è possibile inviare email in stati diversi da &quot;In gestione&quot;.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>Check_mail_subject</name>
        <label>Check mail subject</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>CheckcaseInfo</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Manuale</defaultConnectorLabel>
        <rules>
            <name>Subject_email_automatica</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Subject</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>UnipolRental - Conferma apertura richiesta</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Subject</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>UnipolRental - Conferma chiusura richiesta</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Subject</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>UnipolRental - Richiesta</stringValue>
                </rightValue>
            </conditions>
            <label>Subject email automatica</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckcaseInfo</name>
        <label>Check  case Info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>no_assegnatario</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseInfo.UtAssegnatario__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>currentUserId</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Custom_Error_Message_1</targetReference>
            </connector>
            <label>no assegnatario</label>
        </rules>
        <rules>
            <name>Non_in_gestione</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseInfo.Status</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>In gestione</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Show_error_msg_stato</targetReference>
            </connector>
            <label>Non in gestione</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>currentUserId</name>
        <dataType>String</dataType>
        <expression>CASESAFEID({!$User.Id})</expression>
    </formulas>
    <formulas>
        <name>EmailMsgCreateddatePlus7Days</name>
        <dataType>DateTime</dataType>
        <expression>{!$Record.CreatedDate} +7</expression>
    </formulas>
    <interviewLabel>urcs_RTEmailMessageBeforeIU {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_RTEmailMessageBeforeIU</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetCaseInfo</name>
        <label>GetCaseInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_mail_subject</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.ParentId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetCaseInfo</targetReference>
        </connector>
        <filterFormula>AND(
    {!$Record.ParentId} != NULL,
    {!$Setup.urcs_GeneralSettings__c.SkipTrigger__c} == false,
    OR(
      {!$Record.Parent.RecordType.DeveloperName} == &apos;ur_CaseCRM&apos;,
      {!$Record.Parent.RecordType.DeveloperName} ==  &apos;ur_CaseAR&apos;,
      {!$Record.Parent.RecordType.DeveloperName}  == &apos;ur_CasePQ&apos;,
      {!$Record.Parent.RecordType.DeveloperName}  == &apos;ur_CaseSitoWeb&apos;,
      {!$Record.Parent.RecordType.DeveloperName}  == &apos;ur_CaseES&apos;
     ),
    {!$Record.Incoming} == false
)</filterFormula>
        <object>EmailMessage</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>urRTidList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
