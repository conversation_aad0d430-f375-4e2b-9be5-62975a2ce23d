@isTest
private class CC_CheckSLAExpiringCasesSchedulerTest {
    // Tries to resolve the Case RecordType 'CC_Contact_Center'; returns null if missing
    private static Id findCCRecordTypeIdIfAny() {
        List<RecordType> rts = [SELECT Id FROM RecordType WHERE SobjectType = 'Case' AND DeveloperName = 'CC_Contact_Center' LIMIT 1];
        return rts.isEmpty() ? null : rts[0].Id;
    }

    @isTest
    static void testSchedulerRunsBatch() {
        Date target = CC_CheckSLAExpiringCases.computeNextBusinessDate(Date.today());
        Id ccRtId = findCCRecordTypeIdIfAny();
        Case c = new Case(
            Subject = 'Scheduled Run',
            Origin = 'Web',
            Status = 'New',
            Expiration_Date__c = target,
            SLA_Expiration_Notice__c = false
        );
        if (ccRtId != null) c.RecordTypeId = ccRtId;
        insert c;

        Test.startTest();
        // Invoke scheduler execute directly; batch will run on Test.stopTest()
        new CC_CheckSLAExpiringCasesScheduler().execute(null);
        Test.stopTest();
    }
}
