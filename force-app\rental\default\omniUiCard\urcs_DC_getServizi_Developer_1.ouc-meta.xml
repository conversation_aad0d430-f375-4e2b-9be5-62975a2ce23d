<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Developer</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;Remote\&quot;][\&quot;data\&quot;]&quot;,&quot;ipMethod&quot;:&quot;URCS_Servizi&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;8109V00002DLkaPQAT&quot;,&quot;id&quot;:5}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>urcs_DC_getServizi</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Data Table&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:&quot;false&quot;,&quot;issortavailable&quot;:&quot;true&quot;,&quot;records&quot;:&quot;{records}&quot;,&quot;columns&quot;:[]},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;datasourceKey&quot;:&quot;state0element0&quot;,&quot;uKey&quot;:&quot;1752495960942-75&quot;,&quot;elementLabel&quot;:&quot;Data Table-0&quot;},{&quot;name&quot;:&quot;Data Table&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:false,&quot;issortavailable&quot;:false,&quot;cellLevelEdit&quot;:false,&quot;pagelimit&quot;:3,&quot;groupOrder&quot;:&quot;asc&quot;,&quot;searchDatatable&quot;:&quot;&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;data-preloadConditionalElement&quot;:false,&quot;styles&quot;:{&quot;cellMargin&quot;:[],&quot;cellPadding&quot;:[]},&quot;groupBy&quot;:&quot;ID_SERVIZIO__c&quot;,&quot;sortAcrossGroups&quot;:true,&quot;groupNameWrapperClass&quot;:&quot;&quot;,&quot;specialcharactersort&quot;:false,&quot;hideTableHeader&quot;:false,&quot;userSelectableColumn&quot;:false,&quot;hideExtraColumn&quot;:true,&quot;activeGroups&quot;:false,&quot;columns&quot;:[{&quot;fieldName&quot;:&quot;ID_SERVIZIO__c&quot;,&quot;label&quot;:&quot;Id Servizio&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:&quot;false&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;ID_CONTRATTO__c&quot;,&quot;label&quot;:&quot;Id Contratto&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:&quot;false&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;,&quot;label&quot;:&quot;Tipologia Servizio&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:&quot;false&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;DS_SERVIZIO__c&quot;,&quot;label&quot;:&quot;Descrizione Servizio&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:&quot;false&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;FL_INCLUSIONE__c&quot;,&quot;label&quot;:&quot;Inclusione&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:&quot;false&quot;,&quot;type&quot;:&quot;checkbox&quot;},{&quot;fieldName&quot;:&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;,&quot;label&quot;:&quot;Dettaglio Inclusione&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:&quot;false&quot;,&quot;type&quot;:&quot;textarea&quot;}],&quot;records&quot;:&quot;{records}&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;cellMargin&quot;:[],&quot;cellPadding&quot;:[]}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;height&quot;:&quot;&quot;},&quot;datasourceKey&quot;:&quot;state0element1&quot;,&quot;uKey&quot;:&quot;1752496304637-969&quot;,&quot;elementLabel&quot;:&quot;Data Table-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;cellMargin&quot;:[],&quot;cellPadding&quot;:[]}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;height&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[],&quot;blankCardState&quot;:false}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;Remote\&quot;][\&quot;data\&quot;]&quot;,&quot;ipMethod&quot;:&quot;URCS_Servizi&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;8109V00002DLkaPQAT&quot;,&quot;id&quot;:5}]},&quot;title&quot;:&quot;urcs_DC_getServizi&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyB0YXJnZXRzPSJsaWdodG5pbmdfX0FwcFBhZ2UiPgogICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIi8+CiAgICAgICAgICAgICAgICA8L3RhcmdldENvbmZpZz4KICAgICAgICAgICAgICAgIDx0YXJnZXRDb25maWcgdGFyZ2V0cz0ibGlnaHRuaW5nX19SZWNvcmRQYWdlIj4KICAgICAgICAgICAgICAgICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iLz4KICAgICAgICAgICAgICAgIDwvdGFyZ2V0Q29uZmlnPg==&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;apiVersion&quot;:61,&quot;description&quot;:&quot;&quot;,&quot;masterLabel&quot;:&quot;urcs_DC_getServizi&quot;},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}}]}],&quot;isRepeatable&quot;:false}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;RemoteStatus&quot;:true,&quot;Set ValuesStatus&quot;:true,&quot;Remote&quot;:{&quot;error&quot;:&quot;OK&quot;,&quot;errorCode&quot;:&quot;INVOKE-200&quot;,&quot;data&quot;:[{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:1,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi tecnici di manutenzione&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Manutenzione ordinaria&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa II. Piccole manutenzioni: Incluse&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:1,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi tecnici di manutenzione&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Manutenzione ordinaria&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Bollino blu (art.4 1. e. Revisioni e controlli): Non incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:1,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi tecnici di manutenzione&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Manutenzione ordinaria&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Revisioni (art.4 1. e. Revisioni e controlli): Incluse&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:1,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi tecnici di manutenzione&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Manutenzione ordinaria&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa I. Tagliando: Tagliando gestito da UnipolRental&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:3,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi tecnici di manutenzione&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Manutenzione straordinaria&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:4,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi di assistenza&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Ritiro e riconsegna veicolo&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Sede Legale/Operativa cliente&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:5,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi tecnici di manutenzione&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Servizio pneumatici a consumo&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Gomme termiche: Illimitate a usura&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:5,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi tecnici di manutenzione&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Servizio pneumatici a consumo&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Gomme ordinarie: Illimitate a usura&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:5,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi tecnici di manutenzione&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Servizio pneumatici a consumo&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Cerchio maggiorato: No&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:7,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi di assistenza&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Soccorso e recupero&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Incluso Italia: Traino, soccorso e sostitutiva immediata cat 1.2&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:7,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi di assistenza&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Soccorso e recupero&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Incluso Estero: Solo traino e soccorso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:15,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi di assistenza&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Veicolo sostitutivo&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Incluso B. Definitivo: Non incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:15,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi di assistenza&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Veicolo sostitutivo&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Incluso A. Temporaneo: Categoria equivalente max 2.5 cc - dopo 8 ore&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:19,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi assicurativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Polizza RCA&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Penale: nessuna&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:19,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi assicurativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Polizza RCA&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Massimale: 10mln €&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:20,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi assicurativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Limitazione responsabilità incendio e furto&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Penale: Nessuna&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:21,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi assicurativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Kasko&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Penale: nessuna&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:24,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Tassa di proprietà&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Esclusa con Servizio di pagamento tassa di proprietà Incluso. UnipolRental provvederà al pagamento della tassa di proprietà in nome e per conto del Cliente con addebito del relativo importo&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:25,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Autorizzazione alla circolazione&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Persone autorizzate alla guida del veicolo oltre all&apos;utilizzatore/assegnatario: dipendenti e collaboratori autorizzati dal cliente&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:25,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Autorizzazione alla circolazione&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Documentazione per richiesta permessi e/o autorizzazioni: A pagamento&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:26,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Servizio carburante&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:27,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Gestione multe&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Respinte agli organi competenti Commissione: € 0&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:40,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi di assistenza&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Consulenza e assistenza&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Gestione sinistri: Inclusa&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:40,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi di assistenza&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Consulenza e assistenza&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Presa e consegna per manutenzione ordinaria: Non inclusa&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:40,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi di assistenza&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Consulenza e assistenza&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Manuale operativo personalizzato: Incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:40,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi di assistenza&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Consulenza e assistenza&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Gestione attività con Referente o Utilizzatore: Inclusa&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:40,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi di assistenza&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Consulenza e assistenza&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Convenzioni Officine di riparazione e assistenza: Incluse&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:41,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi Policy&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Report&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:46,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Oneri finanziari&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;6. GARANZIE DI TERZI: Non previste&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:46,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Oneri finanziari&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;1. FATTURAZIONE MENSILE: Anticipata&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:46,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Oneri finanziari&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;5. PAGAMENTI: Modalità di pagamento: Pagamento tramite Ri.Ba.&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:46,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Oneri finanziari&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;4. CORRISPETTIVI ANTICIPATI: Nessuna mensilità&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:46,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Oneri finanziari&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;5. PAGAMENTI: Termini di pagamento: 60 gg df fm&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:47,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi Policy&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Inplant&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:48,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi Policy&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Sito&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;www.unipolrental.it Questo è il suo codice da utilizzare per la registrazione alla nostra area riservata http://www.unipolrental.link/areaclienti:        ##CODCLIENTE##&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:50,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi Policy&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Vendita usato&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:100,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi assicurativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Cristalli&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusi Penale: Nessuna penale&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:101,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi assicurativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;PAI&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non richiesta&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:102,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi assicurativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Eventi atmosferici&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Penale: Nessuna&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:103,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi assicurativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Atti vandalici&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa Penale: Nessuna penale&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:104,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi assicurativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Diaria ritiro patente&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non richiesta&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:105,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Documentazione per l&apos;espatrio&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non inclusa&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:113,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Maxi Rata&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non inclusa&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:114,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi tecnici di manutenzione&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Servizio pneumatici a numero&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non inclusa&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:115,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi tecnici di manutenzione&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Riparazione conseguente a sinistro&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusa&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:116,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi di assistenza&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Veicolo in anticipo&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:117,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi Policy&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Referente flotta&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non inclusa&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:118,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi assicurativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Furto parziale&quot;,&quot;FL_INCLUSIONE__c&quot;:true,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Inclusi Penale: Nessuna&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:120,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi assicurativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Salvaguardia parziale corrispettivi (art.10)&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:121,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi Policy&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Dispositivi satellitari&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:127,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi Policy&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Unibox&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:128,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi amministrativi&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Energy Card&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:129,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi di assistenza&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Roomy car&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:130,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi Policy&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Polizza assistenza impianti&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non incluso&quot;},{&quot;ID_CONTRATTO__c&quot;:1238817,&quot;ID_SERVIZIO__c&quot;:131,&quot;DS_TIPOLOGIA_SERVIZIO__c&quot;:&quot;Servizi Policy&quot;,&quot;DS_SERVIZIO__c&quot;:&quot;Servizio di Ricarica Domestica - WallBox&quot;,&quot;FL_INCLUSIONE__c&quot;:false,&quot;DS_DETTAGLIO_INCLUSIONE__c&quot;:&quot;Non incluso&quot;}]},&quot;Set Values&quot;:{&quot;methodExecute&quot;:&quot;init&quot;,&quot;recordId&quot;:&quot;8109V00002DLkaPQAT\n&quot;},&quot;options&quot;:{&quot;forceQueueable&quot;:false,&quot;mockHttpResponse&quot;:null,&quot;vlcApexResponse&quot;:true,&quot;useFuture&quot;:false,&quot;isTestProcedure&quot;:false,&quot;resetCache&quot;:false,&quot;integrationProcedureKey&quot;:null,&quot;vlcIPData&quot;:null,&quot;OmniAnalyticsTrackingDebug&quot;:false,&quot;ignoreCache&quot;:false,&quot;shouldCommit&quot;:false,&quot;vlcTestSuiteUniqueKey&quot;:null,&quot;vlcTestUniqueKey&quot;:null,&quot;vlcCacheKey&quot;:null,&quot;continuationStepResult&quot;:null,&quot;vlcFilesMap&quot;:null,&quot;ParentInteractionToken&quot;:null,&quot;useQueueable&quot;:false,&quot;disableMetadataCache&quot;:false,&quot;isDebug&quot;:false,&quot;queueableChainable&quot;:false,&quot;useContinuation&quot;:false,&quot;chainable&quot;:false,&quot;ignoreMetadataPermissions&quot;:false,&quot;useHttpCalloutMock&quot;:false,&quot;useQueueableApexRemoting&quot;:false},&quot;response&quot;:{},&quot;ResponseStatus&quot;:true,&quot;recordId&quot;:&quot;8109V00002DLkaPQAT&quot;}</sampleDataSourceResponse>
    <stylingConfiguration>{}</stylingConfiguration>
    <versionNumber>1</versionNumber>
</OmniUiCard>
