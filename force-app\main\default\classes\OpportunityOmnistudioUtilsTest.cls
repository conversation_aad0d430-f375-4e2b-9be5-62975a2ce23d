@isTest
private class OpportunityOmnistudioUtilsTest {

    @isTest
    static void testMetodoInesistente(){
        Map<String, Object> args = new Map<String, Object>{
            'input' => new Map<String, Object>{},
            'output' => new Map<String, Object>(),
            'options' => new Map<String, Object>()
        };
        
        Object result = new OpportunityOmnistudioUtils().call('test', args);
    }

    @isTest
    static void testCheckProdottiRecapitiUnisalute(){

        String recordTypeOppProdottoId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId();
        String recordTypeOppOmnicanaleId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Omnicanale').getRecordTypeId();
        
        Test.startTest();
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        User owner = [SELECT Id FROM User WHERE Profile.Name = 'Standard User' LIMIT 1];

        Opportunity opp1 = new Opportunity(
            Name = 'Test Opp 1',
            StageName = 'Assegnato',
            CloseDate = Date.today().addDays(10),
            AccountId = acc.Id,
            OwnerId = owner.Id,
            Amount = 1000,
            Rating__c = 'Calda',
            AreaOfNeed__c = 'Casa',
            HasCallMeBack__c = true,
            WorkingSLAExpiryDate__c = Date.today().addDays(5),
            RecordTypeId = recordTypeOppOmnicanaleId
        );

        insert opp1;

        Opportunity oppChild1 = new Opportunity(
            Name = 'Test Opp 1',
            StageName = 'Assegnato',
            CloseDate = Date.today().addDays(10),
            AccountId = acc.Id,
            OwnerId = owner.Id,
            Amount = 1000,
            Rating__c = 'Calda',
            AreaOfNeed__c = 'Casa',
            HasCallMeBack__c = true,
            Parent__c = opp1.Id,
            WorkingSLAExpiryDate__c = Date.today().addDays(5),
            RecordTypeId = recordTypeOppProdottoId
        );

        Opportunity oppChild2 = new Opportunity(
            Name = 'Test Opp 1',
            StageName = 'Assegnato',
            CloseDate = Date.today().addDays(10),
            AccountId = acc.Id,
            OwnerId = owner.Id,
            Amount = 1000,
            Rating__c = 'Calda',
            AreaOfNeed__c = 'Casa',
            HasCallMeBack__c = true,
            Parent__c = opp1.Id,
            WorkingSLAExpiryDate__c = Date.today().addDays(5),
            RecordTypeId = recordTypeOppProdottoId
        );

        insert new List<Opportunity>{oppChild1, oppChild2};

        Quote quote1 = new Quote(
            Name = 'Test Quote',
            OpportunityId = oppChild1.Id
        );

        Quote quote2 = new Quote(
            Name = 'Test Quote',
            OpportunityId = oppChild2.Id
        );

        insert new List<Quote>{quote1, quote2};
   		
        //NO accountAccountRelation
        Map<String, Object> args = new Map<String, Object>{
            'input' => new Map<String, Object>{'actualOppId' => oppChild1.Id, 'parentOppId' => opp1.Id},
            'output' => new Map<String, Object>(),
            'options' => new Map<String, Object>()
        };
        
            try{
                Object result = new OpportunityOmnistudioUtils().call('checkProdottiRecapitiUnisalute', args);
            }catch(Exception ex){}
        
        Test.stopTest();
    }
}