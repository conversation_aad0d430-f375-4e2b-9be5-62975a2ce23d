------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
This manual procedure aims to configure EmailtoCase mailboxes for Production.
The mailboxes are grouped by queue.
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------
 
1. Login to Salesforce
2. Open Setup - Search for "Email-to-Case"

===============================================================================
SECTION A: FleetAccountCustCare Queue (40 mailboxes)
===============================================================================

3. For each of the following 40 email addresses, create a new Email-to-Case configuration executing the following steps:
    
   - Open Setup - Search for "Email-to-Case"
   - Click "New" and configure:
   - Routing Name: <ROUTING_NAME>
   - Email Address: <EMAIL_ADDRESS>
   - Controlled by Permission Set: Checked
   - Case Owner: Queue -> FleetAccountCustCare
   - Case Priority: Medium
   - Case Origin: Email Service
   - Case Record Type: ur_CaseES
   - Save.
   - Request the mailbox owner to complete verification process.

    EMAIL_ADDRESS                                 ROUTING_NAME
    <EMAIL>	      SIC gestione operativa
    <EMAIL>	      PWC gestione operativa
    <EMAIL>	  SCHINDLER gestione operativa
    <EMAIL>  ENGINEERING gestione operativa
    <EMAIL>	  Roma Gestione Operativa Sirti
    <EMAIL>	              CAMST Servizi
    <EMAIL>	      gestione operativa BBPM
    <EMAIL>	      CREDEM gestione operativa
    <EMAIL>	      UNIPOL gestione operativa
    <EMAIL>	      IMA gestione operativa
    <EMAIL>	  Granarolo gestione operativa
    <EMAIL>	      Sacmi gestione operativa
    <EMAIL>	      HENKEL gestione operativa
    <EMAIL>	      TSG gestione operativa
    <EMAIL>	CA        gestione operativa
    <EMAIL>	      ALTHEA gestione operativa
    <EMAIL>	  Alperia gestione operativa
    <EMAIL>	      gestione operativaa USLBO
    <EMAIL>	      gestione operativa BCC
    <EMAIL>	  gestione operativa CIRFOOD
    <EMAIL>	      gestione operativa UNIPOL
    <EMAIL>	      ENG gestione operativa
    <EMAIL>  Milano Gestione Operativa Sirti
    <EMAIL>  Gruppo Gavio gestione operativa
    <EMAIL>	      BPER gestione operativa
    <EMAIL>	          SI gestione operativa
    <EMAIL>	  inter pump gestione operativa
    <EMAIL>	  Siemens gestione operativa
    <EMAIL>	      Madilo gestione operativa
    <EMAIL>	  Dussmann gestione operativa
    <EMAIL>	      Il Sole gestione operativa
    <EMAIL>  coopservice gestione operativa
    <EMAIL>	      natana gestione operativa
    <EMAIL>	  vorwerk gestione operativa
    <EMAIL>	      circet gestione operativa
    <EMAIL>	      rcs gestione operativa
    <EMAIL>	      coop gestione operativa
    <EMAIL>	      e80 gestione operativa
    <EMAIL>	      hdl gestione operativa
    <EMAIL>	  recordati gestione operativa


===============================================================================
SECTION B: BandieGare Queue (9 mailboxes)
===============================================================================


6. For each of the following 9 email addresses, create a new Email-to-Case configuration executing the following steps like above:
    
    (All same settings as above)
    
   - Case Owner: Queue -> BandieGare

    EMAIL_ADDRESS                                   ROUTING_NAME
    <EMAIL>	Acque Veronesi gestione operativa
    <EMAIL>	Regione Lazio gestione operativa
    <EMAIL>	                        POSTE 
    <EMAIL>	                    Gruppo Iren
    <EMAIL>	    Nuove Acque gestioneoperativa
    <EMAIL>	                enel operativo
    <EMAIL>	                enel promiscuo
    <EMAIL>	                    enienjoy
    <EMAIL>	                    gruppo acea


===============================================================================
SECTION C: Individual Queue Mailboxes (4 mailboxes)
===============================================================================

7. Configure the following individual mailboxes:

   7.1 UffMulte Queue:
       - Email: <EMAIL>
       - Routing Name: <EMAIL>
       - Case Owner: Queue -> UffMulte
       - (Other settings same as above)

   7.2 CarteCarburante Queue:
       - Email: <EMAIL>
       - Routing Name: <EMAIL>
       - Case Owner: Queue -> CarteCarburante
       - (Other settings same as above)

   7.3 Amministrazione_Art94 Queue:
       - Email: <EMAIL>
       - Routing Name: <EMAIL>
       - Case Owner: Queue -> Amministrazione_Art94
       - (Other settings same as above)

   7.4 GestioneBollo Queue:
       - Email: <EMAIL>
       - Routing Name: <EMAIL>
       - Case Owner: Queue -> GestioneBollo
       - (Other settings same as above)

===============================================================================
SECTION D: Permission Set Configuration
===============================================================================

8. Open Setup 
   - Search for "permission Set" 
   - Search for: "Unipol Rental CS Manage Case" and select 
   - Find setting for: "Routing" and select "Email-to-Case Routing Address Access"  
   - Click edit and add ALL 53 configured email addresses to Selected Email-to-Case Routing Addresses
   - Save

===============================================================================
VERIFICATION STEPS
===============================================================================

9. Verify all 53 Email-to-Case configurations are created and active
10. Confirm all mailbox owners have completed verification process

===============================================================================
NOTES
===============================================================================

- Total mailboxes configured: 53
- FleetAccountCustCare: 40 mailboxes
- BandieGare: 9 mailboxes  
- Individual queues: 4 mailboxes
- All configurations use ur_CaseES record type and Medium priority
- All are controlled by Permission Set for security
