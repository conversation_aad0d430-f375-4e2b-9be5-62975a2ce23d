@isTest
public with sharing class VoiceCallTest {
    @TestSetup
    static void makeData(){
        //CallCenter cc = [SELECT Id FROM CallCenter WHERE Name = 'UnipolDEV1ContactCenter'];
        VoiceCall vc = new VoiceCall();
        vc.CallType = 'Inbound';
        vc.OwnerId = UserInfo.getUserId();
        //vc.CallcenterId = cc.Id;
        vc.CallStartDateTime = System.now();
        vc.CallEndDateTime = System.now(); 
        vc.FromPhoneNumber= 'Yes'; 
        vc.ToPhoneNumber = '3333333334';
        vc.CallType = 'Callback';
        try{ insert vc; }catch(Exception ex){}
    }

    @isTest
    static void voiceCallInsert(){
        //CallCenter cc = [SELECT Id FROM CallCenter WHERE Name = 'UnipolDEV1ContactCenter'];
        VoiceCall vc = new VoiceCall();
        vc.CallType = 'Inbound';
        vc.OwnerId = UserInfo.getUserId();
        //vc.CallcenterId = cc.Id;
        vc.CallStartDateTime = System.now();
        vc.CallEndDateTime = System.now(); 
        vc.FromPhoneNumber= 'Yes'; 
        vc.ToPhoneNumber = '3333333333';
        vc.CallType = 'Callback';
        try{ insert vc; }catch(Exception ex){}
    }

    @isTest
    static void voiceCallUpdate(){
        try{
            VoiceCall vc = [SELECT Id FROM VoiceCall LIMIT 1];
        	vc.Target__c = 'Vendita';
        	update vc; 
        }catch(Exception ex){}
    }
}