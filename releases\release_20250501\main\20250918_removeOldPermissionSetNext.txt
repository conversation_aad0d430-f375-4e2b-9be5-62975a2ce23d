------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
This manual procedure deals with removing old Next permission sets due to LDAP 
group name typos. 
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------
 

1. Login as System Admin to Salesforce
2. Click on the settings icon at the top right and go to the setup item
3. Search for Permission Set in Quick Search and select it
4. Search for Permission Set "APDACONS" and click it
5. Click on "Manage Assignments". If there are assignement, click on the checkbox to select all assignment 
and click on the trash icon to "Delete Assignemnt"
6. Go back on Permission Set List View and search for "APDACONS", than click on delete to delete the Permission Set

Repeat from 4 to 6 for APDAASS,APDAPORT,APPREV,APGEST,APASSAGE,APALL,AADACONS,AADAASS,AADAPORT,AAPREV,AAGEST,AAASSAGE,AAALL

7. Go to Setup, Search for Custom Metadata
8. Search for UCA Mapping and click Manage Record
9. Search for "UNISALUTENEXT_0019" and delete the record.

Repeat step 9 for UNISALUTENEXT_0020, UNISALUTENEXT_0021, UNISALUTENEXT_0022, UNISALUTENEXT_0023, UNISALUTENEXT_0024,
UNISALUTENEXT_0025, UNISALUTENEXT_0026