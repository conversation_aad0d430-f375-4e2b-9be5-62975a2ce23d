/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 09-17-2025
 * @last modified by  : <EMAIL>
**/
@IsTest
private class CC_CallDeleteCaseActivityRipianificaTest {

    @IsTest
    static void test_CampaignVuota() {
        CC_CallDeleteCaseActivityRipianifica.Request req = new CC_CallDeleteCaseActivityRipianifica.Request();
        req.campaign = null;
        req.listId = 'LIST123';
        req.phone = '+391234567890';

        List<CC_CallDeleteCaseActivityRipianifica.Response> results =
            CC_CallDeleteCaseActivityRipianifica.removeContactFromFlow(new List<CC_CallDeleteCaseActivityRipianifica.Request>{req});

        System.assertEquals(false, results[0].success);
        System.assertEquals('Campo campaign vuoto', results[0].message);
    }

    @IsTest
    static void test_ListIdVuota() {
        CC_CallDeleteCaseActivityRipianifica.Request req = new CC_CallDeleteCaseActivityRipianifica.Request();
        req.campaign = 'CAMP123';
        req.listId = '';
        req.phone = '+391234567890';

        List<CC_CallDeleteCaseActivityRipianifica.Response> results =
            CC_CallDeleteCaseActivityRipianifica.removeContactFromFlow(new List<CC_CallDeleteCaseActivityRipianifica.Request>{req});

        System.assertEquals(false, results[0].success);
        System.assertEquals('Campo listId vuoto', results[0].message);
    }

    @IsTest
    static void test_NumeroNonValido() {
        CC_CallDeleteCaseActivityRipianifica.Request req = new CC_CallDeleteCaseActivityRipianifica.Request();
        req.campaign = 'CAMP123';
        req.listId = 'LIST123';
        req.phone = '1234567890'; // non inizia con +39

        List<CC_CallDeleteCaseActivityRipianifica.Response> results =
            CC_CallDeleteCaseActivityRipianifica.removeContactFromFlow(new List<CC_CallDeleteCaseActivityRipianifica.Request>{req});

        System.assertEquals(false, results[0].success);
        System.assert(results[0].message.contains('Numero non valido'));
    }

    @IsTest
    static void test_RichiestaCorretta() {
        CC_CallDeleteCaseActivityRipianifica.Request req = new CC_CallDeleteCaseActivityRipianifica.Request();
        req.campaign = 'CAMP123';
        req.listId = 'LIST123';
        req.phone = '+391234567890';

        List<CC_CallDeleteCaseActivityRipianifica.Response> results =
            CC_CallDeleteCaseActivityRipianifica.removeContactFromFlow(new List<CC_CallDeleteCaseActivityRipianifica.Request>{req});

    }
}