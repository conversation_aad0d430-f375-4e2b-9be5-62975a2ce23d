<template>
   <table class="slds-table">
    <thead style="border: 2px solid #cdcdcd;">
             <tr style="font-size: 18px; padding-top: 13px; padding-left: 13px;">
                 <th scope="col">Permessi <p style="padding: 5px;"><span style="font-size: 14px; margin-bottom: 5px; font-weight: 500;">{selectedCount} elementi di {localRecords.length}</span></p></th>
                 <th scope="col">
                     <div style="display: flex; align-items: center; justify-content: flex-end;">                         
                         <input type="text" placeholder="Cerca permesso..." value={searchTerm} oninput={handleSearch} style="margin-left: 20px; padding: 4px; font-size: 14px; width: 220px;" />
                     </div>
                 </th>
             </tr>
    </thead>



    <tbody>
       <template for:each={filteredRecords} for:item="record">
          <!-- <tr style="border: 2px solid #cdcdcd;" key={record.Id}> -->
            <tr style="border: 2px solid #cdcdcd;" key={record.PermissionSet}>
             <td style="padding: 2px 2px 2px 10px  !important;">
                <!-- <lightning-input
                   type="checkbox"
                   checked={record.Checked}
                   onchange={handleChange}
                   label={record.Label}
                   data-id={record.Id}>
                </lightning-input> -->
                <lightning-input
                   type="checkbox"
                   checked={record.Checked}
                   onchange={handleCheckboxChange} 
                   label={record.Label}
                   value={record.PermissionSet}
                   data-id={record.Id}>
                </lightning-input>
             </td>
          </tr>
       </template>
    </tbody>
 </table>

<div class="button-group" style="display: flex; justify-content: space-between; margin-top: 1.5rem;">
    <button class="slds-button slds-button_neutral" style="padding: 0.5rem 1.5rem; font-size: 1rem;" onclick={handleAnnulla}>Annulla</button>
    <button class="slds-button slds-button_brand" style="padding: 0.5rem 1.5rem; font-size: 1rem;" onclick={handleConferma}>Conferma</button>
</div>


  <!-- ...elenco PermissionSet con checkbox... -->
    <template if:true={showModal}>
        <section role="dialog" tabindex="-1" aria-modal="true" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container" style="max-width: 420px; min-width: 320px; width: 100%;">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">Esito Salvataggio</h2>
                </header>
                <div class="slds-modal__content">
                    <p>{modalMessage}</p>
                </div>
                <footer class="slds-modal__footer">
                    <button class="slds-button slds-button_brand" onclick={handleModalOk}>OK</button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
 
</template>