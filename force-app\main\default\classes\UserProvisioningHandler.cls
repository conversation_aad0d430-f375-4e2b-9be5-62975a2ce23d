/*
* @description : Handler class for UserProvisioning
* @cicd_tests UserProvisioningHandler_Test
*/
global class UserProvisioningHandler implements Auth.SamlJitHandler {
    
    private class JitException extends Exception{}

    /*
    * @description : This method is called to create a User
    * @param Id samlSsoProviderId Id of the SSO configuration
    * @param Id communityId Id of the community, not used
    * @param Id portalId Id of the portal, not used
    */
    global User createUser(Id samlSsoProviderId, Id communityId, Id portalId,
        String federationIdentifier, Map<String, String> attributes, String assertion) {
        User u = new User();
            /* u.FirstName = 'Test2';
            u.LastName = 'TestLastName2';
            u.FederationIdentifier = federationIdentifier;
            u.ProfileId = [SELECT Id FROM Profile WHERE Name = 'System Administrator' LIMIT 1].Id;
			u.Email = '<EMAIL>';
            u.Username = '<EMAIL>';
            u.Alias = 'ttst2';
            u.TimeZoneSidKey = 'Europe/Rome';
            u.EmailEncodingKey = 'UTF-8';
            u.LanguageLocaleKey = 'en_US';
            u.LocaleSidKey = 'it_IT'; */

        UserProvisioningCreate.handleUser(u, attributes);

        return u;
    }

    /*
    * @description : This method is called to update a User
    * @param Id usersId Id of the User logging in to Salesforce
    * @param Id samlSsoProviderId Id of the SSO configuration
    * @param Id communityId Id of the community, not used
    * @param Id portalId Id of the portal, not used
    * @param String federationIdentifier String with the federation identifier used as key in SSO
    * @param Map<String, String> attributes Map Key-->Value saml request attriute --> value of the attribute
    * @param String assertion saml assertion
    */
    global void updateUser(Id userId, Id samlSsoProviderId, Id communityId, Id portalId, String federationIdentifier, Map<String, String> attributes, String assertion) {
        User u = [SELECT Id, FederationIdentifier, Profile.Name, Email FROM User WHERE Id = :userId];
        //handleJit(false, u, samlSsoProviderId, communityId, portalId, federationIdentifier, attributes, assertion);
        
        if('Unipol Rental CS Standard User'.equalsIgnoreCase(u.Profile.Name)){
            UserProvisioningUpdateRental.handleUser(u, attributes);
        }else{
	        UserProvisioningUpdate.handleUser(u, attributes);
        }
    }
}