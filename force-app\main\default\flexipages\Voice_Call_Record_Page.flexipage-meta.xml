<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>force:detailPanel</componentName>
                <identifier>force_detailPanel</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>detailTabContent</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>forceChatter:recordFeedContainer</componentName>
                <identifier>forceChatter_recordFeedContainer</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>feedTabContent</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListContainer</componentName>
                <identifier>force_relatedListContainer</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>relatedTabContent</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>detailTabContent</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>detailTab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>feedTabContent</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.feed</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>feedTab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>relatedTabContent</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.relatedLists</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>relatedListsTab</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>mainTabs</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>FeedItem.TextPost</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Global.FinServ__NewEventAdvisor</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Global.FinServ__NewTaskAdvisor</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Global.FinServ__LogACallAdvisor</value>
                        </valueListItems>
                        <valueListItems>
                            <value>FeedItem.ContentPost</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Global.NewContact</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Global.NewOpportunity</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Global.NewLead</value>
                        </valueListItems>
                        <valueListItems>
                            <value>FeedItem.PollPost</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Global.FinServ__NewLeadReferral</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Edit</value>
                        </valueListItems>
                        <valueListItems>
                            <value>ChangeRecordType</value>
                        </valueListItems>
                        <valueListItems>
                            <value>PrintableView</value>
                        </valueListItems>
                        <valueListItems>
                            <value>ShareVoiceCall</value>
                        </valueListItems>
                        <valueListItems>
                            <value>AddToCollection</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Delete</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CreateIncident</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>runtime_conversation:mediaPlayer</componentName>
                <identifier>runtime_conversation_mediaPlayer</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>mainTabs</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <!-- <itemInstances>
            <componentInstance>
                <componentName>opencti:acwTimer</componentName>
                <identifier>opencti_acwTimer</identifier>
            </componentInstance>
        </itemInstances> -->
        <mode>Replace</mode>
        <name>rightsidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <!-- <itemInstances>
            <componentInstance>
                <componentName>opencti:recordPhoneCallControls</componentName>
                <identifier>opencti_recordPhoneCallControls</identifier>
            </componentInstance>
        </itemInstances> -->
        <itemInstances>
            <componentInstance>
                <componentName>lbpm:actionList</componentName>
                <identifier>lbpm_guidedactionlist</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>lookupFieldName</name>
                    <value>Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>titleFieldName</name>
                    <value>Call Notes</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>updateQuickActionName</name>
                    <value>VoiceCall._LightningCallNotes</value>
                </componentInstanceProperties>
                <componentName>console:relatedRecord</componentName>
                <identifier>console_relatedRecord</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>hideAudioNoteRecorderButton</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideConversationHistory</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideEmojiButton</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideFilePickerButton</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideQuickTextButton</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideRichContentPickerButton</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideTransferButton</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideWhisperFlagButton</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>newLineOnEnter</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>scrt:conversationBody</componentName>
                <identifier>scrt_conversationBody</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RelatedCase__r.Status}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Chiuso</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>ccOpenRelatedSubtab</componentName>
                <identifier>c_ccOpenRelatedSubtab</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>leftsidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Voice Call Record Page</masterLabel>
    <parentFlexiPage>support__VoiceCall_rec_L_3col</parentFlexiPage>
    <sobjectType>VoiceCall</sobjectType>
    <template>
        <name>flexipage:recordHomePinnedLeftSidebarTemplateDesktop</name>
    </template>
    <type>RecordPage</type>
</FlexiPage>
