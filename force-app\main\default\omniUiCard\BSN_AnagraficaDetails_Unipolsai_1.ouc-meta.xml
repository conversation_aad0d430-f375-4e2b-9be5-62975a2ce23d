<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>AnagraficaDetails/Unipolsai/1.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_GetData&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;UserId&quot;:&quot;{User.userId}&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;0059Q00000PIMo2QAH&quot;,&quot;id&quot;:11},{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;a1i9Q00000cvAZRQA2&quot;,&quot;id&quot;:14}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <name>BSN_AnagraficaDetails</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;BSN_DA_Dati_Anagrafici&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;{record.datiAnagrafici}&quot;,&quot;selectedState&quot;:&quot;Active&quot;,&quot;isChildCardTrackingEnabled&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;FlexCard-0&quot;},{&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;DA_Dati_Agenzia&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;{record.datiAgenzia}&quot;,&quot;selectedState&quot;:&quot;Active&quot;,&quot;isChildCardTrackingEnabled&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;FlexCard-1&quot;},{&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;DA_Recapiti&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;{record.recapiti}&quot;,&quot;selectedState&quot;:&quot;Active&quot;,&quot;isChildCardTrackingEnabled&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;FlexCard-2&quot;},{&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;DA_InformazioniSocietarie&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;{record.infoSocietarie}&quot;,&quot;selectedState&quot;:&quot;Active&quot;,&quot;isChildCardTrackingEnabled&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;FlexCard-3&quot;}]}},&quot;childCards&quot;:[&quot;BSN_DA_Dati_Anagrafici&quot;,&quot;DA_Dati_Agenzia&quot;,&quot;DA_Recapiti&quot;,&quot;DA_InformazioniSocietarie&quot;],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_GetData&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;UserId&quot;:&quot;{User.userId}&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;0059Q00000PIMo2QAH&quot;,&quot;id&quot;:11},{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;a1i9Q00000cvAZRQA2&quot;,&quot;id&quot;:14}]},&quot;title&quot;:&quot;BSN_AnagraficaDetails&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyB0YXJnZXRzPSJsaWdodG5pbmdfX1JlY29yZFBhZ2UiPgogICAgICAgICAgICAgICAgICAgICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iLz4KICAgICAgICAgICAgICAgICAgICAgICAgPHByb3BlcnR5IG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciLz4KICAgICAgICAgICAgICAgICAgICAgIDwvdGFyZ2V0Q29uZmlnPjx0YXJnZXRDb25maWcgdGFyZ2V0cz0ibGlnaHRuaW5nX19BcHBQYWdlIj4KICAgICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICAgICAgICAgICAgICAgICAgPHByb3BlcnR5IG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciLz4KICAgICAgICAgICAgICAgICAgICA8L3RhcmdldENvbmZpZz4=&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;masterLabel&quot;:&quot;BSN_AnagraficaDetails&quot;},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]}],&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfBSN_AnagraficaDetails_2_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9Q000002kZUfSAM&quot;,&quot;MasterLabel&quot;:&quot;cfBSN_AnagraficaDetails_2_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;}}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;recapiti&quot;:{&quot;RecAgenziaCellPersonale&quot;:&quot;+40 3205566777&quot;,&quot;RecAgenziaEmailPers&quot;:&quot;<EMAIL>&quot;},&quot;datiAnagrafici&quot;:{&quot;TipoPersonaGiuridica&quot;:&quot;N/D&quot;,&quot;modifica&quot;:{&quot;DisableCompany&quot;:true,&quot;Propaga&quot;:false},&quot;Originator&quot;:&quot;Sistema ANAG 1&quot;,&quot;SedeLegale&quot;:&quot;, , US&quot;,&quot;ReferenteAziendale&quot;:&quot;N/D&quot;,&quot;ClienteInPerimetro&quot;:&quot;No&quot;,&quot;FormaSoocietaria&quot;:&quot;Persona Giur L1&quot;,&quot;AnzianitaRelAltraAgenzia&quot;:&quot;12/10/2022&quot;,&quot;GestoreAnagrafica&quot;:&quot;COD 23456&quot;,&quot;DataCostituzione&quot;:&quot;N/D&quot;,&quot;PartitaIva&quot;:&quot;PT234567890&quot;,&quot;StatoSoggettoPerAzienda&quot;:&quot;Cliente&quot;,&quot;RagioneSociale&quot;:&quot;Azienda IT&quot;,&quot;STatoPartitaIVA&quot;:&quot;N/D&quot;,&quot;AnzianitaRelazione&quot;:&quot;22/04/2024&quot;},&quot;datiAgenzia&quot;:{&quot;StatoCliente&quot;:&quot;Cliente&quot;,&quot;AdesioneCauzione&quot;:&quot;No&quot;}}</sampleDataSourceResponse>
    <versionNumber>1</versionNumber>
</OmniUiCard>
