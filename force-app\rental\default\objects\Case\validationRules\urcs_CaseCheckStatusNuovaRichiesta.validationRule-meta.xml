<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>urcs_CaseCheckStatusNuovaRichiesta</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
		$Setup.urcs_GeneralSettings__c.SkipValidationRule__c == false,
    ISCHANGED(Status),
    ISPICKVAL(PRIORVALUE(Status),&apos;Nuova richiesta&apos;),
				OR(
        RecordType.DeveloperName == &apos;ur_CaseCRM&apos;,
        RecordType.DeveloperName == &apos;ur_CasePQ&apos;,
        RecordType.DeveloperName == &apos;ur_CaseSitoWeb&apos;,
        RecordType.DeveloperName == &apos;ur_CaseAR&apos;,
				  	 RecordType.DeveloperName == &apos;ur_CaseES&apos;
    ),
    OR(
								ISPICKVAL(Status, &apos;Trasferito&apos;),
        ISPICKVAL(Status, &apos;In attesa terze parti&apos;),
        ISPICKVAL(Status, &apos;In attesa risposta cliente&apos;),
        ISPICKVAL(Status, &apos;Ricevuta risposta cliente&apos;),
        ISPICKVAL(Status, &apos;Mancata risposta cliente&apos;),
        ISPICKVAL(Status, &apos;Chiuso - Annullato&apos;)
				)  
)</errorConditionFormula>
    <errorMessage>Cambio di stato non accessibile</errorMessage>
</ValidationRule>
