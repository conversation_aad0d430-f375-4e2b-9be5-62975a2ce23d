<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>urcs_CaseBloccoCambioOwner</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
   			$Setup.urcs_GeneralSettings__c.SkipValidationRule__c == false,
			NOT(ISNEW()),
			ISCHANGED(OwnerId),  
			OR(
      RecordType.DeveloperName == &apos;ur_CaseCRM&apos;,
      RecordType.DeveloperName == &apos;ur_CaseAR&apos;,
      RecordType.DeveloperName == &apos;ur_CaseSitoWeb&apos;,
      RecordType.DeveloperName == &apos;ur_CasePQ&apos;,
					 RecordType.DeveloperName == &apos;ur_CaseES&apos;
			),
			AND(
							OR(
		        AND(
							      BEGINS(OwnerId, &apos;005&apos; ),
  	          ISPICKVAL(Status, &apos;Trasferito&apos;)
										),
										AND(
  	          NOT(ISPICKVAL(Status, &apos;Trasferito&apos;))
										)
							)
			)
								
								
)</errorConditionFormula>
    <errorMessage>Per trasferire il case ad un altro ufficio è necessario utilizzare la funzionalità di Presa in Carico.</errorMessage>
</ValidationRule>
