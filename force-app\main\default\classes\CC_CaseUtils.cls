public class CC_CaseUtils {
  @TestVisible private static Map<String, String> queueBaseByTypeCache;
  public static Group getCCParkQueue() {
    List<Group> groups = [
      SELECT Id, Name, DeveloperName
      FROM Group
      WHERE Type = 'queue' 
      AND DeveloperName = 'CC_Park'
    ];

    return groups.isEmpty() ? null : groups[0];
  }

  public static Map<String, Integer> getCCCounters(){
    Map<String, Integer> counters = new Map<String, Integer>();

    // Perform the aggregate query
    for (AggregateResult result : [
      SELECT 
        CC_ContactCenter__c, Count(Id)
      FROM Case 
      WHERE RecordType.DeveloperName = 'CC_Contact_Center' 
      AND CC_ContactCenter__c != null
      GROUP BY CC_ContactCenter__c 
    ]) {
      counters.put((String)result.get('CC_ContactCenter__c'), (Integer)result.get('expr0'));
    }

    return counters;
  }

  // Returns the base Queue DeveloperName for a given Case.Type using Custom Metadata CC_QueueMapping__mdt
  public static String getBaseQueueDeveloperName(String caseType) {
    if (String.isBlank(caseType)) return null;
    ensureQueueMappingCache();
    return queueBaseByTypeCache.get(caseType);
  }

  @TestVisible
  static void clearQueueMappingCache() {
    queueBaseByTypeCache = null;
  }

  private static void ensureQueueMappingCache() {
    if (queueBaseByTypeCache != null) return;
    queueBaseByTypeCache = new Map<String, String>();
    for (CC_QueueMapping__mdt queueMapping : [
      SELECT TypeName__c, BaseQueueDeveloperName__c FROM CC_QueueMapping__mdt
    ]) {
      if (!String.isBlank(queueMapping.TypeName__c) && !String.isBlank(queueMapping.BaseQueueDeveloperName__c)) {
        queueBaseByTypeCache.put(queueMapping.TypeName__c, queueMapping.BaseQueueDeveloperName__c);
      }
    }
  }

  public static List<CC_CaseRoutingRules.RoutingRuleResponseItem> fetchRoutingRules(
    String activity,
    List<String> types,
    List<String> contact_center,
    List<String> need_scope
  ) {
    return CC_CaseRoutingRules.fetchRoutingRules(activity, types, contact_center, need_scope);
  }

  @AuraEnabled (cacheable=true)
    public static List<Case> ccGetClosedCaseByAccountId(Id accountId) {
        if (accountId == null) return new List<Case>();
        return [
            SELECT Id, CaseNumber, Subject, ClosedDate, Origin, CommercialAreasOfNeed__c, Esito__c, Digital_Step__c, Engagement_Channel__c
            FROM Case
            WHERE AccountId = :accountId
              AND RecordType.DeveloperName = 'CC_Contact_Center'
              AND IsClosed = true
            ORDER BY ClosedDate DESC NULLS LAST
        ];
    }
}
