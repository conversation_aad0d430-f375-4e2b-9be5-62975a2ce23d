@isTest
private class CC_CaseAssignmentBatchTest {
    @isTest
    static void testBatchRunsOnProvidedIds() {
        // Prepare sample Cases
        Case caseOne = new Case(Subject = 'Assign Me 1', Origin = 'Phone', Status = 'New');
        Case caseTwo = new Case(Subject = 'Assign Me 2', Origin = 'Web', Status = 'New');
        insert new List<Case>{ caseOne, caseTwo };

        Test.startTest();
        Database.executeBatch(new CC_CaseAssignmentBatch(new List<Id>{ caseOne.Id, caseTwo.Id }), 50);
        Test.stopTest();

    }

    @isTest
    static void testBatchWithDefaultSelectionNoop() {
        // No queue or matching records are created; start should return empty scope
        Test.startTest();
        Database.executeBatch(new CC_CaseAssignmentBatch(), 50);
        Test.stopTest();

    }

    @isTest
    static void testBatchUpdatesContactCenterAndOwner() {
        // Prepare Queue for Type 'CallMeBack' and center CC3 (expected first pick)
        List<Group> qList = [SELECT Id FROM Group WHERE DeveloperName = 'CC_Queue_CallMeBack_CC3_Case' LIMIT 1];
    Group queue;
      if(!qList.isEmpty()){
          queue = qList[0];
      }else{
        queue = new Group(Name = 'Queue CallMeBack CC3', DeveloperName = 'CC_Queue_CallMeBack_CC3_Case', Type = 'Queue');
    	insert queue;
      }
        //insert new QueueSobject(QueueId = queue.Id, SobjectType = 'Case');

        // Prepare a Case to be assigned
        Case toAssign = new Case(Subject = 'Assign Owner', Origin = 'Phone', Status = 'New', Type = 'CallMeBack');
        insert toAssign;

        Test.startTest();
        Database.executeBatch(new CC_CaseAssignmentBatch(new List<Id>{ toAssign.Id }), 1);
        Test.stopTest();

    }

    @isTest
    static void testBatchMappingWithSpacedType() {
        // Uses existing CMDT mapping: 'Acquisto non concluso' -> 'CC_Queue_AcquistoNonConcluso'
        List<Group> qList = [SELECT Id FROM Group WHERE DeveloperName = 'CC_Queue_AcquistoNonConcluso_CC3_Case' LIMIT 1];
    Group queue;
      if(!qList.isEmpty()){
          queue = qList[0];
      }else{
        queue = new Group(Name = 'Queue AcquistoNonConcluso CC3', DeveloperName = 'CC_Queue_AcquistoNonConcluso_CC3_Case', Type = 'Queue');
    	insert queue;
      }
        //insert new QueueSobject(QueueId = queue.Id, SobjectType = 'Case');

        Case toAssign = new Case(Subject = 'Assign Owner with spaced Type', Origin = 'Phone', Status = 'New', Type = 'Acquisto non concluso');
        insert toAssign;

        Test.startTest();
        Database.executeBatch(new CC_CaseAssignmentBatch(new List<Id>{ toAssign.Id }), 1);
        Test.stopTest();

    }
}