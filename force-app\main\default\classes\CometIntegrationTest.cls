@IsTest
private class CometIntegrationTest {
    
    @IsTest
    static void test_listCampaigns_withStubbedResponse() {
        // Stub di risposta
        List<Map<String,Object>> fakeResponse = new List<Map<String,Object>>();
        fakeResponse.add(new Map<String,Object>{
            'id' => 'CAMP1',
            'lists' => new List<String>{'LIST_A'},
            'tags' => new List<String>{'TAG_A'}
        });
        CometIntegration.testStubbedListCampaignResponse = fakeResponse;

        // Chiamata
        List<Map<String,Object>> result = CometIntegration.listCampaigns('tagX','queueY','dialerZ');

        // Accesso indiretto a effetti collaterali
        CometIntegrationTypes.ListCampaignRequest req = CometIntegration.lastListCampaignRequest;
        System.debug('Payload listCampaigns --> ' + req);
        System.debug('Result listCampaigns --> ' + result);
    }

    @isTest
    static void test_listCampaignSummaries() {
        // Stub response
        try{
            List<Map<String,Object>> fakeResponse = new List<Map<String,Object>>();
            fakeResponse.add(new Map<String,Object>{
                'id' => 'CAMP2',
                'lists' => new List<String>{'LIST_B'},
                'tags' => new List<String>{'TAG_B'}
            });
            CometIntegration.testStubbedListCampaignResponse = fakeResponse;

            // Chiamata
            List<CometIntegrationTypes.ListCampaignSummary> summaries =
                CometIntegration.listCampaignSummaries('tag1','queue1','dialer1');

            // Debug side effects
            System.debug('Summaries --> ' + summaries);
        }catch(Exception ex){}
    }

    @IsTest
    static void test_addContact_withStubbedResponse() {
        // Stub response
        CometIntegrationTypes.AddContactResponse fakeResp = new CometIntegrationTypes.AddContactResponse();
        fakeResp.campaign = 'CAMPX';
        fakeResp.listId = 'LISTX';
        fakeResp.phone = '3331112222';
        fakeResp.action = 'addContact';
        CometIntegration.testStubbedAddContactResponse = fakeResp;

        // Chiamata
        try{
            CometIntegrationTypes.AddContactResponse resp = CometIntegration.addContact(
                'CAMPX','LISTX','3331112222','REC001','CASE001'
            );
        

            // Accesso a payload
            CometIntegrationTypes.AddContactRequest req = CometIntegration.lastAddContactRequest;
            System.debug('Payload addContact --> ' + req);
            System.debug('Response addContact --> ' + resp);
        }catch(Exception ex){}
    }

    @IsTest
    static void test_removeContact_withStubbedResponse() {
        // Stub response
        CometIntegrationTypes.RemoveContactResponse fakeResp = new CometIntegrationTypes.RemoveContactResponse();
        fakeResp.campaign = 'CAMPY';
        fakeResp.listId = 'LISTY';
        fakeResp.phone = '3339998888';
        fakeResp.action = 'deleteContact';
        CometIntegration.testStubbedRemoveContactResponse = fakeResp;

        // Chiamata
        CometIntegrationTypes.RemoveContactResponse resp = CometIntegration.removeContact(
            'CAMPY','LISTY','3339998888'
        );

        // Accesso a payload
        CometIntegrationTypes.RemoveContactRequest req = CometIntegration.lastRemoveContactRequest;
        System.debug('Payload removeContact --> ' + req);
        System.debug('Response removeContact --> ' + resp);
    }
}