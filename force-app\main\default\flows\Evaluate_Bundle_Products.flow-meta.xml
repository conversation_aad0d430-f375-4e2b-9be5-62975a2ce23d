<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <assignments>
        <name>Assign_ESSIG_AUTO_Product</name>
        <label>Assign ESSIG AUTO Product</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ProductsOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>ESSIG_AUTO</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Increment_Counter</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ESSIG_RE_Product</name>
        <label>Assign ESSIG RE Product</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ProductsOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>ESSIG_RE</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Increment_Counter</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ESSIG_VITA_INDIVIDUALE_Product</name>
        <label>Assign ESSIG VITA INDIVIDUALE Product</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ProductsOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>ESSIG_VITA_INDIVIDUALE</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Increment_Counter</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ESSIG_VITA_Product</name>
        <label>Assign ESSIG VITA Product</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ProductsOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>ESSIG_VITA</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Increment_Counter</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_List_Product</name>
        <label>Assign List Product</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ProductsList</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>get_Products</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>ProductListFilter</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Multiple_Products</name>
        <label>Assign Multiple Products</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ProductsOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Multiplo</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Increment_Counter</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Previdenza_Product</name>
        <label>Assign Previdenza Product</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ProductsOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Previdenza</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Increment_Counter</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Unica_Product</name>
        <label>Assign Unica Product</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ProductsOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>PU</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Increment_Counter</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Increment_Counter</name>
        <label>Increment Counter</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>loopCounter</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Over_Get_Products</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Reset_Counter</name>
        <label>Reset Counter</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>loopCounter</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </assignmentItems>
    </assignments>
    <collectionProcessors>
        <name>ProductListFilter</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>ProductListFilter</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNextValueToReference>currentItem_ProductListFilter</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>ProductsList</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_ProductListFilter.Parent__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <elementReference>BundleOpportunity.Id</elementReference>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Loop_Over_Get_Products</targetReference>
        </connector>
    </collectionProcessors>
    <decisions>
        <name>check_Product_Domain_Type</name>
        <label>check Product Domain Type</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Increment_Counter</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Unica</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Over_Get_Products.DomainType__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>PU</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Unica_Product</targetReference>
            </connector>
            <label>Unica</label>
        </rules>
        <rules>
            <name>ESSIG_AUTO</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Over_Get_Products.DomainType__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ESSIG_AUTO</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ESSIG_AUTO_Product</targetReference>
            </connector>
            <label>ESSIG-AUTO</label>
        </rules>
        <rules>
            <name>ESSIG_RE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Over_Get_Products.DomainType__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ESSIG_RE</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ESSIG_RE_Product</targetReference>
            </connector>
            <label>ESSIG-RE</label>
        </rules>
        <rules>
            <name>ESSIG_VITA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Over_Get_Products.DomainType__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ESSIG_VITA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ESSIG_VITA_Product</targetReference>
            </connector>
            <label>ESSIG-VITA</label>
        </rules>
        <rules>
            <name>Previdenza</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Over_Get_Products.DomainType__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ESSIG_VITA_PREVIDENZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Previdenza_Product</targetReference>
            </connector>
            <label>Previdenza</label>
        </rules>
        <rules>
            <name>ESSIG_VITA_INDIVIDUALE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Over_Get_Products.DomainType__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ESSIG_VITA_INDIVIDUALE</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ESSIG_VITA_INDIVIDUALE_Product</targetReference>
            </connector>
            <label>ESSIG-VITA-INDIVIDUALE</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_Multiple_Product</name>
        <label>is Multiple Product</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>check_Product_Domain_Type</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Multiple_Product</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>loopCounter</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Multiple_Products</targetReference>
            </connector>
            <label>Multiple Product</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_Valid_Product</name>
        <label>is Valid Product</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>get_Products</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Valid_Product</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ProductsList</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ProductListFilter</targetReference>
            </connector>
            <label>Valid Product</label>
        </rules>
    </decisions>
    <description>vers prec 4
fix essig vita individuale</description>
    <environments>Default</environments>
    <interviewLabel>Evaluate Bundle Products {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Evaluate Bundle Products</label>
    <loops>
        <name>Loop_Over_Get_Products</name>
        <label>Loop Over Get Products</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>ProductListFilter</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>is_Multiple_Product</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Reset_Counter</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>get_Products</name>
        <label>get Products</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_List_Product</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Parent__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>BundleOpportunity.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>is_Valid_Product</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>BundleOpportunity</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>currentItem_ProductListFilter</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>loopCounter</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>ProductsList</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>ProductsOutput</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
