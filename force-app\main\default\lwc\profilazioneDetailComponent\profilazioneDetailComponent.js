import { LightningElement,api,track } from 'lwc';
import savePermissionSetAssignments from '@salesforce/apex/PermissionSetAssignmentController.savePermissionSetAssignments';
import getPermissionSetIdsByName from '@salesforce/apex/PermissionSetAssignmentController.getPermissionSetIdsByName';
import removePermissionSetAssignments from '@salesforce/apex/PermissionSetAssignmentController.removePermissionSetAssignments';
import getAssignedPermissionSetNames from '@salesforce/apex/PermissionSetAssignmentController.getAssignedPermissionSetNames';
import updateUserPersonas from '@salesforce/apex/PermissionSetAssignmentController.updateUserPersonas';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

export default class ProfilazioneDetailComponent extends LightningElement {

@api records = [];
@track localRecords = [];
@api userId; // ricevuto dalla FlexCard
@api selectedPersonas; // ricevuto dalla FlexCard
@api initialAssignedPermissionSets = []; // array di PermissionSetId assegnati inizialmente
@track selectedPermissionSets = []; // array di PermissionSetId selezionati
@track showModal = false;
@track modalMessage = '';
@track userAssignedPermissionSets = [];
@track searchTerm = '';

 connectedCallback(){
        let jsonString = JSON.stringify(this.records);
        console.log(jsonString);
        let parsedObject = JSON.parse(jsonString);

        // Logga il parametro selectedPersonas
        console.log('TEST selectedPersonas:', JSON.stringify(this.selectedPersonas));
        console.log('TEST userId:', JSON.stringify(this.userId));

        // Normalizzazione unica tramite mergePermissions
		if(parsedObject[0].mappingDataPS != null){

		  if(parsedObject[0].isUserDataNull == true){
			  this.localRecords = parsedObject[0].mappingDataPS;
		  }
		  else{
			  this.mergePermissions(parsedObject[0]);
		  }
		 }
		 else{
		  this.localRecords = parsedObject;
		 }
        console.log('##TEST localRecords dopo  mergePermissions:', JSON.stringify(this.localRecords));        

        if (this.localRecords) {
            // Conversione in array puro tramite JSON
            const recordsArray = JSON.parse(JSON.stringify(Array.from(this.localRecords)));
            // Valorizza initialAssignedPermissionSets con i PermissionSet Name assegnati all'inizio
            this.initialAssignedPermissionSets = recordsArray
                .filter(item => item.Checked)
                .map(item => item.PermissionSet);
            // Valorizza selectedPermissionSets (array di Name)
            this.selectedPermissionSets = [...this.initialAssignedPermissionSets];
            console.log('initialAssignedPermissionSets:', JSON.stringify(this.initialAssignedPermissionSets));
            console.log('selectedPermissionSets inizializzato:', JSON.stringify(this.selectedPermissionSets));
        }
    }


  mergePermissions(json) {
      try{

            console.log('##DEBUG input of mergePermissions:', JSON.stringify(json));
            const mappingDataPS = json.mappingDataPS;
            const permissionsMappingName = json.PermissionsMappingName;
            console.log('##DEBUG permissionsMappingName in mergePermissions:', JSON.stringify(permissionsMappingName));

            // 1. Crea un set di PermissionSet da PermissionsMappingName
            console.log('##DEBUG before  permissionsMappingName.map 1');
            let permissionsArray = [];
            if (Array.isArray(permissionsMappingName)) {
               permissionsArray = permissionsMappingName;
            } else if (permissionsMappingName && typeof permissionsMappingName === 'object') {
               // Se è un oggetto singolo, lo metto in un array
               permissionsArray = [permissionsMappingName];
            } else {
               // Se è undefined/null/altro, lascio array vuoto
               permissionsArray = [];
            }

            let permissionSetToTrue = new Set(
               permissionsArray.map(item => item.PermissionSet)
            );

            console.log('TEST in mergePermission selectedPersonas:', this.selectedPersonas);
            console.log('TEST in mergePermission userId:', this.userId);



            // 2. Crea una mappa per accedere rapidamente ai permessi completi da PermissionsMappingName
            const permissionDetailsMap = new Map(
               permissionsArray.map(item => [item.PermissionSet, item])
            );
            

            // 3. Crea un oggetto intermedio per evitare duplicati
            const updatedMap = new Map();
            if (!Array.isArray(mappingDataPS)) {
                console.error('mergePermissions: mappingDataPS non è un array o è undefined. Valore:', mappingDataPS, 'JSON:', JSON.stringify(mappingDataPS));
            }

            // 4. Prima: aggiorna tutti quelli esistenti in mappingDataPS
            mappingDataPS.forEach(item => {
            const isInSelected = permissionSetToTrue.has(item.PermissionSet);
            updatedMap.set(item.PermissionSet, {
               ...item,
               Checked: isInSelected ? true : item.Checked
            });
            });
            console.log('##DEBUG after step 4');
            // 5. Poi: aggiungi quelli che esistono solo in PermissionsMappingName
            permissionSetToTrue.forEach(permissionSet => {
            if (!updatedMap.has(permissionSet)) {
               updatedMap.set(permissionSet, {
                  ...permissionDetailsMap.get(permissionSet),
                  Checked: true
               });
            }
            });
            console.log('##DEBUG after step 5');

            // 6. Risultato finale
            this.localRecords = Array.from(updatedMap.values());
            console.log('##DEBUG final localRecords in mergepermission:', JSON.stringify(this.localRecords));
         } catch (error) {
               console.error('##TEST Error merging permissions:', error);
               console.error('##DEBUG error:', error, error.stack);
         }
      }


    saveSelectedPermissionSets() {
        // Salva il campo Personas__c per lo user
        updateUserPersonas({ userId: this.userId, personasValue: JSON.stringify(this.selectedPersonas) })
            .then(() => {
                console.log('Campo Personas__c aggiornato con successo');
            })
            .catch(error => {
                console.error('Errore nell\'aggiornamento di Personas__c:', error);
            });
    console.log('##DEBUG selectedPersonas al salvataggio:', JSON.stringify(this.selectedPersonas));
        if (!this.userId) {
            this.showToast('Errore', 'UserId mancante', 'error');
            return;
        }

        // Recupera i PermissionSet effettivamente assegnati allo user PRIMA di calcolare aggiunte/rimozioni
        getAssignedPermissionSetNames({ userId: this.userId })
        .then(result => {
            this.userAssignedPermissionSets = result;

            const initialSet = new Set(this.userAssignedPermissionSets);
            const selectedSet = new Set(this.selectedPermissionSets);
            console.log('##DEBUG userAssignedPermissionSets:', JSON.stringify(this.userAssignedPermissionSets));
            console.log('##DEBUG selectedPermissionSets prima di calcolare toAdd/toRemove:', JSON.stringify(this.selectedPermissionSets));

            let toAdd = Array.from(selectedSet).filter(ps => !initialSet.has(ps));
            toAdd = toAdd.filter(ps => !initialSet.has(ps));
            // Rimuovi solo i PermissionSet assegnati allo user, non più selezionati, e presenti nella lista base dei checkbox
            const baseCheckboxSet = new Set(this.localRecords.map(item => item.PermissionSet));
            const toRemove = Array.from(initialSet)
                .filter(ps => !selectedSet.has(ps))
                .filter(ps => baseCheckboxSet.has(ps));

            // Log di debug
            console.log('##DEBUG initialAssignedPermissionSets:', JSON.stringify(this.initialAssignedPermissionSets));
            console.log('##DEBUG selectedPermissionSets:', JSON.stringify(this.selectedPermissionSets));
            console.log('##DEBUG toAdd:', JSON.stringify(toAdd));
            console.log('##DEBUG toRemove:', JSON.stringify(toRemove));

            // Esegui aggiunta e rimozione
            let addPromise = Promise.resolve();
            if (toAdd.length > 0) {
                addPromise = getPermissionSetIdsByName({ names: toAdd })
                    .then(idsArray => {
                        console.log('PermissionSet Ids da aggiungere:', idsArray);
                        return savePermissionSetAssignments({ 
                            assigneeId: this.userId, 
                            permissionSetIds: idsArray 
                        });
                    });
            }

            let removePromise = Promise.resolve();
            if (toRemove.length > 0) {
                removePromise = removePermissionSetAssignments({
                    assigneeId: this.userId,
                    permissionSetNames: toRemove
                });
            }

            Promise.all([addPromise, removePromise])
                .then(() => {
                    this.modalMessage = 'Salvataggio completato con successo!';
                    this.showModal = true;
                })
                .catch(error => {
                    let errorMsg = 'Errore nel salvataggio';
                    console.error('##DEBUG errore completo:', error);

                    if (error && error.body && error.body.fieldErrors) {
                        Object.keys(error.body.fieldErrors).forEach(field => {
                            error.body.fieldErrors[field].forEach(e => {
                                errorMsg += ` [${field}]: ${e.message}`;
                            });
                        });
                    }

                    if (error && error.body && error.body.pageErrors && error.body.pageErrors.length > 0) {
                        error.body.pageErrors.forEach(e => {
                            errorMsg += ` [PageError]: ${e.message}`;
                        });
                    }

                    if (error && error.body && error.body.message) {
                        errorMsg += ': ' + error.body.message;
                    } else if (error && error.message) {
                        errorMsg += ': ' + error.message;
                    } else if (typeof error === 'string') {
                        errorMsg += ': ' + error;
                    } else {
                        errorMsg += '.';
                    }
                    this.modalMessage = errorMsg;
                    this.showModal = true;
                });
        })
        .catch(error => {
            console.error('Errore nel recupero dei PermissionSet assegnati:', error);
            this.showToast('Errore', 'Impossibile recuperare i PermissionSet assegnati', 'error');
        });
    }

    // Gestione checkbox
    handleCheckboxChange(event) {
        const permissionSetId = event.target.value;
        console.log('##TESDT in handlechange value: ' + permissionSetId);
        if (event.target.checked) {
            if (!this.selectedPermissionSets.includes(permissionSetId)) {
                this.selectedPermissionSets.push(permissionSetId);
            }
        } else {
            this.selectedPermissionSets = this.selectedPermissionSets.filter(id => id !== permissionSetId);
        }
        console.log('##TESDT ACTUAL selectedPermissionSets: ' + JSON.stringify(this.selectedPermissionSets));
    }

    // Mostra modale toast
    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({
            title: title,
            message: message,
            variant: variant,
            mode: 'dismissable'
        }));
    }

    // Chiusura modale e reload pagina
    handleModalOk() {
        this.showModal = false;
        window.location.reload();
    }


    // Gestione click su Conferma
handleConferma() {
    this.saveSelectedPermissionSets();
}

// Gestione click su Annulla
    handleAnnulla() {
            // Chiudi la modale di esito e ricarica la pagina
            window.location.reload();
    }

    get selectedCount() {
        return this.localRecords.filter(item => item.Checked).length;
    }

    handleSearch(event) {
        this.searchTerm = event.target.value;
    }

    get filteredRecords() {
        if (!this.searchTerm) {
            return this.localRecords;
        }
        const term = this.searchTerm.toLowerCase();
        // Debug: stampa cosa viene confrontato
        this.localRecords.forEach(item => {
            console.log('Confronto:', item.Label ? item.Label.toLowerCase() : '', 'con', term);
        });
        return this.localRecords.filter(item =>
            item.Label && item.Label.toLowerCase().includes(term)
        );
    }

}