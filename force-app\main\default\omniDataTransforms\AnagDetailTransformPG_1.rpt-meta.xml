<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <description><PERSON><PERSON><PERSON>, <PERSON>: eliminate vecchie versioni</description>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;KPI&quot; : {
    &quot;AccountKeyAgency&quot; : {
      &quot;&quot; : &quot;&quot;,
      &quot;PP_AGENZIA_STATO&quot; : {
        &quot;Value&quot; : &quot;Cliente&quot;
      },
      &quot;PP_AGENZIA_CLIENTE_DAL&quot; : {
        &quot;Value&quot; : &quot;22/04/2024&quot;
      },
      &quot;PP_AGENZIA_SOCIETA_STATO&quot; : {
        &quot;Value&quot; : &quot;Cliente&quot;
      },
      &quot;PP_AGENZIA_SOCIETA_CLIENTE_DAL&quot; : {
        &quot;Value&quot; : &quot;12/10/2022&quot;
      },
      &quot;PP_CONVENZIONI&quot; : {
        &quot;Value&quot; : &quot;Si&quot;
      },
      &quot;CRM_CLIENTE_VIP&quot; : {
        &quot;Value&quot; : &quot;Si&quot;
      },
      &quot;ConditionalBlock3Status&quot; : true,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;LoopBlockIterationIndex&quot; : 6
    },
    &quot;AccountKey&quot; : {
      &quot;&quot; : &quot;&quot;,
      &quot;CRMA_VALUS&quot; : {
        &quot;Value&quot; : 2
      },
      &quot;MDM_CONTATTABILITA&quot; : {
        &quot;Value&quot; : &quot;No&quot;
      },
      &quot;TPD_DATAULTIMOACCESSO&quot; : {
        &quot;Value&quot; : &quot;01/05/2024&quot;
      },
      &quot;CRMA_CAPACITADISPESA&quot; : {
        &quot;Value&quot; : &quot;Alta&quot;
      },
      &quot;PP_TITOLARITACLIENTE&quot; : {
        &quot;Value&quot; : &quot;Cliente Unipol&quot;
      },
      &quot;MEDAGLIA_VOC&quot; : null,
      &quot;TPD_ACCESSO&quot; : null,
      &quot;ANAG2_STATOCIVILE&quot; : {
        &quot;Value&quot; : &quot;Libero/Single&quot;
      },
      &quot;ANAG2_FLAGFIGLI&quot; : {
        &quot;Value&quot; : true
      },
      &quot;ANAG2_TITOLODISTUDIO&quot; : {
        &quot;Value&quot; : &quot;Laurea/Master&quot;
      },
      &quot;ANAG2_TIPORISPARMIO&quot; : {
        &quot;Value&quot; : &quot;Alto&quot;
      },
      &quot;ANAG2_FLAGCONIUGECARICO&quot; : {
        &quot;Value&quot; : false
      },
      &quot;ANAG2_NUMEROFIGLIOCARICO&quot; : {
        &quot;Value&quot; : 2
      },
      &quot;ANAG2_REDDITOLAVORO&quot; : {
        &quot;Value&quot; : 40
      },
      &quot;ANAG2_EVOLUZIONEREDDITOLAVORO&quot; : {
        &quot;Value&quot; : &quot;Media&quot;
      },
      &quot;ANAG2_EVOLUZIONEALTRIREDDITI&quot; : {
        &quot;Value&quot; : &quot;Alta&quot;
      },
      &quot;ANAG2_SPESEINCOMPRIMIBILI&quot; : {
        &quot;Value&quot; : 2500
      },
      &quot;ANAG2_SPESECOMPRIMIBILI&quot; : {
        &quot;Value&quot; : 1000
      },
      &quot;ANAG2_DATANASCITACONIUGE&quot; : {
        &quot;Value&quot; : &quot;22/06/1981&quot;
      },
      &quot;ANAG2_NUMERONUCLEOFAMIGLIA&quot; : {
        &quot;Value&quot; : 2
      },
      &quot;ANAG2_FLAGANIMALIDOMESTICI&quot; : {
        &quot;Value&quot; : true
      },
      &quot;ANAG2_FLAGCOLLABORATORIDOMESTICI&quot; : {
        &quot;Value&quot; : false
      },
      &quot;ANAG2_FLAGCASAPROPRIETA&quot; : {
        &quot;Value&quot; : true
      },
      &quot;ANAG2_FLAGCASAAFFITTO&quot; : {
        &quot;Value&quot; : false
      },
      &quot;ANAG2_FLAGSECONDACASA&quot; : {
        &quot;Value&quot; : false
      },
      &quot;ANAG2_FLAGMUTUO&quot; : {
        &quot;Value&quot; : true
      },
      &quot;CRIBIS_AZIENDAINUTILE_ULTIMOANNO&quot; : null,
      &quot;CRIBIS_AZIENDAINUTILE_ULTIMI3ANNI&quot; : null,
      &quot;CRIBIS_NDIPENDENTIPERINQUADRAMENTO&quot; : null,
      &quot;CRIBIS_DETTAGLIOTFR&quot; : null,
      &quot;ConditionalBlock2Status&quot; : true,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;LoopBlockIterationIndex&quot; : 24
    }
  },
  &quot;Value&quot; : {
    &quot;account_Name&quot; : &quot;COND. VIA ORBASSANO 100B&quot;,
    &quot;accountAgencyDetail_AdesioneCauzione&quot; : &quot;No&quot;,
    &quot;accountDetail_TipoPersonaGiuridica&quot; : &quot;N/D&quot;,
    &quot;accountAccountRelationSociey_Id&quot; : &quot;a009Q00000NqPzdQAF&quot;,
    &quot;accountDetail_FullName&quot; : &quot;Azienda IT&quot;,
    &quot;accountDetail_ClientePerimetro&quot; : &quot;N/D&quot;,
    &quot;accountDetail_CompanyType&quot; : &quot;Persona Giur L1&quot;,
    &quot;accountDetail_DataCostituzione&quot; : &quot;N/D&quot;,
    &quot;accountDetailMDM_SourceSystemOrigin&quot; : &quot;Sistema ANAG 1&quot;,
    &quot;accountAccountRelationAgency_Id&quot; : &quot;a009X00000RKjknQAD&quot;,
    &quot;accountAgencyDetail_Cellulare&quot; : &quot;+40 **********&quot;,
    &quot;accountDetail_VatNumber&quot; : &quot;PT234567890&quot;,
    &quot;accountDetail_StatoPartitaIva&quot; : &quot;N/D&quot;,
    &quot;accountAgencyDetail_Email&quot; : &quot;<EMAIL>&quot;,
    &quot;accountAgencyDetail_SubAgencyCode&quot; : &quot;COD 23456&quot;,
    &quot;accountDetail_Residenza&quot; : &quot;, , US&quot;
  }
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>AnagDetailTransformPG</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountDetail_ProfessioneDescrizione ISNOTBLANK var:Value:accountDetail_ProfessioneDescrizione &quot;-&quot; != &amp;&amp;</formulaConverted>
        <formulaExpression>ISNOTBLANK(Value:accountDetail_ProfessioneDescrizione) &amp;&amp; (Value:accountDetail_ProfessioneDescrizione != &quot;-&quot;)</formulaExpression>
        <formulaResultPath>showProfPG</formulaResultPath>
        <formulaSequence>15.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem14</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountDetail_TipologiaPersonaGiuridica &quot;TST&quot; == &quot;Tipologia/\/\/Settore&quot; &quot;&quot; IF</formulaConverted>
        <formulaExpression>IF(Value:accountDetail_TipologiaPersonaGiuridica == &quot;TST&quot;, &quot;Tipologia Settore&quot;, &quot;&quot;)</formulaExpression>
        <formulaResultPath>labelCheck2</formulaResultPath>
        <formulaSequence>17.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem17</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountDetail_TipologiaPersonaGiuridica &quot;GCL&quot; == &quot;Tipologia/\/\/Grande/\/\/Cliente&quot; &quot;&quot; IF</formulaConverted>
        <formulaExpression>IF(Value:accountDetail_TipologiaPersonaGiuridica == &quot;GCL&quot;, &quot;Tipologia Grande Cliente&quot;, &quot;&quot;)</formulaExpression>
        <formulaResultPath>labelCheck3</formulaResultPath>
        <formulaSequence>18.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem16</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountAgencyDetail_CellulareUsage &quot;PER&quot; == &quot;&quot; &quot;&quot; IF</formulaConverted>
        <formulaExpression>IF(Value:accountAgencyDetail_CellulareUsage == &quot;PER&quot;, &quot;&quot;, &quot;&quot;)</formulaExpression>
        <formulaResultPath>UsageCellConcat</formulaResultPath>
        <formulaSequence>7.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem19</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem74</globalKey>
        <inputFieldName>Value:accountAgencyDetail_SubAgencyCode</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:Subagenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_Fax ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_Fax))</formulaExpression>
        <formulaResultPath>ShowFax</formulaResultPath>
        <formulaSequence>6.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem13</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_PEC ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_PEC))</formulaExpression>
        <formulaResultPath>ShowPEC</formulaResultPath>
        <formulaSequence>14.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem12</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountDetail_TipologiaPersonaGiuridica &quot;CSS&quot; == &quot;Tipologia/\/\/Cassa&quot; &quot;&quot; IF</formulaConverted>
        <formulaExpression>IF(Value:accountDetail_TipologiaPersonaGiuridica == &quot;CSS&quot;, &quot;Tipologia Cassa&quot; , &quot;&quot;)</formulaExpression>
        <formulaResultPath>labelCheck1</formulaResultPath>
        <formulaSequence>16.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem15</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem113</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:nazioneNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem75</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoReferenteUsage</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaTelefonoReferenteUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem82</globalKey>
        <inputFieldName>Privacy</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:privacy</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem27</globalKey>
        <inputFieldName>Value:accountDetail_id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:extra:id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem20</globalKey>
        <inputFieldName>ShowFax</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:ShowFax</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem121</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:comuneNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem83</globalKey>
        <inputFieldName>Value:accountDetailMDM_Email</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:MigliorRecEmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem28</globalKey>
        <inputFieldName>Value:accountAgencyDetail_EmailUsage</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaEmailPersUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem120</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:codiceSoggettoCanale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem26</globalKey>
        <inputFieldName>ShowPEC</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:ShowPEC</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem118</globalKey>
        <inputFieldName>Value:accountAgencyDetail_Fax</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaFax</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem80</globalKey>
        <inputFieldName>UsageCellConcat</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:UsageCellConcat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem25</globalKey>
        <inputFieldName>Value:accountAgencyDetail_dataClienteTop</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:dataClienteTop</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem78</globalKey>
        <inputFieldName>Value:accountAgencyDetail_codiceSubagenzia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:codiceSubAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem91</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:Propaga</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem23</globalKey>
        <inputFieldName>Value:accountAgencyDetail_EmailId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaEmailPersId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem119</globalKey>
        <inputFieldName>Value:accountAgencyDetail_SubAgencyCode</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:Modifica:SubDropDef:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem81</globalKey>
        <inputFieldName>ShowCellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:ShowCellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem67</globalKey>
        <inputFieldName>Value:accountDetailMDM_Cellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:MigliorRecapitoCell</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem117</globalKey>
        <inputFieldName>Value:accountAgencyDetail_statoSoggetto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:statoSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem88</globalKey>
        <inputFieldName>Value:accountDegail_impiego</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:professione:impiego:codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem70</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoUsage</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaTelefonoUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem79</globalKey>
        <inputFieldName>Value:accountDetail_RegistryStatus</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:StatoCliente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem39</globalKey>
        <inputFieldName>Value:accountAgengyDetail_flagClienteTop</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:flagClienteTop</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem69</globalKey>
        <inputFieldName>UsageFaxConcat</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:UsageFaxConcat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem24</globalKey>
        <inputFieldName>Value:accountDetailPrivateArea_Cellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:ARCellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountDetail_TipologiaPersonaGiuridica &quot;CSS&quot; != var:Value:accountDetail_TipologiaPersonaGiuridica &quot;TST&quot; != &amp;&amp; var:Value:accountDetail_TipologiaPersonaGiuridica &quot;GCL&quot; != &amp;&amp; &quot;Professione&quot; &quot;&quot; IF</formulaConverted>
        <formulaExpression>IF(Value:accountDetail_TipologiaPersonaGiuridica != &quot;CSS&quot; &amp;&amp; Value:accountDetail_TipologiaPersonaGiuridica != &quot;TST&quot;  &amp;&amp; Value:accountDetail_TipologiaPersonaGiuridica != &quot;GCL&quot; , &quot;Professione&quot;, &quot;&quot;)</formulaExpression>
        <formulaResultPath>labelCheck4</formulaResultPath>
        <formulaSequence>19.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem2</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_Fax ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_Fax))</formulaExpression>
        <formulaResultPath>ShowFax</formulaResultPath>
        <formulaSequence>13.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem11</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem116</globalKey>
        <inputFieldName>Value:accountAgencyDetail_EmailPreferred</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaEmailPersPreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem128</globalKey>
        <inputFieldName>Value:accountAgencyDetail_Referente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:ReferenteAziendale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem63</globalKey>
        <inputFieldName>KPI:AccountKeyAgency:PP_CLIENTE_DAL:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:AnzianitaRelazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem22</globalKey>
        <inputFieldName>Value:accountAgencyDetail_CellularePreferred</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaCellPersonalePreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem89</globalKey>
        <inputFieldName>InfoSocietarie</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>infoSocietarie</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem66</globalKey>
        <inputFieldName>UsagePECConcat</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:UsagePECConcat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem114</globalKey>
        <inputFieldName>Value:accountAgencyDetail_AdesioneCauzione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:AdesioneCauzione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;true&quot; : &quot;SI&quot;,
  &quot;false&quot; : &quot;NO&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem40</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoReferenteId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaTelefonoReferenteId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem65</globalKey>
        <inputFieldName>Value:accountAgencyDetail_CellulareId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaCellPersonaleId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem76</globalKey>
        <inputFieldName>Value:accountAgencyDetail_AgenziaPrevalente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:codiceAgenziaPrevalente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_TelefonoReferente ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_TelefonoReferente))</formulaExpression>
        <formulaResultPath>ShowTelefonoReferente</formulaResultPath>
        <formulaSequence>5.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem68</globalKey>
        <inputFieldName>Value:accountAgencyDetail_PECUsage</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaPECUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem21</globalKey>
        <inputFieldName>Value:accountDetail_personaGiuridica</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:professione:personaGiuridica:codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem125</globalKey>
        <inputFieldName>Value:accountAgencyDetail_Cellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaCellPersonale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_Cellulare ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_Cellulare))</formulaExpression>
        <formulaResultPath>ShowCellulare</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem18</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem72</globalKey>
        <inputFieldName>Value:accountAgencyDetail_FaxPreferred</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaFaxPreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem35</globalKey>
        <inputFieldName>Value:accountAgencyDetail_SubAgencyCode</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:Modifica:SubDropDef:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem71</globalKey>
        <inputFieldName>UsageTELREFConcat</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:UsageTELREFConcat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem115</globalKey>
        <inputFieldName>Indirizzi</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:indirizzi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem64</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:STatoPartitaIVA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem77</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:TipoPersonaGiuridica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem126</globalKey>
        <inputFieldName>Value:accountAgencyDetail_PECPreferred</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaPECPreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem33</globalKey>
        <inputFieldName>Value:accountAgencyDetail_Telefono</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaTelefono</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:labelCheck1 var:labelCheck2 var:labelCheck3 var:labelCheck4 CONCAT</formulaConverted>
        <formulaExpression>CONCAT(labelCheck1, labelCheck2, labelCheck3, labelCheck4)</formulaExpression>
        <formulaResultPath>labelProfPG</formulaResultPath>
        <formulaSequence>20.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem36</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem123</globalKey>
        <inputFieldName>CompagnieMandato:list_mandato</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:extra:mandato:listaCompagnie</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem73</globalKey>
        <inputFieldName>UsageTELConcat</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:UsageTELConcat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem37</globalKey>
        <inputFieldName>Value:accountDetail_ProfessioneDescrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:Professione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountAgencyDetail_EmailUsage &quot;PER&quot; == &quot;&quot; &quot;&quot; IF</formulaConverted>
        <formulaExpression>IF(Value:accountAgencyDetail_EmailUsage == &quot;PER&quot;, &quot;&quot;, &quot;&quot;)</formulaExpression>
        <formulaResultPath>UsageMailConcat</formulaResultPath>
        <formulaSequence>8.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem32</globalKey>
        <inputFieldName>Value:codiceGestore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:GestoreAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem100</globalKey>
        <inputFieldName>Value:accountAgencyDetail_Email</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaEmailPers</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem86</globalKey>
        <inputFieldName>Value:accountDetail_settore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:professione:settore:codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem124</globalKey>
        <inputFieldName>Value:accountDetail_tipoSoggetto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:tipoSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem31</globalKey>
        <inputFieldName>CompagnieMandato:societa_attuale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:extra:mandato:compagniaAttuale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem87</globalKey>
        <inputFieldName>Value:accountDetailMDM_FonteCellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:MigliorRecFonteCellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem34</globalKey>
        <inputFieldName>Value:accountAgencyDetail_AgenziaPrevalente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:AgenziaPrevalente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem38</globalKey>
        <inputFieldName>Value:accountDetail_ProfessioneDescrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:professioneDescrizione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem30</globalKey>
        <inputFieldName>Value:account_VatNumber</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:codiceFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem98</globalKey>
        <inputFieldName>Value:accountAgencyDetail_PECId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaPECId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem122</globalKey>
        <inputFieldName>Value:accountAgencyDetail_CellulareUsage</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaCellPersonaleUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem138</globalKey>
        <inputFieldName>ShowEmail</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:ShowEmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem84</globalKey>
        <inputFieldName>Value:accountAgencyDetail_dataInizioEffetto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:dataInizioEffetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem101</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoPreferred</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaTelefonoPreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem29</globalKey>
        <inputFieldName>Value:accountDetail_Residenza</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:SedeLegale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem137</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:professione:mercatoPreferenziale:codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem92</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountAgencyDetail_TelefonoReferenteUsage &quot;PER&quot; == &quot;&quot; &quot;&quot; IF</formulaConverted>
        <formulaExpression>IF(Value:accountAgencyDetail_TelefonoReferenteUsage == &quot;PER&quot;, &quot;&quot;, &quot;&quot;)</formulaExpression>
        <formulaResultPath>UsageTELREFConcat</formulaResultPath>
        <formulaSequence>12.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem10</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem55</globalKey>
        <inputFieldName>Value:accountDetail_VatNumber</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:PartitaIva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem43</globalKey>
        <inputFieldName>Value:accountDetail_Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:nome</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem136</globalKey>
        <inputFieldName>Value:accountDetailMDM_SourceSystemOrigin</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:Originator</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;1&quot; : &quot;Unipol Assicurazioni S.p.A&quot;,
  &quot;2&quot; : &quot;BIM&quot;,
  &quot;4&quot; : &quot;UNISALUTE&quot;,
  &quot;5&quot; : &quot;LINEAR&quot;,
  &quot;7&quot; : &quot;FATTORIE DEL CERRO&quot;,
  &quot;8&quot; : &quot;LINEAR LIFE&quot;,
  &quot;10&quot; : &quot;ALG&quot;,
  &quot;11&quot; : &quot;NOVAAEG&quot;,
  &quot;12&quot; : &quot;BPER&quot;,
  &quot;13&quot; : &quot;TIM&quot;,
  &quot;14&quot; : &quot;SANTINI&quot;,
  &quot;15&quot; : &quot;CONAD&quot;,
  &quot;16&quot; : &quot;FINITALIA&quot;,
  &quot;17&quot; : &quot;UNIPOLTECH&quot;,
  &quot;18&quot; : &quot;UNA_NAXOS&quot;,
  &quot;19&quot; : &quot;CRIF/CRIBIS&quot;,
  &quot;20&quot; : &quot;PRONTO ASSISTANCE&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem58</globalKey>
        <inputFieldName>Value:accountAgencyDetail__DataCreazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:DataInizio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_Telefono ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_Telefono))</formulaExpression>
        <formulaResultPath>ShowTelefono</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem6</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem99</globalKey>
        <inputFieldName>Value:accountDetail_FiscalCodeStatus</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:statoCodiceFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem85</globalKey>
        <inputFieldName>Value:accountAgencyDetail_DataCessazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:DataCessazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem135</globalKey>
        <inputFieldName>Value:accountAgencyDetail_PEC</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaPEC</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem93</globalKey>
        <inputFieldName>Value:accountDetail_CompanyType</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:FormaSoocietaria</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem134</globalKey>
        <inputFieldName>Value:accountDetailPrivateArea_Email</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:AREmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem44</globalKey>
        <inputFieldName>Value:accountAgencyDetail_AgenziaPrevalente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:agenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem97</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaTelefonoId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem111</globalKey>
        <inputFieldName>Value:accountAgencyDetail_flagProprietaContattiFea</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:flagProprietaContattiFea</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountAgencyDetail_TelefonoUsage &quot;PER&quot; == &quot;&quot; &quot;&quot; IF</formulaConverted>
        <formulaExpression>IF(Value:accountAgencyDetail_TelefonoUsage == &quot;PER&quot;, &quot;&quot;, &quot;&quot;)</formulaExpression>
        <formulaResultPath>UsageTELConcat</formulaResultPath>
        <formulaSequence>11.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem7</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem133</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:dataNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem56</globalKey>
        <inputFieldName>labelProfPG</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:labelProfPG</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem129</globalKey>
        <inputFieldName>Value:accountDetail_tipoAnagrafica</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:tipoAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem96</globalKey>
        <inputFieldName>Value:accountAgencyDetail_dataFineEffetto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:dataFineEffetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem110</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoReferentePreferred</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaTelefonoReferentePreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem90</globalKey>
        <inputFieldName>Value:accountDetail_professione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:professione:professione:codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem131</globalKey>
        <inputFieldName>Value:accountDetail_LastName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:cognome</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>true</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem41</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:DisableCompany</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem94</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:codiceCanale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_PEC ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_PEC))</formulaExpression>
        <formulaResultPath>ShowTelefonoReferente</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem4</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem45</globalKey>
        <inputFieldName>Value:accountDetail_FiscalCodeStatus</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:statoCodiceFiscalePartitaIva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem130</globalKey>
        <inputFieldName>Value:accountAgencyDetail_Referente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaReferente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountAgencyDetail_PECUsage &quot;PER&quot; == &quot;&quot; &quot;&quot; IF</formulaConverted>
        <formulaExpression>IF(Value:accountAgencyDetail_PECUsage == &quot;PER&quot;, &quot;&quot;, &quot;&quot;)</formulaExpression>
        <formulaResultPath>UsagePECConcat</formulaResultPath>
        <formulaSequence>10.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem8</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem42</globalKey>
        <inputFieldName>Value:accountAgencyDetail_SubAgencyCode</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:Modifica:Subagenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_Email ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_Email))</formulaExpression>
        <formulaResultPath>ShowEmail</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem5</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem46</globalKey>
        <inputFieldName>Value:accountDetail_Ciu</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>string</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem127</globalKey>
        <inputFieldName>Value:accountDetail_compagnia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountAgencyDetail_FaxUsage &quot;PER&quot; == &quot;&quot; &quot;&quot; IF</formulaConverted>
        <formulaExpression>IF(Value:accountAgencyDetail_FaxUsage == &quot;PER&quot;, &quot;&quot;, &quot;&quot;)</formulaExpression>
        <formulaResultPath>UsageFaxConcat</formulaResultPath>
        <formulaSequence>9.0</formulaSequence>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem9</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem108</globalKey>
        <inputFieldName>Value:accountAgencyDetail_FaxId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaFaxId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem53</globalKey>
        <inputFieldName>Value:accountAgencyDetail_FaxUsage</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaFaxUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem132</globalKey>
        <inputFieldName>ShowTelefono</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:ShowTelefono</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem95</globalKey>
        <inputFieldName>Value:accountDetailMDM_FonteEmail</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:MigliorRecFonteEmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem106</globalKey>
        <inputFieldName>Value:accountDetail_FullName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:RagioneSociale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem51</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:comuneNascitaBelfiore</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem109</globalKey>
        <inputFieldName>Value:accountAgencyDetail_flagAdesioneFEA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:flagAdesioneFEA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem54</globalKey>
        <inputFieldName>Value:accountDetail_VatNumberStatus</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:StatoPartitaIva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>--</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem104</globalKey>
        <inputFieldName>Value:accountDetail_Corporate</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:IsCorporate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem49</globalKey>
        <inputFieldName>Value:accountDetail_statoAnagrafica</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:statoAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem107</globalKey>
        <inputFieldName>Value:accountAgencyDetail_DataCessazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:dataCessazioneCliente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem52</globalKey>
        <inputFieldName>UsageMailConcat</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:UsageMailConcat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem102</globalKey>
        <inputFieldName>Value:accountDetail_specializzazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:professione:specializzazione:codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem47</globalKey>
        <inputFieldName>Value:accountDetail_Gender</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:sesso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem105</globalKey>
        <inputFieldName>Value:accountAgencyDetail_AdesioneCauzione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:flagAutorizzazioneCauzione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem50</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:tipoAnagraficaRichiesto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem140</globalKey>
        <inputFieldName>Value:codiceGestore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:Modifica:GestoreAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem103</globalKey>
        <inputFieldName>showProfPG</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:showProfPG</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem48</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:dataDecesso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem139</globalKey>
        <inputFieldName>ShowTelefonoReferente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:ShowTelefonoReferente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem60</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:DataCostituzione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem59</globalKey>
        <inputFieldName>KPI:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:StatoSoggettoPerAzienda</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem62</globalKey>
        <inputFieldName>Value:accountAgencyDetail_DataCessazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:DataCessazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem61</globalKey>
        <inputFieldName>KPI:AccountKeyAgency:PP_AGENZIA_SOCIETA_CLIENTE_DAL:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:AnzianitaRelAltraAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem112</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoReferente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaTelefonoReferente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>No</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformPGCustom0jI9V000000rv09UAAItem57</globalKey>
        <inputFieldName>KPI:AccountKeyAgency:CRMA_CLIENTEPATTO30:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransformPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:ClienteInPerimetro</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;company&quot; : &quot;UNIPOL&quot;,
  &quot;type&quot; : &quot;Business&quot;,
  &quot;tipoFormaGiuridica&quot; : &quot;Ditta Individuale&quot;,
  &quot;KPI&quot; : {
    &quot;AccountKeyAgency&quot; : {
      &quot;&quot; : &quot;&quot;,
      &quot;PP_AGENZIA_STATO&quot; : null,
      &quot;PP_AGENZIA_CLIENTE_DAL&quot; : null,
      &quot;PP_AGENZIA_SOCIETA_STATO&quot; : null,
      &quot;PP_AGENZIA_SOCIETA_CLIENTE_DAL&quot; : null,
      &quot;PP_CONVENZIONI&quot; : null,
      &quot;CRM_CLIENTE_VIP&quot; : null,
      &quot;MDM_CONTATTABILITA&quot; : null,
      &quot;ConditionalBlock3Status&quot; : true,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;LoopBlockIterationIndex&quot; : 1
    },
    &quot;AccountKey&quot; : {
      &quot;&quot; : &quot;&quot;,
      &quot;CRMA_VALUS&quot; : null,
      &quot;TPD_DATAULTIMOACCESSO&quot; : null,
      &quot;CRMA_CAPACITADISPESA&quot; : null,
      &quot;PP_TITOLARITACLIENTE&quot; : null,
      &quot;MEDAGLIA_VOC&quot; : null,
      &quot;TPD_ACCESSO&quot; : null,
      &quot;ANAG2_STATOCIVILE&quot; : null,
      &quot;ANAG2_FLAGFIGLI&quot; : null,
      &quot;ANAG2_TITOLODISTUDIO&quot; : null,
      &quot;ANAG2_TIPORISPARMIO&quot; : null,
      &quot;ANAG2_FLAGCONIUGECARICO&quot; : null,
      &quot;ANAG2_NUMEROFIGLIOCARICO&quot; : null,
      &quot;ANAG2_REDDITOLAVORO&quot; : null,
      &quot;ANAG2_EVOLUZIONEREDDITOLAVORO&quot; : null,
      &quot;ANAG2_EVOLUZIONEALTRIREDDITI&quot; : null,
      &quot;ANAG2_SPESEINCOMPRIMIBILI&quot; : null,
      &quot;ANAG2_SPESECOMPRIMIBILI&quot; : null,
      &quot;ANAG2_DATANASCITACONIUGE&quot; : null,
      &quot;ANAG2_NUMERONUCLEOFAMIGLIA&quot; : null,
      &quot;ANAG2_FLAGANIMALIDOMESTICI&quot; : null,
      &quot;ANAG2_FLAGCOLLABORATORIDOMESTICI&quot; : null,
      &quot;ANAG2_FLAGCASAPROPRIETA&quot; : null,
      &quot;ANAG2_FLAGCASAAFFITTO&quot; : null,
      &quot;ANAG2_FLAGSECONDACASA&quot; : null,
      &quot;ANAG2_FLAGMUTUO&quot; : null,
      &quot;CRIBIS_AZIENDAINUTILE_ULTIMOANNO&quot; : null,
      &quot;CRIBIS_AZIENDAINUTILE_ULTIMI3ANNI&quot; : null,
      &quot;CRIBIS_NDIPENDENTIPERINQUADRAMENTO&quot; : null,
      &quot;CRIBIS_DETTAGLIOTFR&quot; : null,
      &quot;V_QUEST&quot; : null,
      &quot;CRIBIS&quot; : null,
      &quot;BPER_MARCHIATURASOGGETTO&quot; : null,
      &quot;BPER_TUTELASALVAGUARDIABPER&quot; : null,
      &quot;ASSURBANCA_PROCESSI&quot; : null,
      &quot;BANKASSURANCE_BISOGNI&quot; : null,
      &quot;BANKASSURANCE_PROCESSI&quot; : null,
      &quot;TPD_COMPORTAMENTOOMNICANALE&quot; : null,
      &quot;TPD_CANALEACCESSO&quot; : null,
      &quot;TPD_PRESENZAAPP&quot; : null,
      &quot;ANAG2_DATIATTRIBUTI&quot; : null,
      &quot;ConditionalBlock2Status&quot; : true,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;LoopBlockIterationIndex&quot; : 1
    }
  },
  &quot;Value&quot; : {
    &quot;accountDetail_dugDomi&quot; : &quot;&quot;,
    &quot;accountAgencyDetail_flagClienteTop&quot; : false,
    &quot;accountDetail_FiscalCodeStatus&quot; : &quot;-&quot;,
    &quot;accountDetail_pressoDomi&quot; : &quot;&quot;,
    &quot;accountAgencyDetail_CellularePreferred&quot; : false,
    &quot;accountDetail_tipoIndirizzo&quot; : &quot;&quot;,
    &quot;accountAgencyDetail_TelefonoReferentePreferred&quot; : false,
    &quot;accountAgencyDetail_statoSoggetto&quot; : &quot;&quot;,
    &quot;accountDetail_codiceIstatComune&quot; : &quot;&quot;,
    &quot;accountDetail_codiceBelfioreComuneDomi&quot; : &quot;&quot;,
    &quot;accountAgencyDetail_FaxPreferred&quot; : false,
    &quot;accountDetail_mail&quot; : &quot;&quot;,
    &quot;accountAgencyDetail_EmailPreferred&quot; : false,
    &quot;accountAgencyDetail_TelefonoPreferred&quot; : false,
    &quot;accountDetail_BirthCountry&quot; : &quot;-&quot;,
    &quot;accountDetail_Gender&quot; : &quot;-&quot;,
    &quot;accountDetail_longitudineDomi&quot; : 0,
    &quot;accountDetail_dug&quot; : &quot;&quot;,
    &quot;accountDetail_tipoSoggetto&quot; : &quot;&quot;,
    &quot;accountAccountRelationSociey_Id&quot; : &quot;a009O00000YqoOXQAZ&quot;,
    &quot;accountDetail_codiceIstatAnag1Domi&quot; : &quot;&quot;,
    &quot;accountDetail_dataDecesso&quot; : null,
    &quot;accountDetail_Eta&quot; : &quot;-&quot;,
    &quot;accountDetail_ClientePerimetro&quot; : &quot;-&quot;,
    &quot;accountAgencyDetail_flagAdesioneFEA&quot; : false,
    &quot;accountDetail_CompanyType&quot; : &quot;-&quot;,
    &quot;accountDetail_LastName&quot; : &quot;-&quot;,
    &quot;accountDetail_professione&quot; : &quot;2904&quot;,
    &quot;accountDetail_codiceBelfioreComune&quot; : &quot;&quot;,
    &quot;accountDetail_personaGiuridica&quot; : &quot;DIN&quot;,
    &quot;accountDetail_BirthProvince&quot; : &quot;-&quot;,
    &quot;accountDetail_Occupation&quot; : &quot;2904&quot;,
    &quot;account_ExternalId&quot; : &quot;***********&quot;,
    &quot;accountDetail_Domicilio&quot; : &quot;-&quot;,
    &quot;accountDetail_tipoNormalizzatoDomi&quot; : &quot;&quot;,
    &quot;accountDetail_latitudineDomi&quot; : 0,
    &quot;accountAgencyDetail_dataClienteTop&quot; : null,
    &quot;accountAgencyDetail_SubAgencyCode&quot; : &quot;-&quot;,
    &quot;accountDetail_RegistryStatus&quot; : &quot;-&quot;,
    &quot;accountDetail_Name&quot; : &quot;PROVAPROF&quot;,
    &quot;accountDetail_specializzazione&quot; : &quot;&quot;,
    &quot;accountAgencyDetail_flagProprietaContattiFea&quot; : false,
    &quot;AccountDetail_presso&quot; : &quot;&quot;,
    &quot;accountDetail_localitaDomi&quot; : &quot;&quot;,
    &quot;accountAgencyDetail_AdesioneCauzione&quot; : false,
    &quot;accountDetail_TipologiaPersonaGiuridica&quot; : &quot;xxx&quot;,
    &quot;accountDetail_statoAnagrafica&quot; : &quot;&quot;,
    &quot;accountDetail_FullName&quot; : &quot;PROVAPROF&quot;,
    &quot;accountDetail_idContattoMail&quot; : &quot;&quot;,
    &quot;accountDetail_flagDomicilio&quot; : true,
    &quot;accountAccountRelationAgency_Id&quot; : &quot;a009O00000YqoOYQAZ&quot;,
    &quot;accountDetail_codiceIstatAnag1&quot; : &quot;&quot;,
    &quot;accountAgencyDetail_id&quot; : &quot;a1h9O000006skDhQAI&quot;,
    &quot;accountDetail_tipoIndirizzoDomi&quot; : &quot;&quot;,
    &quot;accountAgencyDetail_Cellulare&quot; : &quot;+************&quot;,
    &quot;accountDetail_tipoAnagrafica&quot; : &quot;&quot;,
    &quot;accountDetail_VatNumber&quot; : &quot;***********&quot;,
    &quot;accountDetailMDM_StatoEmail&quot; : &quot;-&quot;,
    &quot;accountDetail_BirthPlace&quot; : &quot;-&quot;,
    &quot;accountDetail_numeroCivico&quot; : &quot;&quot;,
    &quot;accountDetail_id&quot; : &quot;a1i9O000005UQS1QAO&quot;,
    &quot;accountDetail_ProfessioneDescrizione&quot; : &quot;Allenatore/Istruttore sportivo di Nuoto&quot;,
    &quot;accountDetail_FirstName&quot; : &quot;-&quot;,
    &quot;accountDetail_cellulare&quot; : &quot;&quot;,
    &quot;accountDetail_FiscalCode&quot; : &quot;-&quot;,
    &quot;accountDetail_compagnia&quot; : &quot;UNIPOL&quot;,
    &quot;accountDetail_numeroCivicoDomi&quot; : &quot;&quot;,
    &quot;accountAgencyDetail_PECPreferred&quot; : false,
    &quot;accountDetail_TipoPersonaGiuridica&quot; : &quot;-&quot;,
    &quot;accountDetail_idContattoCellulare&quot; : &quot;&quot;,
    &quot;accountAgencyDetail_AgenziaPrevalente&quot; : &quot;01853&quot;,
    &quot;accountAgencyDetail_Domicilio&quot; : &quot;-&quot;,
    &quot;accountDetail_impiego&quot; : &quot;&quot;,
    &quot;accountAgencyDetail__DataCreazione&quot; : &quot;-&quot;,
    &quot;accountDetail_DataCostituzione&quot; : &quot;-&quot;,
    &quot;accountDetailMDM_StatoCellulare&quot; : &quot;-&quot;,
    &quot;accountDetail_tipoNormalizzato&quot; : &quot;&quot;,
    &quot;accountDetail_localita&quot; : &quot;&quot;,
    &quot;accountDetailMDM_SourceSystemOrigin&quot; : &quot;-&quot;,
    &quot;account_FEA&quot; : false,
    &quot;accountDetail_settore&quot; : &quot;&quot;,
    &quot;accountDetail_VatNumberStatus&quot; : &quot;-&quot;,
    &quot;accountDetail_BirthDate&quot; : &quot;-&quot;,
    &quot;accountDetail_StatoPartitaIva&quot; : &quot;-&quot;,
    &quot;accountDetail_Ciu&quot; : &quot;7177662&quot;,
    &quot;accountDetail_Residenza&quot; : &quot;-&quot;
  },
  &quot;Indirizzi&quot; : [ {
    &quot;codiceBelfioreComune&quot; : &quot;&quot;,
    &quot;localita&quot; : &quot;&quot;,
    &quot;cabComune&quot; : &quot;&quot;,
    &quot;tipoIndirizzo&quot; : &quot;RESI&quot;,
    &quot;dus&quot; : &quot;&quot;,
    &quot;cabStato&quot; : &quot;&quot;,
    &quot;idIndirizzo&quot; : &quot;&quot;,
    &quot;codiceIstatComune&quot; : &quot;&quot;,
    &quot;codiceUnsdM49&quot; : &quot;&quot;,
    &quot;numeroCivico&quot; : &quot;&quot;,
    &quot;tipoNormalizzato&quot; : &quot;N&quot;,
    &quot;stato&quot; : &quot;&quot;,
    &quot;codiceIstatAnag1&quot; : &quot;&quot;,
    &quot;cap&quot; : &quot;&quot;,
    &quot;indirizzoBreve&quot; : &quot;&quot;,
    &quot;codiceBelfioreStato&quot; : &quot;&quot;,
    &quot;indirizzoCompleto&quot; : &quot;&quot;,
    &quot;latitudine&quot; : null,
    &quot;dug&quot; : &quot;&quot;,
    &quot;abbreviazioneProvincia&quot; : &quot;&quot;,
    &quot;presso&quot; : &quot;&quot;,
    &quot;longitudine&quot; : null,
    &quot;flagPreview&quot; : false
  }, {
    &quot;codiceBelfioreComune&quot; : &quot;&quot;,
    &quot;localita&quot; : &quot;&quot;,
    &quot;cabComune&quot; : &quot;&quot;,
    &quot;tipoIndirizzo&quot; : &quot;DOMI&quot;,
    &quot;dus&quot; : &quot;&quot;,
    &quot;cabStato&quot; : &quot;&quot;,
    &quot;idIndirizzo&quot; : &quot;&quot;,
    &quot;codiceIstatComune&quot; : &quot;&quot;,
    &quot;codiceUnsdM49&quot; : &quot;&quot;,
    &quot;numeroCivico&quot; : &quot;&quot;,
    &quot;tipoNormalizzato&quot; : &quot;N&quot;,
    &quot;stato&quot; : &quot;&quot;,
    &quot;codiceIstatAnag1&quot; : &quot;&quot;,
    &quot;cap&quot; : &quot;&quot;,
    &quot;indirizzoBreve&quot; : &quot;&quot;,
    &quot;codiceBelfioreStato&quot; : &quot;&quot;,
    &quot;indirizzoCompleto&quot; : &quot;&quot;,
    &quot;latitudine&quot; : 0,
    &quot;dug&quot; : &quot;&quot;,
    &quot;abbreviazioneProvincia&quot; : &quot;&quot;,
    &quot;presso&quot; : &quot;&quot;,
    &quot;longitudine&quot; : 0,
    &quot;flagPreview&quot; : false
  } ],
  &quot;Professione&quot; : {
    &quot;professione&quot; : {
      &quot;descrizione&quot; : &quot;Impiegato&quot;,
      &quot;codice&quot; : &quot;1401&quot;
    },
    &quot;mercatoPreferenziale&quot; : {
      &quot;descrizione&quot; : &quot;sconosciuto&quot;,
      &quot;codice&quot; : &quot;00&quot;
    },
    &quot;impiego&quot; : {
      &quot;descrizione&quot; : &quot;DIPENDENTE&quot;,
      &quot;codice&quot; : &quot;D&quot;
    },
    &quot;settore&quot; : {
      &quot;descrizione&quot; : &quot;PUBBLICO&quot;,
      &quot;codice&quot; : &quot;PUB&quot;
    }
  },
  &quot;Privacy&quot; : {
    &quot;datiAdesione&quot; : {
      &quot;applicazioneFine&quot; : &quot;&quot;,
      &quot;dataInizioEffetto&quot; : &quot;&quot;,
      &quot;tipoPrivacy&quot; : &quot;&quot;,
      &quot;applicazioneInizio&quot; : &quot;PUPTF&quot;
    }
  },
  &quot;InfoSocietarie&quot; : {
    &quot;Compagnia&quot; : &quot;UNIPOL&quot;,
    &quot;Ciu&quot; : 7177662,
    &quot;InfoSocietarie&quot; : {
      &quot;error&quot; : &quot;XML or JSON parsing error.&quot;
    },
    &quot;SitoWeb&quot; : null,
    &quot;Attivita&quot; : {
      &quot;AtecoShortCode&quot; : &quot;&quot;,
      &quot;AtecoFlagConnection&quot; : false,
      &quot;AtecoShort&quot; : &quot;&quot;,
      &quot;AtecoFlagSociety&quot; : false,
      &quot;AtecoActivityType&quot; : &quot;&quot;,
      &quot;AtecoRAE&quot; : &quot;&quot;,
      &quot;AtecoTypeCode&quot; : &quot;&quot;,
      &quot;AtecoSAE&quot; : &quot;&quot;,
      &quot;AtecoCode&quot; : &quot;&quot;,
      &quot;ClientType&quot; : &quot;Business&quot;,
      &quot;Modifica&quot; : {
        &quot;RAELabel&quot; : null,
        &quot;SAELabel&quot; : null,
        &quot;SummaryLabel&quot; : null,
        &quot;ActivityLabel&quot; : null,
        &quot;ATECOLabel&quot; : null,
        &quot;ActivityValues&quot; : [ {
          &quot;value&quot; : &quot;000&quot;,
          &quot;label&quot; : &quot;000 - SCONOSCIUTO&quot;
        }, {
          &quot;value&quot; : &quot;001&quot;,
          &quot;label&quot; : &quot;001 - PENSIONATO&quot;
        }, {
          &quot;value&quot; : &quot;002&quot;,
          &quot;label&quot; : &quot;002 - STUDENTE&quot;
        }, {
          &quot;value&quot; : &quot;003&quot;,
          &quot;label&quot; : &quot;003 - CASALINGA&quot;
        }, {
          &quot;value&quot; : &quot;004&quot;,
          &quot;label&quot; : &quot;004 - DISOCCUPATO&quot;
        }, {
          &quot;value&quot; : &quot;005&quot;,
          &quot;label&quot; : &quot;005 - DIRIGENTE&quot;
        }, {
          &quot;value&quot; : &quot;006&quot;,
          &quot;label&quot; : &quot;006 - IMPIEGATO&quot;
        }, {
          &quot;value&quot; : &quot;007&quot;,
          &quot;label&quot; : &quot;007 - OPERAIO&quot;
        }, {
          &quot;value&quot; : &quot;008&quot;,
          &quot;label&quot; : &quot;008 - DIPENDENTE COMMERCIO&quot;
        }, {
          &quot;value&quot; : &quot;009&quot;,
          &quot;label&quot; : &quot;009 - INSEGNANTE&quot;
        }, {
          &quot;value&quot; : &quot;00H&quot;,
          &quot;label&quot; : &quot;00H - 00H - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;00O&quot;,
          &quot;label&quot; : &quot;00O - 00O - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;010&quot;,
          &quot;label&quot; : &quot;010 - ARCHITETTO&quot;
        }, {
          &quot;value&quot; : &quot;011&quot;,
          &quot;label&quot; : &quot;011 - INGEGNERE&quot;
        }, {
          &quot;value&quot; : &quot;012&quot;,
          &quot;label&quot; : &quot;012 - GEOMETRA&quot;
        }, {
          &quot;value&quot; : &quot;013&quot;,
          &quot;label&quot; : &quot;013 - AVVOCATO - PROCURATORE&quot;
        }, {
          &quot;value&quot; : &quot;014&quot;,
          &quot;label&quot; : &quot;014 - NOTAIO&quot;
        }, {
          &quot;value&quot; : &quot;015&quot;,
          &quot;label&quot; : &quot;015 - DOTTORE/RAGIONIERE COMMERCIALISTA&quot;
        }, {
          &quot;value&quot; : &quot;016&quot;,
          &quot;label&quot; : &quot;016 - MEDICO&quot;
        }, {
          &quot;value&quot; : &quot;017&quot;,
          &quot;label&quot; : &quot;017 - CONSULENTE&quot;
        }, {
          &quot;value&quot; : &quot;018&quot;,
          &quot;label&quot; : &quot;018 - PERITO - ESPERTO&quot;
        }, {
          &quot;value&quot; : &quot;019&quot;,
          &quot;label&quot; : &quot;019 - ARTISTA&quot;
        }, {
          &quot;value&quot; : &quot;02/&quot;,
          &quot;label&quot; : &quot;02/ - 02/ - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;020&quot;,
          &quot;label&quot; : &quot;020 - ATLETA&quot;
        }, {
          &quot;value&quot; : &quot;021&quot;,
          &quot;label&quot; : &quot;021 - IMPRENDITORE&quot;
        }, {
          &quot;value&quot; : &quot;027&quot;,
          &quot;label&quot; : &quot;027 - 027 - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;037&quot;,
          &quot;label&quot; : &quot;037 - 037 - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;049&quot;,
          &quot;label&quot; : &quot;049 - 049 - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;091&quot;,
          &quot;label&quot; : &quot;091 - 091 - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;0CI&quot;,
          &quot;label&quot; : &quot;0CI - 0CI - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;0DI&quot;,
          &quot;label&quot; : &quot;0DI - 0DI - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;0RI&quot;,
          &quot;label&quot; : &quot;0RI - 0RI - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;0SN&quot;,
          &quot;label&quot; : &quot;0SN - 0SN - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;0/A&quot;,
          &quot;label&quot; : &quot;0/A - 0/A - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;A 1&quot;,
          &quot;label&quot; : &quot;A 1 - A 1 - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;A 4&quot;,
          &quot;label&quot; : &quot;A 4 - A 4 - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;A 6&quot;,
          &quot;label&quot; : &quot;A 6 - A 6 - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;A 9&quot;,
          &quot;label&quot; : &quot;A 9 - A 9 - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;BIN&quot;,
          &quot;label&quot; : &quot;BIN - BIN - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;CIS&quot;,
          &quot;label&quot; : &quot;CIS - CIS - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;DON&quot;,
          &quot;label&quot; : &quot;DON - DON - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;ENT&quot;,
          &quot;label&quot; : &quot;ENT - ENT - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;EOT&quot;,
          &quot;label&quot; : &quot;EOT - EOT - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;ETA&quot;,
          &quot;label&quot; : &quot;ETA - ETA - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;IAN&quot;,
          &quot;label&quot; : &quot;IAN - IAN - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;ICO&quot;,
          &quot;label&quot; : &quot;ICO - ICO - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;I 1&quot;,
          &quot;label&quot; : &quot;I 1 - I 1 - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;I 6&quot;,
          &quot;label&quot; : &quot;I 6 - I 6 - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;KM&quot;,
          &quot;label&quot; : &quot;KM - KM . DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;NCI&quot;,
          &quot;label&quot; : &quot;NCI - NCI - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;NZA&quot;,
          &quot;label&quot; : &quot;NZA - NZA - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;O 1&quot;,
          &quot;label&quot; : &quot;O 1 - O 1 - DA ELIDERE&quot;
        } ],
        &quot;SummaryValues&quot; : [ {
          &quot;value&quot; : &quot;000&quot;,
          &quot;label&quot; : &quot;000 - SCONOSCIUTO&quot;
        }, {
          &quot;value&quot; : &quot;100&quot;,
          &quot;label&quot; : &quot;100 - AMMINISTRAZIONI PUBBLICHE&quot;
        }, {
          &quot;value&quot; : &quot;101&quot;,
          &quot;label&quot; : &quot;101 - AMMINISTRAZIONI CENTRALI ED ALTRE AMMINISTRAZIONI&quot;
        }, {
          &quot;value&quot; : &quot;102&quot;,
          &quot;label&quot; : &quot;102 - AMMINISTRAZIONI LOCALI&quot;
        }, {
          &quot;value&quot; : &quot;103&quot;,
          &quot;label&quot; : &quot;103 - SERVIZI SANITARI PUBBLICI&quot;
        }, {
          &quot;value&quot; : &quot;104&quot;,
          &quot;label&quot; : &quot;104 - SERVIZI ASSISTENZIALI, RICREATIVI E CULTURALI PUBBLICI&quot;
        }, {
          &quot;value&quot; : &quot;200&quot;,
          &quot;label&quot; : &quot;200 - IMPRESE DI ASSICURAZIONE E FONDI PENSIONE&quot;
        }, {
          &quot;value&quot; : &quot;310&quot;,
          &quot;label&quot; : &quot;310 - SISTEMA BANCARIO&quot;
        }, {
          &quot;value&quot; : &quot;311&quot;,
          &quot;label&quot; : &quot;311 - INTERMEDIARI FINANZIARI&quot;
        }, {
          &quot;value&quot; : &quot;312&quot;,
          &quot;label&quot; : &quot;312 - ALTRI INTERMEDIARI FINANZIARI&quot;
        }, {
          &quot;value&quot; : &quot;410&quot;,
          &quot;label&quot; : &quot;410 - AGRICOLTURA&quot;
        }, {
          &quot;value&quot; : &quot;411&quot;,
          &quot;label&quot; : &quot;411 - INDUSTRIA MINERARIA, ENERGETICA, PETROLCHIMICA E SIDERURGICA&quot;
        }, {
          &quot;value&quot; : &quot;412&quot;,
          &quot;label&quot; : &quot;412 - EDILIZIA&quot;
        }, {
          &quot;value&quot; : &quot;413&quot;,
          &quot;label&quot; : &quot;413 - FABBRICAZIONE DI MACCHINE E APPARECCHIATURE&quot;
        }, {
          &quot;value&quot; : &quot;414&quot;,
          &quot;label&quot; : &quot;414 - INDUSTRIA ALIMENTARE&quot;
        }, {
          &quot;value&quot; : &quot;415&quot;,
          &quot;label&quot; : &quot;415 - TESSILE&quot;
        }, {
          &quot;value&quot; : &quot;416&quot;,
          &quot;label&quot; : &quot;416 - ALTRI PRODOTTI INDUSTRIALI&quot;
        }, {
          &quot;value&quot; : &quot;510&quot;,
          &quot;label&quot; : &quot;510 - COMMERCIO ALL INGROSSO&quot;
        }, {
          &quot;value&quot; : &quot;511&quot;,
          &quot;label&quot; : &quot;511 - COMMERCIO AL MINUTO&quot;
        }, {
          &quot;value&quot; : &quot;512&quot;,
          &quot;label&quot; : &quot;512 - SETTORE ALBERGHIERO E DELLA RISTORAZIONE&quot;
        }, {
          &quot;value&quot; : &quot;513&quot;,
          &quot;label&quot; : &quot;513 - SERVIZI DEI TRASPORTI&quot;
        }, {
          &quot;value&quot; : &quot;514&quot;,
          &quot;label&quot; : &quot;514 - SERVIZI DI LOCAZIONE IMMOBILIARE E AUSILIARI FINANZIARI&quot;
        }, {
          &quot;value&quot; : &quot;515&quot;,
          &quot;label&quot; : &quot;515 - SERVIZI CONNESSI AL TRATTAMENTO DEI RIFIUTI&quot;
        }, {
          &quot;value&quot; : &quot;516&quot;,
          &quot;label&quot; : &quot;516 - SERVIZI SANITARI&quot;
        }, {
          &quot;value&quot; : &quot;517&quot;,
          &quot;label&quot; : &quot;517 - ALTRI SERVIZI DESTINABILI ALLA VENDITA&quot;
        }, {
          &quot;value&quot; : &quot;600&quot;,
          &quot;label&quot; : &quot;600 - FAMIGLIE CONSUMATRICI&quot;
        }, {
          &quot;value&quot; : &quot;601&quot;,
          &quot;label&quot; : &quot;601 - FAMIGLIE PRODUTTRICI&quot;
        }, {
          &quot;value&quot; : &quot;711&quot;,
          &quot;label&quot; : &quot;711 - RESTO DEL MONDO SOCIETA NON FINANZIARIE, FAMIGLIE E AMMINISTRAZIONI PU&quot;
        }, {
          &quot;value&quot; : &quot;712&quot;,
          &quot;label&quot; : &quot;712 - RESTO DEL MONDO SOCIETA BANCARIE&quot;
        }, {
          &quot;value&quot; : &quot;713&quot;,
          &quot;label&quot; : &quot;713 - RESTO DEL MONDO SOCIETA FINANZIARIE&quot;
        }, {
          &quot;value&quot; : &quot;810&quot;,
          &quot;label&quot; : &quot;810 - ALTRI&quot;
        }, {
          &quot;value&quot; : &quot;811&quot;,
          &quot;label&quot; : &quot;811 - ALTRI&quot;
        }, {
          &quot;value&quot; : &quot;812&quot;,
          &quot;label&quot; : &quot;812 - SETTORE NON-PROFIT&quot;
        } ],
        &quot;SAEValuesProd&quot; : [ {
          &quot;value&quot; : &quot;614&quot;,
          &quot;label&quot; : &quot;614 - FAMIGLIE PRODUTTRICI ARTIGIANE&quot;
        }, {
          &quot;value&quot; : &quot;615&quot;,
          &quot;label&quot; : &quot;615 - ALTRE FAMIGLIE PRODUTTRICI&quot;
        } ],
        &quot;SAEValuesCons&quot; : [ {
          &quot;value&quot; : &quot;600&quot;,
          &quot;label&quot; : &quot;600 - FAMIGLIE CONSUMATRICI&quot;
        } ],
        &quot;SAEValues&quot; : [ {
          &quot;label&quot; : &quot;000 - SCONOSCIUTO&quot;,
          &quot;value&quot; : &quot;000&quot;
        }, {
          &quot;label&quot; : &quot;100 - TESORO DELLO STATO&quot;,
          &quot;value&quot; : &quot;100&quot;
        }, {
          &quot;label&quot; : &quot;101 - CASSA DD. PP.&quot;,
          &quot;value&quot; : &quot;101&quot;
        }, {
          &quot;label&quot; : &quot;102 - AMMINISTRAZIONE STATALE E ORGANI COSTITUZIONALI&quot;,
          &quot;value&quot; : &quot;102&quot;
        }, {
          &quot;label&quot; : &quot;120 - AMMINISTRAZIONI REGIONALI&quot;,
          &quot;value&quot; : &quot;120&quot;
        }, {
          &quot;label&quot; : &quot;121 - AMMINISTRAZIONI PROVINCIALI E CITTA METROPOLITANE&quot;,
          &quot;value&quot; : &quot;121&quot;
        }, {
          &quot;label&quot; : &quot;165 - AMMIN.CENTR.ENTI PRODUTTORI SERV.ECON. E REGOL.ATT&quot;,
          &quot;value&quot; : &quot;165&quot;
        }, {
          &quot;label&quot; : &quot;166 - AMMIN.CENTR.ENTI PRODUTTORI SERV.ASSITEN.RICREATIV&quot;,
          &quot;value&quot; : &quot;166&quot;
        }, {
          &quot;label&quot; : &quot;167 - AMMIN.CENTR.ENTI DI RICERCA&quot;,
          &quot;value&quot; : &quot;167&quot;
        }, {
          &quot;label&quot; : &quot;173 - AMMIN.COMUNALI E UNIONI DI COMUNI&quot;,
          &quot;value&quot; : &quot;173&quot;
        }, {
          &quot;label&quot; : &quot;174 - AMMIN.LOCALI ENTI PRODUTTORI DI SERVIZI SANITARI&quot;,
          &quot;value&quot; : &quot;174&quot;
        }, {
          &quot;label&quot; : &quot;175 - AMMIN.LOCALI ALTRI ENTI PRODUTTORI DI SERVIZI SANI&quot;,
          &quot;value&quot; : &quot;175&quot;
        }, {
          &quot;label&quot; : &quot;176 - AMMIN.LOCALI ENTI PRODUTTORI SERV.ECONOMICI E REG.&quot;,
          &quot;value&quot; : &quot;176&quot;
        }, {
          &quot;label&quot; : &quot;177 - AMMIN.LOCALI ENTI PRODUTTORI SERV.ASSIST.RICREATIV&quot;,
          &quot;value&quot; : &quot;177&quot;
        }, {
          &quot;label&quot; : &quot;178 - ALTRI ENTI LOCALI&quot;,
          &quot;value&quot; : &quot;178&quot;
        }, {
          &quot;label&quot; : &quot;191 - ENTI DI PREVIDENZA E ASSISTENZA SOCIALE&quot;,
          &quot;value&quot; : &quot;191&quot;
        }, {
          &quot;label&quot; : &quot;245 - SISTEMA BANCARIO&quot;,
          &quot;value&quot; : &quot;245&quot;
        }, {
          &quot;label&quot; : &quot;247 - FONDI COMUNI DI INVESTIMENTO MONETARIO&quot;,
          &quot;value&quot; : &quot;247&quot;
        }, {
          &quot;label&quot; : &quot;248 - ISTITUTI DI MONETA ELETTRONICA&quot;,
          &quot;value&quot; : &quot;248&quot;
        }, {
          &quot;label&quot; : &quot;249 - SOCIETA VEICOLO&quot;,
          &quot;value&quot; : &quot;249&quot;
        }, {
          &quot;label&quot; : &quot;250 - FONDAZIONI BANCARIE&quot;,
          &quot;value&quot; : &quot;250&quot;
        }, {
          &quot;label&quot; : &quot;251 - CONTROPARTI CENTRALI DI COMPENSAZIONE&quot;,
          &quot;value&quot; : &quot;251&quot;
        }, {
          &quot;label&quot; : &quot;255 - HOLDING FINANZIARIE PUBBLICHE&quot;,
          &quot;value&quot; : &quot;255&quot;
        }, {
          &quot;label&quot; : &quot;256 - HOLDING FINANZIARIE PRIVATE&quot;,
          &quot;value&quot; : &quot;256&quot;
        }, {
          &quot;label&quot; : &quot;257 - MERCHANT BANKS (ARTT. 6 - 7 T.U.B.)&quot;,
          &quot;value&quot; : &quot;257&quot;
        }, {
          &quot;label&quot; : &quot;258 - SOCIETA DI LEASING (ARTT. 6 - 7 T.U.B.)&quot;,
          &quot;value&quot; : &quot;258&quot;
        }, {
          &quot;label&quot; : &quot;259 - SOCIETA DI FACTORING (ARTT. 6 - 7 T.U.B.)&quot;,
          &quot;value&quot; : &quot;259&quot;
        }, {
          &quot;label&quot; : &quot;263 - SOCIETA DI CREDITO AL CONSUMO (ARTT. 6 - 7 T.U.B.&quot;,
          &quot;value&quot; : &quot;263&quot;
        }, {
          &quot;label&quot; : &quot;264 - SOCIETA DI INTERMEDIAZIONE MOBILIARE (SIM)&quot;,
          &quot;value&quot; : &quot;264&quot;
        }, {
          &quot;label&quot; : &quot;265 - SOCIETA FIDUCIARIE DI GESTIONE&quot;,
          &quot;value&quot; : &quot;265&quot;
        }, {
          &quot;label&quot; : &quot;266 - FONDI COMUNI INVEST.MOB. E SOC.INVEST.CAPIT.VAR(SICAV)/FISSO(SICAF)&quot;,
          &quot;value&quot; : &quot;266&quot;
        }, {
          &quot;label&quot; : &quot;267 - ALTRI ORGANISMI DI INVESTIMENTO COLLETTIVO DEL RIS&quot;,
          &quot;value&quot; : &quot;267&quot;
        }, {
          &quot;label&quot; : &quot;268 - ALTRE FINANZIARIE&quot;,
          &quot;value&quot; : &quot;268&quot;
        }, {
          &quot;label&quot; : &quot;269 - IMPRESE DI INVESTIMENTO SISTEMICHE&quot;,
          &quot;value&quot; : &quot;269&quot;
        }, {
          &quot;label&quot; : &quot;270 - SOCIETA DI GESTIONE DI FONDI&quot;,
          &quot;value&quot; : &quot;270&quot;
        }, {
          &quot;label&quot; : &quot;273 - SOCIETA FIDUCIARIE DI AMMINISTRAZIONE&quot;,
          &quot;value&quot; : &quot;273&quot;
        }, {
          &quot;label&quot; : &quot;274 - ENTI PREPOSTI AL FUNZIONAMENTO DEI MERCATI&quot;,
          &quot;value&quot; : &quot;274&quot;
        }, {
          &quot;label&quot; : &quot;275 - ENTI PREPOSTI AL FUNZIONAMENTO DEI MERCATI&quot;,
          &quot;value&quot; : &quot;275&quot;
        }, {
          &quot;label&quot; : &quot;276 - AGENTI DI CAMBIO (utilizzabile solo da Direzione)&quot;,
          &quot;value&quot; : &quot;276&quot;
        }, {
          &quot;label&quot; : &quot;278 - ASSOCIAZIONI TRA IMPRESE FINANZIARIE E ASSICURATIV&quot;,
          &quot;value&quot; : &quot;278&quot;
        }, {
          &quot;label&quot; : &quot;279 - AUTORITA CENTRALI DI CONTROLLO&quot;,
          &quot;value&quot; : &quot;279&quot;
        }, {
          &quot;label&quot; : &quot;280 - MEDIATORI AGENTI E CONSULENTI DI ASSICURAZIONE&quot;,
          &quot;value&quot; : &quot;280&quot;
        }, {
          &quot;label&quot; : &quot;283 - PROMOTORI FINANZIARI&quot;,
          &quot;value&quot; : &quot;283&quot;
        }, {
          &quot;label&quot; : &quot;284 - ALTRI AUSILIARI FINANZIARI&quot;,
          &quot;value&quot; : &quot;284&quot;
        }, {
          &quot;label&quot; : &quot;285 - HOLDING OPERATIVE FINANZIARIE&quot;,
          &quot;value&quot; : &quot;285&quot;
        }, {
          &quot;label&quot; : &quot;287 - SOCIETA DI PARTECIPAZIONE (HOLDING) DI GRUPPI FINANZIARI&quot;,
          &quot;value&quot; : &quot;287&quot;
        }, {
          &quot;label&quot; : &quot;288 - SOCIETA DI PARTECIPAZIONE (HOLDING) DI GRUPPI NON FINANZIARI&quot;,
          &quot;value&quot; : &quot;288&quot;
        }, {
          &quot;label&quot; : &quot;289 - ISTITUZIONI CAPTIVE DIVERSE DALLE HOLDING DI PARTECIPAZIONE&quot;,
          &quot;value&quot; : &quot;289&quot;
        }, {
          &quot;label&quot; : &quot;290 - SOCIETA DI PARTECIPAZIONE (HOLDING) DI GRUPPI FINANZIARI E NON&quot;,
          &quot;value&quot; : &quot;290&quot;
        }, {
          &quot;label&quot; : &quot;294 - IMPRESE DI ASSICURAZIONE&quot;,
          &quot;value&quot; : &quot;294&quot;
        }, {
          &quot;label&quot; : &quot;295 - FONDI PENSIONE (REGISTRO COVIP)&quot;,
          &quot;value&quot; : &quot;295&quot;
        }, {
          &quot;label&quot; : &quot;296 - ALTRI FONDI PREVIDENZIALI&quot;,
          &quot;value&quot; : &quot;296&quot;
        }, {
          &quot;label&quot; : &quot;300 - BANCA D ITALIA&quot;,
          &quot;value&quot; : &quot;300&quot;
        }, {
          &quot;label&quot; : &quot;329 - ASSOCIAZIONI BANCARIE&quot;,
          &quot;value&quot; : &quot;329&quot;
        }, {
          &quot;label&quot; : &quot;430 - IMPRESE PRODUTTIVE PRIVATE&quot;,
          &quot;value&quot; : &quot;430&quot;
        }, {
          &quot;label&quot; : &quot;431 - HOLDING PRIVATE&quot;,
          &quot;value&quot; : &quot;431&quot;
        }, {
          &quot;label&quot; : &quot;432 - HOLDING OPERATIVE PRIVATE&quot;,
          &quot;value&quot; : &quot;432&quot;
        }, {
          &quot;label&quot; : &quot;450 - ASSOCIAZIONI FRA IMPRESE NON FINANZIARIE&quot;,
          &quot;value&quot; : &quot;450&quot;
        }, {
          &quot;label&quot; : &quot;470 - AZIENDE MUNICIPALIZZATE, PROVINCIALIZZATE E REGION&quot;,
          &quot;value&quot; : &quot;470&quot;
        }, {
          &quot;label&quot; : &quot;471 - IMPRESE PARTECIPATE DALLO STATO&quot;,
          &quot;value&quot; : &quot;471&quot;
        }, {
          &quot;label&quot; : &quot;472 - IMPRESE A PARTECIPAZIONE REGIONALE O LOCALE&quot;,
          &quot;value&quot; : &quot;472&quot;
        }, {
          &quot;label&quot; : &quot;473 - ALTRE UNITA PUBBLICHE&quot;,
          &quot;value&quot; : &quot;473&quot;
        }, {
          &quot;label&quot; : &quot;474 - HOLDING PUBBLICHE&quot;,
          &quot;value&quot; : &quot;474&quot;
        }, {
          &quot;label&quot; : &quot;475 - IMPRESE CONTROLLATE DALLE AMMINISTRAZIONI CENTRALI&quot;,
          &quot;value&quot; : &quot;475&quot;
        }, {
          &quot;label&quot; : &quot;476 - IMPRESE CONTROLLATE DA AMMINISTRAZIONI LOCALI&quot;,
          &quot;value&quot; : &quot;476&quot;
        }, {
          &quot;label&quot; : &quot;477 - IMPRESE CONTROLLATE DA ALTRE AMMINISTRAZIONI PUBBLICHE&quot;,
          &quot;value&quot; : &quot;477&quot;
        }, {
          &quot;label&quot; : &quot;480 - UNITA O SOCIETA NON FINANZ.ARTIGIANE CON 20 O PI&quot;,
          &quot;value&quot; : &quot;480&quot;
        }, {
          &quot;label&quot; : &quot;481 - UNITA O SOCIETA NON FINANZ.  ARTIGIANE CON PIU&quot;,
          &quot;value&quot; : &quot;481&quot;
        }, {
          &quot;label&quot; : &quot;482 - SOCIETA NON FINANZ.ARTIGIANE CON MENO DI 20 ADDET&quot;,
          &quot;value&quot; : &quot;482&quot;
        }, {
          &quot;label&quot; : &quot;490 - UNITA O SOCIETA NON FINANZ.ALTRE CON 20 O PIU A&quot;,
          &quot;value&quot; : &quot;490&quot;
        }, {
          &quot;label&quot; : &quot;491 - UNITA O SOCIETA NON FINANZ.ALTRE CON PIU DI 5 E&quot;,
          &quot;value&quot; : &quot;491&quot;
        }, {
          &quot;label&quot; : &quot;492 - SOCIETA NON FINANZ.ALTRE CON MENO DI 20 ADDETTI&quot;,
          &quot;value&quot; : &quot;492&quot;
        }, {
          &quot;label&quot; : &quot;500 - ISTITUZIONI ED ENTI ECCLESIATICI E RELIGIOSI&quot;,
          &quot;value&quot; : &quot;500&quot;
        }, {
          &quot;label&quot; : &quot;501 - ISTIT.ED ENTI ASSIST.BENEF.ISTRUZ.CULTUR.SINDA.POL&quot;,
          &quot;value&quot; : &quot;501&quot;
        }, {
          &quot;label&quot; : &quot;551 - UNITA NON CLASSIFICABILI&quot;,
          &quot;value&quot; : &quot;551&quot;
        }, {
          &quot;label&quot; : &quot;552 - UNITA NON CLASSIFICATE&quot;,
          &quot;value&quot; : &quot;552&quot;
        }, {
          &quot;label&quot; : &quot;600 - FAMIGLIE CONSUMATRICI&quot;,
          &quot;value&quot; : &quot;600&quot;
        }, {
          &quot;label&quot; : &quot;614 - FAMIGLIE PRODUTTRICI ARTIGIANE&quot;,
          &quot;value&quot; : &quot;614&quot;
        }, {
          &quot;label&quot; : &quot;615 - ALTRE FAMIGLIE PRODUTTRICI&quot;,
          &quot;value&quot; : &quot;615&quot;
        }, {
          &quot;label&quot; : &quot;704 - AMMINISTRAZIONI CENTRALI DEI PAESI UE MEMBRI DELL&quot;,
          &quot;value&quot; : &quot;704&quot;
        }, {
          &quot;label&quot; : &quot;705 - AMMINISTRAZIONI CENTRALI DEI PAESI UE NON MEMBRI D&quot;,
          &quot;value&quot; : &quot;705&quot;
        }, {
          &quot;label&quot; : &quot;706 - AMMINISTRAZIONI DI STATI FEDERATI DEI PAESI UE MEM&quot;,
          &quot;value&quot; : &quot;706&quot;
        }, {
          &quot;label&quot; : &quot;707 - AMMINISTRAZIONI DI STATI FEDERATI DEI PAESI UE NON&quot;,
          &quot;value&quot; : &quot;707&quot;
        }, {
          &quot;label&quot; : &quot;708 - AMMINISTRAZIONI LOCALI DEI PAESI UE MEMBRI DELL UM&quot;,
          &quot;value&quot; : &quot;708&quot;
        }, {
          &quot;label&quot; : &quot;709 - AMMINISTRAZIONI LOCALI DEI PAESI UE NON MEMBRI DEL&quot;,
          &quot;value&quot; : &quot;709&quot;
        }, {
          &quot;label&quot; : &quot;713 - ENTI DI ASSISTENZA E PREVIDENZA SOCIALE DEI PAESI&quot;,
          &quot;value&quot; : &quot;713&quot;
        }, {
          &quot;label&quot; : &quot;714 - ENTI DI ASSISTENZA E PREVIDENZA SOCIALE DEI PAESI&quot;,
          &quot;value&quot; : &quot;714&quot;
        }, {
          &quot;label&quot; : &quot;715 - AMMINISTRAZ.PUBBL. E ENTI DI ASSISTENZA E PREVIDENZA PAESI EXTRA UE&quot;,
          &quot;value&quot; : &quot;715&quot;
        }, {
          &quot;label&quot; : &quot;717 - ALTRE SOC.FINANZ.-SOC.VEICOLO PAESI UE MEMBRI UM&quot;,
          &quot;value&quot; : &quot;717&quot;
        }, {
          &quot;label&quot; : &quot;718 - ALTRE SOC.FINANZ.-SOC.VEICOLO PAESI UE NON MEMBRI UM&quot;,
          &quot;value&quot; : &quot;718&quot;
        }, {
          &quot;label&quot; : &quot;719 - IMPRESE INVESTIMENTO SISTEMICHE DEI PAESI UE MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;719&quot;
        }, {
          &quot;label&quot; : &quot;723 - IMPRESE INVESTIMENTO SISTEMICHE DEI PAESI UE NON MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;723&quot;
        }, {
          &quot;label&quot; : &quot;724 - AUTORITA BANCARIE CENTRALI PAESI UE MEMBRI DELL U&quot;,
          &quot;value&quot; : &quot;724&quot;
        }, {
          &quot;label&quot; : &quot;725 - AUTORITA BANCARIE CENTRALI PAESI UE NON MEMBRI DE&quot;,
          &quot;value&quot; : &quot;725&quot;
        }, {
          &quot;label&quot; : &quot;726 - AUTORITA BANCARIE CENTRALI DEI PAESI EXTRA UE&quot;,
          &quot;value&quot; : &quot;726&quot;
        }, {
          &quot;label&quot; : &quot;727 - SISTEMA BANCARIO PAESI UE MEMBRI DELL UM&quot;,
          &quot;value&quot; : &quot;727&quot;
        }, {
          &quot;label&quot; : &quot;728 - SISTEMA BANCARIO PAESI UE NON MEMBRI DELL UM&quot;,
          &quot;value&quot; : &quot;728&quot;
        }, {
          &quot;label&quot; : &quot;729 - SISTEMA BANCARIO DEI PAESI EXTRA UE&quot;,
          &quot;value&quot; : &quot;729&quot;
        }, {
          &quot;label&quot; : &quot;733 - ALTRE ISTITUZIONI FINANZ.MONETARIE PAESI UE MEMBRI&quot;,
          &quot;value&quot; : &quot;733&quot;
        }, {
          &quot;label&quot; : &quot;734 - ALTRE ISTITUZIONI FINANZ.MONETARIE PAESI UE NON ME&quot;,
          &quot;value&quot; : &quot;734&quot;
        }, {
          &quot;label&quot; : &quot;735 - ALTRE ISTITUZIONI FINANZ.MONETARIE PAESI NON UE&quot;,
          &quot;value&quot; : &quot;735&quot;
        }, {
          &quot;label&quot; : &quot;739 - ALTRI INTERMEDIARI FINANZIARI PAESI UE MEMBRI DELL&quot;,
          &quot;value&quot; : &quot;739&quot;
        }, {
          &quot;label&quot; : &quot;743 - ALTRI INTERMEDIARI FINANZIARI PAESI UE NON MEMBRI&quot;,
          &quot;value&quot; : &quot;743&quot;
        }, {
          &quot;label&quot; : &quot;744 - IMPRESE ASSICURAZIONI PAESI UE MEMBRI DELL UM&quot;,
          &quot;value&quot; : &quot;744&quot;
        }, {
          &quot;label&quot; : &quot;745 - IMPRESE ASSICURAZIONI PAESI UE NON MEMBRI DELL UM&quot;,
          &quot;value&quot; : &quot;745&quot;
        }, {
          &quot;label&quot; : &quot;746 - AUSILIARI FINANZIARI PAESI UE MEMBRI DELL UM&quot;,
          &quot;value&quot; : &quot;746&quot;
        }, {
          &quot;label&quot; : &quot;747 - AUSILIARI FINANZIARI PAESI UE NON MEMBRI DELL UM&quot;,
          &quot;value&quot; : &quot;747&quot;
        }, {
          &quot;label&quot; : &quot;748 - ALTRI INTERMEDIARI FINANZIARI PAESI NON UE&quot;,
          &quot;value&quot; : &quot;748&quot;
        }, {
          &quot;label&quot; : &quot;753 - FONDI COMUNI MONETARI DEI PAESI UE MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;753&quot;
        }, {
          &quot;label&quot; : &quot;754 - FONDI COMUNI MONETARI DEI PAESI UE NON MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;754&quot;
        }, {
          &quot;label&quot; : &quot;755 - FONDI COMUNI MONETARI DEI PAESI EXTRA UE&quot;,
          &quot;value&quot; : &quot;755&quot;
        }, {
          &quot;label&quot; : &quot;756 - ALTRE ISTITUZ.FINANZIARIE MONETARIE PAESI UE MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;756&quot;
        }, {
          &quot;label&quot; : &quot;757 - SOCIETA NON FINANZIARIE DEI PAESI UE MEMBRI DELL&quot;,
          &quot;value&quot; : &quot;757&quot;
        }, {
          &quot;label&quot; : &quot;758 - SOCIETA NON FINANZIARIE DEI PAESI UE NON MEMBRI D&quot;,
          &quot;value&quot; : &quot;758&quot;
        }, {
          &quot;label&quot; : &quot;759 - SOCIETA NON FINANZIARIE DI PAESI EXTRA UE&quot;,
          &quot;value&quot; : &quot;759&quot;
        }, {
          &quot;label&quot; : &quot;763 - ALTRE ISTITUZ.FINANZIARIE MONETARIE PAESI UE NO MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;763&quot;
        }, {
          &quot;label&quot; : &quot;764 - ALTRI ISTITUZIONI FINANZIARIE MONETARIE DEI PAESI EXTRA UE&quot;,
          &quot;value&quot; : &quot;764&quot;
        }, {
          &quot;label&quot; : &quot;765 - FONDI COMUNI NON MONETARI PAESI UE MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;765&quot;
        }, {
          &quot;label&quot; : &quot;766 - FONDI COMUNI NON MONETARI PAESI UE NO MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;766&quot;
        }, {
          &quot;label&quot; : &quot;767 - FONDI COMUNI NON MONETARI DEI PAESI EXTRA UE&quot;,
          &quot;value&quot; : &quot;767&quot;
        }, {
          &quot;label&quot; : &quot;768 - FAMIGLIE PRODUTTRICI DEI PAESI UE MEMBRI DELL UM&quot;,
          &quot;value&quot; : &quot;768&quot;
        }, {
          &quot;label&quot; : &quot;769 - FAMIGLIE PRODUTTRICI DEI PAESI UE NON MEMBRI DELL&quot;,
          &quot;value&quot; : &quot;769&quot;
        }, {
          &quot;label&quot; : &quot;770 - ISTITUZIONI DELL UE&quot;,
          &quot;value&quot; : &quot;770&quot;
        }, {
          &quot;label&quot; : &quot;771 - ALTRI ORGANISMI INTERNAZIONALI&quot;,
          &quot;value&quot; : &quot;771&quot;
        }, {
          &quot;label&quot; : &quot;772 - FAMIGLIE PRODUTTRICI DI PAESI EXTRA UE&quot;,
          &quot;value&quot; : &quot;772&quot;
        }, {
          &quot;label&quot; : &quot;773 - FAMIGLIE CONSUMATRICI DEI PAESI UE MEMBRI DELL UM&quot;,
          &quot;value&quot; : &quot;773&quot;
        }, {
          &quot;label&quot; : &quot;774 - FAMIGLIE CONSUMATRICI DEI PAESI UE NON MEMBRI DELL&quot;,
          &quot;value&quot; : &quot;774&quot;
        }, {
          &quot;label&quot; : &quot;775 - FAMIGLIE CONSUMATRICI DI PAESI EXTRA UE&quot;,
          &quot;value&quot; : &quot;775&quot;
        }, {
          &quot;label&quot; : &quot;776 - ALTRI INTERM.FIN.PAESI UE AREA EURO NO SOC.VEIC.E IMPR.INV.SIST.&quot;,
          &quot;value&quot; : &quot;776&quot;
        }, {
          &quot;label&quot; : &quot;778 - ALTRI INTERM.FIN.PAESI UE NO AREA EURO NO SOC.VEIC.E IMPR.INV.SIST.&quot;,
          &quot;value&quot; : &quot;778&quot;
        }, {
          &quot;label&quot; : &quot;779 - IMPRESE DI ASSICURAZIONE PAESI UE MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;779&quot;
        }, {
          &quot;label&quot; : &quot;782 - FONDI PENSIONE DEI PAESI UE MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;782&quot;
        }, {
          &quot;label&quot; : &quot;783 - IST.SENZA SCOPO DI LUCRO PER LE FAMIGLIE PAESI UE&quot;,
          &quot;value&quot; : &quot;783&quot;
        }, {
          &quot;label&quot; : &quot;784 - IST.SENZA SCOPO DI LUCRO PER LE FAMIGLIE PAESI UE&quot;,
          &quot;value&quot; : &quot;784&quot;
        }, {
          &quot;label&quot; : &quot;785 - ISTITUZ.SENZA SCOPO DI LUCRO AL SERVIZIO DI FAMIGLIE PAESI EXTRA UE&quot;,
          &quot;value&quot; : &quot;785&quot;
        }, {
          &quot;label&quot; : &quot;790 - IMPRESE ASSICURAZIONE PAESI UE NO MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;790&quot;
        }, {
          &quot;label&quot; : &quot;791 - BANCA CENTRALE EUROPEA&quot;,
          &quot;value&quot; : &quot;791&quot;
        }, {
          &quot;label&quot; : &quot;794 - RAPPRESENTANZE ESTERE&quot;,
          &quot;value&quot; : &quot;794&quot;
        }, {
          &quot;label&quot; : &quot;800 - FONDI PENSIONE PAESI UE NO MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;800&quot;
        }, {
          &quot;label&quot; : &quot;801 - ALTRE SOCIETA FINANZIARIE DI PAESI EXTRA UE&quot;,
          &quot;value&quot; : &quot;801&quot;
        }, {
          &quot;label&quot; : &quot;802 - HOLDING DI PARTECIPAZIONE DEI PAESI UE MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;802&quot;
        }, {
          &quot;label&quot; : &quot;803 - HOLDING DI PARTECIPAZIONE DEI PAESI UE NON MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;803&quot;
        }, {
          &quot;label&quot; : &quot;804 - HOLDING OPERATIVE DEI PAESI UE MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;804&quot;
        }, {
          &quot;label&quot; : &quot;805 - HOLDING OPERATIVE DEI PAESI UE NON MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;805&quot;
        }, {
          &quot;label&quot; : &quot;806 - ISTITUZ.CAPTIVE NO HOLDING DI PARTECIPAZ.PAESI UE AREA EURO&quot;,
          &quot;value&quot; : &quot;806&quot;
        }, {
          &quot;label&quot; : &quot;807 - ISTITUZ.CAPTIVE NO HOLDING DI PARTECIPAZ.PAESI UE NO AREA EURO&quot;,
          &quot;value&quot; : &quot;807&quot;
        }, {
          &quot;label&quot; : &quot;808 - ALTRI AUSILIARI FINANZIARI DEI PAESI UE MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;808&quot;
        }, {
          &quot;label&quot; : &quot;809 - ALTRI AUSILIARI FINANZIARI DEI PAESI UE NO MEMBRI AREA EURO&quot;,
          &quot;value&quot; : &quot;809&quot;
        } ],
        &quot;ATECOValues&quot; : [ {
          &quot;label&quot; : &quot;000 - SCONOSCIUTO&quot;,
          &quot;value&quot; : &quot;000&quot;
        }, {
          &quot;label&quot; : &quot;011 - Coltivazione di ortaggi e meloni. radici e tuberi&quot;,
          &quot;value&quot; : &quot;011&quot;
        }, {
          &quot;label&quot; : &quot;012 - Coltivazione di altri alberi da frutta. frutti di bosco e in guscio&quot;,
          &quot;value&quot; : &quot;012&quot;
        }, {
          &quot;label&quot; : &quot;013 - Riproduzione delle piante&quot;,
          &quot;value&quot; : &quot;013&quot;
        }, {
          &quot;label&quot; : &quot;014 - Allevamento di altri bovini e di bufalini&quot;,
          &quot;value&quot; : &quot;014&quot;
        }, {
          &quot;label&quot; : &quot;015 - Coltivazioni agricole associate allallevamento di animali: attività mista&quot;,
          &quot;value&quot; : &quot;015&quot;
        }, {
          &quot;label&quot; : &quot;016 - Attività di supporto alla produzione vegetale&quot;,
          &quot;value&quot; : &quot;016&quot;
        }, {
          &quot;label&quot; : &quot;017 - Caccia. cattura di animali e servizi connessi&quot;,
          &quot;value&quot; : &quot;017&quot;
        }, {
          &quot;label&quot; : &quot;021 - Silvicoltura ed altre attività forestali&quot;,
          &quot;value&quot; : &quot;021&quot;
        }, {
          &quot;label&quot; : &quot;022 - Utilizzo di aree forestali&quot;,
          &quot;value&quot; : &quot;022&quot;
        }, {
          &quot;label&quot; : &quot;023 - Raccolta di prodotti selvatici non legnosi&quot;,
          &quot;value&quot; : &quot;023&quot;
        }, {
          &quot;label&quot; : &quot;024 - Servizi di supporto per la silvicoltura&quot;,
          &quot;value&quot; : &quot;024&quot;
        }, {
          &quot;label&quot; : &quot;031 - Pesca marina&quot;,
          &quot;value&quot; : &quot;031&quot;
        }, {
          &quot;label&quot; : &quot;032 - Acquacoltura in acque dolci&quot;,
          &quot;value&quot; : &quot;032&quot;
        }, {
          &quot;label&quot; : &quot;051 - Estrazione di antracite&quot;,
          &quot;value&quot; : &quot;051&quot;
        }, {
          &quot;label&quot; : &quot;052 - Estrazione di lignite&quot;,
          &quot;value&quot; : &quot;052&quot;
        }, {
          &quot;label&quot; : &quot;061 - ESTRAZIONE DI PETROLIO GREGGIO&quot;,
          &quot;value&quot; : &quot;061&quot;
        }, {
          &quot;label&quot; : &quot;062 - Estrazione di gas naturale&quot;,
          &quot;value&quot; : &quot;062&quot;
        }, {
          &quot;label&quot; : &quot;071 - Estrazione di minerali metalliferi ferrosi&quot;,
          &quot;value&quot; : &quot;071&quot;
        }, {
          &quot;label&quot; : &quot;072 - ESTRAZIONE DI MINERALI METALLIFERI NON FERROSI&quot;,
          &quot;value&quot; : &quot;072&quot;
        }, {
          &quot;label&quot; : &quot;081 - Estrazione di pietre ornamentali e da costruzione. calcare. pietra da gesso. creta e ardesia&quot;,
          &quot;value&quot; : &quot;081&quot;
        }, {
          &quot;label&quot; : &quot;089 - ESTRAZIONE DI MINERALI DA CAVE E MINIERE NCA&quot;,
          &quot;value&quot; : &quot;089&quot;
        }, {
          &quot;label&quot; : &quot;091 - Attività di supporto allestrazione di petrolio e di gas naturale&quot;,
          &quot;value&quot; : &quot;091&quot;
        }, {
          &quot;label&quot; : &quot;099 - Attività di supporto per lestrazione da cave e miniere di altri minerali&quot;,
          &quot;value&quot; : &quot;099&quot;
        }, {
          &quot;label&quot; : &quot;101 - Lavorazione e conservazione di carne (escluso volatili)&quot;,
          &quot;value&quot; : &quot;101&quot;
        }, {
          &quot;label&quot; : &quot;102 - Lavorazione e conservazione di pesce. crostacei e molluschi&quot;,
          &quot;value&quot; : &quot;102&quot;
        }, {
          &quot;label&quot; : &quot;103 - Lavorazione e conservazione delle patate&quot;,
          &quot;value&quot; : &quot;103&quot;
        }, {
          &quot;label&quot; : &quot;104 - Produzione di margarina e di grassi commestibili simili&quot;,
          &quot;value&quot; : &quot;104&quot;
        }, {
          &quot;label&quot; : &quot;105 - Industria lattiero-casearia. trattamento igienico. conservazione del latte&quot;,
          &quot;value&quot; : &quot;105&quot;
        }, {
          &quot;label&quot; : &quot;106 - LAVORAZIONE DELLE GRANAGLIE. PRODUZIONE DI AMIDI E DI PRODOTTI AMIDACEI&quot;,
          &quot;value&quot; : &quot;106&quot;
        }, {
          &quot;label&quot; : &quot;107 - Produzione di fette biscottate e di biscotti&quot;,
          &quot;value&quot; : &quot;107&quot;
        }, {
          &quot;label&quot; : &quot;108 - Produzione di cacao. cioccolato. caramelle e confetterie&quot;,
          &quot;value&quot; : &quot;108&quot;
        }, {
          &quot;label&quot; : &quot;109 - PRODUZIONE DI PRODOTTI PER LALIMENTAZIONE DEGLI ANIMALI&quot;,
          &quot;value&quot; : &quot;109&quot;
        }, {
          &quot;label&quot; : &quot;110 - Produzione di vini da uve&quot;,
          &quot;value&quot; : &quot;110&quot;
        }, {
          &quot;label&quot; : &quot;120 - INDUSTRIA DEL TABACCO&quot;,
          &quot;value&quot; : &quot;120&quot;
        }, {
          &quot;label&quot; : &quot;131 - Preparazione e filatura di fibre tessili&quot;,
          &quot;value&quot; : &quot;131&quot;
        }, {
          &quot;label&quot; : &quot;132 - Tessitura&quot;,
          &quot;value&quot; : &quot;132&quot;
        }, {
          &quot;label&quot; : &quot;133 - Finissaggio dei tessili&quot;,
          &quot;value&quot; : &quot;133&quot;
        }, {
          &quot;label&quot; : &quot;139 - Fabbricazione di tessuti a maglia&quot;,
          &quot;value&quot; : &quot;139&quot;
        }, {
          &quot;label&quot; : &quot;141 - CONFEZIONE DI ARTICOLI DI ABBIGLIAMENTO (ESCLUSO ABBIGLIAMENTO IN PELLICCIA)&quot;,
          &quot;value&quot; : &quot;141&quot;
        }, {
          &quot;label&quot; : &quot;142 - Confezione di articoli in pelliccia&quot;,
          &quot;value&quot; : &quot;142&quot;
        }, {
          &quot;label&quot; : &quot;143 - Fabbricazione di altri articoli di maglieria&quot;,
          &quot;value&quot; : &quot;143&quot;
        }, {
          &quot;label&quot; : &quot;151 - Preparazione e concia del cuoio&quot;,
          &quot;value&quot; : &quot;151&quot;
        }, {
          &quot;label&quot; : &quot;152 - Fabbricazione di calzature&quot;,
          &quot;value&quot; : &quot;152&quot;
        }, {
          &quot;label&quot; : &quot;161 - Taglio e piallatura del legno&quot;,
          &quot;value&quot; : &quot;161&quot;
        }, {
          &quot;label&quot; : &quot;162 - Fabbricazione di altri prodotti in legno. sughero. paglia e materiali da intreccio&quot;,
          &quot;value&quot; : &quot;162&quot;
        }, {
          &quot;label&quot; : &quot;171 - FABBRICAZIONE DI PASTA-CARTA. CARTA E CARTONE&quot;,
          &quot;value&quot; : &quot;171&quot;
        }, {
          &quot;label&quot; : &quot;172 - FABBRICAZIONE DI ARTICOLI DI CARTA E CARTONE&quot;,
          &quot;value&quot; : &quot;172&quot;
        }, {
          &quot;label&quot; : &quot;181 - Stampa di giornali&quot;,
          &quot;value&quot; : &quot;181&quot;
        }, {
          &quot;label&quot; : &quot;182 - Riproduzione di supporti registrati&quot;,
          &quot;value&quot; : &quot;182&quot;
        }, {
          &quot;label&quot; : &quot;191 - Fabbricazione di prodotti di cokeria&quot;,
          &quot;value&quot; : &quot;191&quot;
        }, {
          &quot;label&quot; : &quot;192 - Fabbricazione di prodotti derivanti dalla raffinazione del petrolio&quot;,
          &quot;value&quot; : &quot;192&quot;
        }, {
          &quot;label&quot; : &quot;201 - Fabbricazione di fertilizzanti e composti azotati&quot;,
          &quot;value&quot; : &quot;201&quot;
        }, {
          &quot;label&quot; : &quot;202 - Fabbricazione di agrofarmaci e di altri prodotti chimici per lagricoltura&quot;,
          &quot;value&quot; : &quot;202&quot;
        }, {
          &quot;label&quot; : &quot;203 - Fabbricazione di pitture. vernici e smalti. inchiostri da stampa e adesivi sintetici (mastici)&quot;,
          &quot;value&quot; : &quot;203&quot;
        }, {
          &quot;label&quot; : &quot;204 - Fabbricazione di profumi e cosmetici&quot;,
          &quot;value&quot; : &quot;204&quot;
        }, {
          &quot;label&quot; : &quot;205 - Fabbricazione di prodotti chimici nca&quot;,
          &quot;value&quot; : &quot;205&quot;
        }, {
          &quot;label&quot; : &quot;206 - Fabbricazione di fibre sintetiche e artificiali&quot;,
          &quot;value&quot; : &quot;206&quot;
        }, {
          &quot;label&quot; : &quot;211 - Fabbricazione di prodotti farmaceutici di base&quot;,
          &quot;value&quot; : &quot;211&quot;
        }, {
          &quot;label&quot; : &quot;212 - Fabbricazione di medicinali e preparati farmaceutici&quot;,
          &quot;value&quot; : &quot;212&quot;
        }, {
          &quot;label&quot; : &quot;221 - FABBRICAZIONE DI ARTICOLI IN GOMMA&quot;,
          &quot;value&quot; : &quot;221&quot;
        }, {
          &quot;label&quot; : &quot;222 - Fabbricazione di altri articoli in materie plastiche&quot;,
          &quot;value&quot; : &quot;222&quot;
        }, {
          &quot;label&quot; : &quot;231 - Fabbricazione di vetro piano&quot;,
          &quot;value&quot; : &quot;231&quot;
        }, {
          &quot;label&quot; : &quot;232 - Fabbricazione di prodotti refrattari&quot;,
          &quot;value&quot; : &quot;232&quot;
        }, {
          &quot;label&quot; : &quot;233 - Fabbricazione di mattoni. tegole ed altri prodotti per ledilizia in terracotta&quot;,
          &quot;value&quot; : &quot;233&quot;
        }, {
          &quot;label&quot; : &quot;234 - Fabbricazione di altri prodotti in ceramica&quot;,
          &quot;value&quot; : &quot;234&quot;
        }, {
          &quot;label&quot; : &quot;235 - Produzione di cemento&quot;,
          &quot;value&quot; : &quot;235&quot;
        }, {
          &quot;label&quot; : &quot;236 - Fabbricazione di prodotti in fibrocemento&quot;,
          &quot;value&quot; : &quot;236&quot;
        }, {
          &quot;label&quot; : &quot;237 - Taglio. modellatura e finitura di pietre&quot;,
          &quot;value&quot; : &quot;237&quot;
        }, {
          &quot;label&quot; : &quot;239 - Produzione di prodotti abrasivi&quot;,
          &quot;value&quot; : &quot;239&quot;
        }, {
          &quot;label&quot; : &quot;241 - SIDERURGIA&quot;,
          &quot;value&quot; : &quot;241&quot;
        }, {
          &quot;label&quot; : &quot;242 - FABBRICAZIONE DI TUBI. CONDOTTI. PROFILATI CAVI E RELATIVI ACCESSORI IN ACCIAIO (ESCLUSI QUELLI IN ACCIAIO COLATO)&quot;,
          &quot;value&quot; : &quot;242&quot;
        }, {
          &quot;label&quot; : &quot;243 - Profilatura mediante formatura o piegatura a freddo&quot;,
          &quot;value&quot; : &quot;243&quot;
        }, {
          &quot;label&quot; : &quot;244 - Produzione di piombo. zinco e stagno e semilavorati&quot;,
          &quot;value&quot; : &quot;244&quot;
        }, {
          &quot;label&quot; : &quot;245 - Fusione di acciaio&quot;,
          &quot;value&quot; : &quot;245&quot;
        }, {
          &quot;label&quot; : &quot;251 - Fabbricazione di strutture metalliche e di parti di strutture&quot;,
          &quot;value&quot; : &quot;251&quot;
        }, {
          &quot;label&quot; : &quot;252 - Fabbricazione di radiatori e contenitori in metallo per caldaie per il riscaldamento centrale&quot;,
          &quot;value&quot; : &quot;252&quot;
        }, {
          &quot;label&quot; : &quot;253 - Fabbricazione di generatori di vapore (esclusi i contenitori in metallo per caldaie per il riscaldamento centrale ad acqua calda)&quot;,
          &quot;value&quot; : &quot;253&quot;
        }, {
          &quot;label&quot; : &quot;254 - FABBRICAZIONE DI ARMI E MUNIZIONI&quot;,
          &quot;value&quot; : &quot;254&quot;
        }, {
          &quot;label&quot; : &quot;255 - Fucinatura. imbutitura. stampaggio e profilatura dei metalli&quot;,
          &quot;value&quot; : &quot;255&quot;
        }, {
          &quot;label&quot; : &quot;256 - Lavori di meccanica generale&quot;,
          &quot;value&quot; : &quot;256&quot;
        }, {
          &quot;label&quot; : &quot;257 - FABBRICAZIONE DI ARTICOLI DI COLTELLERIA. UTENSILI E OGGETTI DI FERRAMENTA&quot;,
          &quot;value&quot; : &quot;257&quot;
        }, {
          &quot;label&quot; : &quot;259 - Fabbricazione di imballaggi leggeri in metallo&quot;,
          &quot;value&quot; : &quot;259&quot;
        }, {
          &quot;label&quot; : &quot;261 - Fabbricazione di schede elettroniche assemblate&quot;,
          &quot;value&quot; : &quot;261&quot;
        }, {
          &quot;label&quot; : &quot;262 - Fabbricazione di computer e unità periferiche&quot;,
          &quot;value&quot; : &quot;262&quot;
        }, {
          &quot;label&quot; : &quot;263 - Fabbricazione di apparecchiature per le telecomunicazioni&quot;,
          &quot;value&quot; : &quot;263&quot;
        }, {
          &quot;label&quot; : &quot;264 - Fabbricazione di prodotti di elettronica di consumo audio e video&quot;,
          &quot;value&quot; : &quot;264&quot;
        }, {
          &quot;label&quot; : &quot;265 - Fabbricazione di strumenti e apparecchi di misurazione. prova e navigazione (esclusi quelli ottici)&quot;,
          &quot;value&quot; : &quot;265&quot;
        }, {
          &quot;label&quot; : &quot;266 - Fabbricazione di strumenti per irradiazione. apparecchiature elettromedicali ed elettroterapeutiche&quot;,
          &quot;value&quot; : &quot;266&quot;
        }, {
          &quot;label&quot; : &quot;267 - Fabbricazione di strumenti ottici e attrezzature fotografiche&quot;,
          &quot;value&quot; : &quot;267&quot;
        }, {
          &quot;label&quot; : &quot;268 - Fabbricazione di supporti magnetici ed ottici&quot;,
          &quot;value&quot; : &quot;268&quot;
        }, {
          &quot;label&quot; : &quot;271 - FABBRICAZIONE DI MOTORI. GENERATORI E TRASFORMATORI ELETTRICI E DI APPARECCHIATURE PER LA DISTRIBUZIONE E IL CONTROLLO DELLELETTRICITÀ&quot;,
          &quot;value&quot; : &quot;271&quot;
        }, {
          &quot;label&quot; : &quot;272 - Fabbricazione di batterie di pile ed accumulatori elettrici&quot;,
          &quot;value&quot; : &quot;272&quot;
        }, {
          &quot;label&quot; : &quot;273 - Fabbricazione di attrezzature per cablaggio&quot;,
          &quot;value&quot; : &quot;273&quot;
        }, {
          &quot;label&quot; : &quot;274 - Fabbricazione di apparecchiature per illuminazione&quot;,
          &quot;value&quot; : &quot;274&quot;
        }, {
          &quot;label&quot; : &quot;275 - FABBRICAZIONE DI APPARECCHI PER USO DOMESTICO&quot;,
          &quot;value&quot; : &quot;275&quot;
        }, {
          &quot;label&quot; : &quot;279 - Fabbricazione di altre apparecchiature elettriche&quot;,
          &quot;value&quot; : &quot;279&quot;
        }, {
          &quot;label&quot; : &quot;281 - Fabbricazione di motori e turbine (esclusi i motori per aeromobili. veicoli e motocicli)&quot;,
          &quot;value&quot; : &quot;281&quot;
        }, {
          &quot;label&quot; : &quot;282 - FABBRICAZIONE DI ALTRE MACCHINE DI IMPIEGO GENERALE&quot;,
          &quot;value&quot; : &quot;282&quot;
        }, {
          &quot;label&quot; : &quot;283 - Fabbricazione di macchine per lagricoltura e la silvicoltura&quot;,
          &quot;value&quot; : &quot;283&quot;
        }, {
          &quot;label&quot; : &quot;284 - Fabbricazione di macchine utensili per la formatura dei metalli&quot;,
          &quot;value&quot; : &quot;284&quot;
        }, {
          &quot;label&quot; : &quot;289 - Fabbricazione di macchine per lindustria alimentare. delle bevande e del tabacco&quot;,
          &quot;value&quot; : &quot;289&quot;
        }, {
          &quot;label&quot; : &quot;291 - Fabbricazione di autoveicoli&quot;,
          &quot;value&quot; : &quot;291&quot;
        }, {
          &quot;label&quot; : &quot;292 - Fabbricazione di carrozzerie per autoveicoli. rimorchi e semirimorchi&quot;,
          &quot;value&quot; : &quot;292&quot;
        }, {
          &quot;label&quot; : &quot;293 - Fabbricazione di apparecchiature elettriche ed elettroniche per autoveicoli e loro motori&quot;,
          &quot;value&quot; : &quot;293&quot;
        }, {
          &quot;label&quot; : &quot;301 - Costruzione di navi e di strutture galleggianti&quot;,
          &quot;value&quot; : &quot;301&quot;
        }, {
          &quot;label&quot; : &quot;302 - COSTRUZIONE DI LOCOMOTIVE E DI MATERIALE ROTABILE FERRO-TRANVIARIO&quot;,
          &quot;value&quot; : &quot;302&quot;
        }, {
          &quot;label&quot; : &quot;303 - Fabbricazione di aeromobili. di veicoli spaziali e dei relativi dispositivi&quot;,
          &quot;value&quot; : &quot;303&quot;
        }, {
          &quot;label&quot; : &quot;304 - Fabbricazione di veicoli militari da combattimento&quot;,
          &quot;value&quot; : &quot;304&quot;
        }, {
          &quot;label&quot; : &quot;309 - FABBRICAZIONE DI MEZZI DI TRASPORTO NCA&quot;,
          &quot;value&quot; : &quot;309&quot;
        }, {
          &quot;label&quot; : &quot;310 - Fabbricazione di materassi&quot;,
          &quot;value&quot; : &quot;310&quot;
        }, {
          &quot;label&quot; : &quot;321 - Coniazione di monete&quot;,
          &quot;value&quot; : &quot;321&quot;
        }, {
          &quot;label&quot; : &quot;322 - Fabbricazione di strumenti musicali&quot;,
          &quot;value&quot; : &quot;322&quot;
        }, {
          &quot;label&quot; : &quot;323 - Fabbricazione di articoli sportivi&quot;,
          &quot;value&quot; : &quot;323&quot;
        }, {
          &quot;label&quot; : &quot;324 - FABBRICAZIONE DI GIOCHI E GIOCATTOLI&quot;,
          &quot;value&quot; : &quot;324&quot;
        }, {
          &quot;label&quot; : &quot;325 - FABBRICAZIONE DI STRUMENTI E FORNITURE MEDICHE E DENTISTICHE&quot;,
          &quot;value&quot; : &quot;325&quot;
        }, {
          &quot;label&quot; : &quot;329 - Altre industrie manifatturiere nca&quot;,
          &quot;value&quot; : &quot;329&quot;
        }, {
          &quot;label&quot; : &quot;331 - Riparazione e manutenzione di locomotive e di materiale rotabile ferro-tranviario (esclusi i loro motori)&quot;,
          &quot;value&quot; : &quot;331&quot;
        }, {
          &quot;label&quot; : &quot;332 - Installazione di macchine ed apparecchiature industriali&quot;,
          &quot;value&quot; : &quot;332&quot;
        }, {
          &quot;label&quot; : &quot;351 - Commercio di energia elettrica&quot;,
          &quot;value&quot; : &quot;351&quot;
        }, {
          &quot;label&quot; : &quot;352 - Distribuzione di combustibili gassosi mediante condotte&quot;,
          &quot;value&quot; : &quot;352&quot;
        }, {
          &quot;label&quot; : &quot;353 - Fornitura di vapore e aria condizionata&quot;,
          &quot;value&quot; : &quot;353&quot;
        }, {
          &quot;label&quot; : &quot;360 - RACCOLTA, TRATTAMENTO E FORNITURA DI ACQUA&quot;,
          &quot;value&quot; : &quot;360&quot;
        }, {
          &quot;label&quot; : &quot;370 - GESTIONE DELLE RETI FOGNARIE&quot;,
          &quot;value&quot; : &quot;370&quot;
        }, {
          &quot;label&quot; : &quot;381 - RACCOLTA DEI RIFIUTI&quot;,
          &quot;value&quot; : &quot;381&quot;
        }, {
          &quot;label&quot; : &quot;382 - Trattamento e smaltimento di rifiuti non pericolosi&quot;,
          &quot;value&quot; : &quot;382&quot;
        }, {
          &quot;label&quot; : &quot;383 - RECUPERO DEI MATERIALI&quot;,
          &quot;value&quot; : &quot;383&quot;
        }, {
          &quot;label&quot; : &quot;390 - ATTIVITÀ DI RISANAMENTO E ALTRI SERVIZI DI GESTIONE DEI RIFIUTI&quot;,
          &quot;value&quot; : &quot;390&quot;
        }, {
          &quot;label&quot; : &quot;411 - SVILUPPO DI PROGETTI IMMOBILIARI&quot;,
          &quot;value&quot; : &quot;411&quot;
        }, {
          &quot;label&quot; : &quot;412 - Costruzione di edifici residenziali e non residenziali&quot;,
          &quot;value&quot; : &quot;412&quot;
        }, {
          &quot;label&quot; : &quot;421 - Costruzione di strade e autostrade&quot;,
          &quot;value&quot; : &quot;421&quot;
        }, {
          &quot;label&quot; : &quot;422 - Costruzione di opere di pubblica utilità per il trasporto di fluidi&quot;,
          &quot;value&quot; : &quot;422&quot;
        }, {
          &quot;label&quot; : &quot;429 - Costruzione di opere idrauliche&quot;,
          &quot;value&quot; : &quot;429&quot;
        }, {
          &quot;label&quot; : &quot;431 - DemOLIZIONE E PREPARAZIONE DEL CANTIERE EDILE&quot;,
          &quot;value&quot; : &quot;431&quot;
        }, {
          &quot;label&quot; : &quot;432 - Installazione di impianti elettrici&quot;,
          &quot;value&quot; : &quot;432&quot;
        }, {
          &quot;label&quot; : &quot;433 - Altri lavori di completamento e di finitura degli edifici&quot;,
          &quot;value&quot; : &quot;433&quot;
        }, {
          &quot;label&quot; : &quot;439 - ALTRI LAVORI SPECIALIZZATI DI COSTRUZIONE&quot;,
          &quot;value&quot; : &quot;439&quot;
        }, {
          &quot;label&quot; : &quot;451 - Commercio di autovetture e di autoveicoli leggeri&quot;,
          &quot;value&quot; : &quot;451&quot;
        }, {
          &quot;label&quot; : &quot;452 - Manutenzione e riparazione di autoveicoli&quot;,
          &quot;value&quot; : &quot;452&quot;
        }, {
          &quot;label&quot; : &quot;453 - COMMERCIO DI PARTI E ACCESSORI DI AUTOVEICOLI&quot;,
          &quot;value&quot; : &quot;453&quot;
        }, {
          &quot;label&quot; : &quot;454 - Commercio. manutenzione e riparazione di motocicli e relative parti ed accessori&quot;,
          &quot;value&quot; : &quot;454&quot;
        }, {
          &quot;label&quot; : &quot;461 - Intermediari del commercio di prodotti tessili. abbigliamento. pellicce. calzature e articoli in pelle&quot;,
          &quot;value&quot; : &quot;461&quot;
        }, {
          &quot;label&quot; : &quot;462 - Commercio allingrosso di cereali. tabacco grezzo. sementi e alimenti per il bestiame (mangimi)&quot;,
          &quot;value&quot; : &quot;462&quot;
        }, {
          &quot;label&quot; : &quot;463 - Commercio allingrosso non specializzato di prodotti alimentari. bevande e tabacco&quot;,
          &quot;value&quot; : &quot;463&quot;
        }, {
          &quot;label&quot; : &quot;464 - Commercio allingrosso di orologi e di gioielleria&quot;,
          &quot;value&quot; : &quot;464&quot;
        }, {
          &quot;label&quot; : &quot;465 - Commercio allingrosso di computer. apparecchiature informatiche periferiche e di software&quot;,
          &quot;value&quot; : &quot;465&quot;
        }, {
          &quot;label&quot; : &quot;466 - Commercio allingrosso di macchinari per lindustria tessile. di macchine per cucire e per maglieria&quot;,
          &quot;value&quot; : &quot;466&quot;
        }, {
          &quot;label&quot; : &quot;467 - Commercio allingrosso di combustibili solidi. liquidi. gassosi e di prodotti derivati&quot;,
          &quot;value&quot; : &quot;467&quot;
        }, {
          &quot;label&quot; : &quot;469 - Commercio allingrosso non specializzato&quot;,
          &quot;value&quot; : &quot;469&quot;
        }, {
          &quot;label&quot; : &quot;471 - Commercio al dettaglio in altri esercizi non specializzati&quot;,
          &quot;value&quot; : &quot;471&quot;
        }, {
          &quot;label&quot; : &quot;472 - Commercio al dettaglio di bevande in esercizi specializzati&quot;,
          &quot;value&quot; : &quot;472&quot;
        }, {
          &quot;label&quot; : &quot;473 - Commercio al dettaglio di carburante per autotrazione in esercizi specializzati&quot;,
          &quot;value&quot; : &quot;473&quot;
        }, {
          &quot;label&quot; : &quot;474 - COMMERCIO AL DETTAGLIO DI APPARECCHIATURE INFORMATICHE E PER LE TELECOMUNICAZIONI (ICT) IN ESERCIZI SPECIALIZZATI&quot;,
          &quot;value&quot; : &quot;474&quot;
        }, {
          &quot;label&quot; : &quot;475 - Commercio al dettaglio di ferramenta. vernici. vetro piano e materiali da costruzione in esercizi specializzati&quot;,
          &quot;value&quot; : &quot;475&quot;
        }, {
          &quot;label&quot; : &quot;476 - Commercio al dettaglio di libri in esercizi specializzati&quot;,
          &quot;value&quot; : &quot;476&quot;
        }, {
          &quot;label&quot; : &quot;477 - Commercio al dettaglio di articoli di seconda mano in negozi&quot;,
          &quot;value&quot; : &quot;477&quot;
        }, {
          &quot;label&quot; : &quot;478 - COMMERCIO AL DETTAGLIO AMBULANTE&quot;,
          &quot;value&quot; : &quot;478&quot;
        }, {
          &quot;label&quot; : &quot;479 - Altro commercio al dettaglio al di fuori di negozi. banchi o mercati&quot;,
          &quot;value&quot; : &quot;479&quot;
        }, {
          &quot;label&quot; : &quot;491 - Trasporto ferroviario di passeggeri (interurbano)&quot;,
          &quot;value&quot; : &quot;491&quot;
        }, {
          &quot;label&quot; : &quot;492 - Trasporto ferroviario di merci&quot;,
          &quot;value&quot; : &quot;492&quot;
        }, {
          &quot;label&quot; : &quot;493 - ALTRI TRASPORTI TERRESTRI DI PASSEGGERI&quot;,
          &quot;value&quot; : &quot;493&quot;
        }, {
          &quot;label&quot; : &quot;494 - Trasporto di merci su strada&quot;,
          &quot;value&quot; : &quot;494&quot;
        }, {
          &quot;label&quot; : &quot;495 - Trasporto mediante condotte&quot;,
          &quot;value&quot; : &quot;495&quot;
        }, {
          &quot;label&quot; : &quot;501 - Trasporto marittimo e costiero di passeggeri&quot;,
          &quot;value&quot; : &quot;501&quot;
        }, {
          &quot;label&quot; : &quot;502 - Trasporto marittimo e costiero di merci&quot;,
          &quot;value&quot; : &quot;502&quot;
        }, {
          &quot;label&quot; : &quot;503 - Trasporto di passeggeri per vie dacqua interne&quot;,
          &quot;value&quot; : &quot;503&quot;
        }, {
          &quot;label&quot; : &quot;504 - Trasporto di merci per vie dacqua interne&quot;,
          &quot;value&quot; : &quot;504&quot;
        }, {
          &quot;label&quot; : &quot;511 - Trasporto aereo di passeggeri&quot;,
          &quot;value&quot; : &quot;511&quot;
        }, {
          &quot;label&quot; : &quot;512 - Trasporto spaziale&quot;,
          &quot;value&quot; : &quot;512&quot;
        }, {
          &quot;label&quot; : &quot;521 - Magazzinaggio e custodia&quot;,
          &quot;value&quot; : &quot;521&quot;
        }, {
          &quot;label&quot; : &quot;522 - Attività dei servizi connessi al trasporto aereo&quot;,
          &quot;value&quot; : &quot;522&quot;
        }, {
          &quot;label&quot; : &quot;531 - Attività postali con obbligo di servizio universale&quot;,
          &quot;value&quot; : &quot;531&quot;
        }, {
          &quot;label&quot; : &quot;532 - Altre attività postali e di corriere senza obbligo di servizio universale&quot;,
          &quot;value&quot; : &quot;532&quot;
        }, {
          &quot;label&quot; : &quot;551 - Alberghi e strutture simili&quot;,
          &quot;value&quot; : &quot;551&quot;
        }, {
          &quot;label&quot; : &quot;552 - Alloggi per vacanze e altre strutture per brevi soggiorni&quot;,
          &quot;value&quot; : &quot;552&quot;
        }, {
          &quot;label&quot; : &quot;553 - Aree di campeggio e aree attrezzate per camper e roulotte&quot;,
          &quot;value&quot; : &quot;553&quot;
        }, {
          &quot;label&quot; : &quot;559 - Altri alloggi&quot;,
          &quot;value&quot; : &quot;559&quot;
        }, {
          &quot;label&quot; : &quot;561 - Ristoranti e attività di ristorazione mobile&quot;,
          &quot;value&quot; : &quot;561&quot;
        }, {
          &quot;label&quot; : &quot;562 - Mense e catering continuativo su base contrattuale&quot;,
          &quot;value&quot; : &quot;562&quot;
        }, {
          &quot;label&quot; : &quot;563 - Bar e altri esercizi simili senza cucina&quot;,
          &quot;value&quot; : &quot;563&quot;
        }, {
          &quot;label&quot; : &quot;581 - Edizione di quotidiani&quot;,
          &quot;value&quot; : &quot;581&quot;
        }, {
          &quot;label&quot; : &quot;582 - Edizione di altri software&quot;,
          &quot;value&quot; : &quot;582&quot;
        }, {
          &quot;label&quot; : &quot;591 - Attività di post-produzione cinematografica. di video e di programmi televisivi&quot;,
          &quot;value&quot; : &quot;591&quot;
        }, {
          &quot;label&quot; : &quot;592 - Attività di registrazione sonora e di editoria musicale&quot;,
          &quot;value&quot; : &quot;592&quot;
        }, {
          &quot;label&quot; : &quot;601 - Trasmissioni radiofoniche&quot;,
          &quot;value&quot; : &quot;601&quot;
        }, {
          &quot;label&quot; : &quot;602 - Attività di programmazione e trasmissioni televisive&quot;,
          &quot;value&quot; : &quot;602&quot;
        }, {
          &quot;label&quot; : &quot;611 - TELECOMUNICAZIONI FISSE&quot;,
          &quot;value&quot; : &quot;611&quot;
        }, {
          &quot;label&quot; : &quot;612 - Telecomunicazioni mobili&quot;,
          &quot;value&quot; : &quot;612&quot;
        }, {
          &quot;label&quot; : &quot;613 - Telecomunicazioni satellitari&quot;,
          &quot;value&quot; : &quot;613&quot;
        }, {
          &quot;label&quot; : &quot;619 - Altre attività di telecomunicazione&quot;,
          &quot;value&quot; : &quot;619&quot;
        }, {
          &quot;label&quot; : &quot;620 - Consulenza nel settore delle tecnologie dellinformatica&quot;,
          &quot;value&quot; : &quot;620&quot;
        }, {
          &quot;label&quot; : &quot;631 - ELABORAZIONE DEI DATI. HOSTING E ATTIVITÀ CONNESSE&quot;,
          &quot;value&quot; : &quot;631&quot;
        }, {
          &quot;label&quot; : &quot;639 - Altre attività dei servizi di informazione nca&quot;,
          &quot;value&quot; : &quot;639&quot;
        }, {
          &quot;label&quot; : &quot;641 - Attività delle banche centrali&quot;,
          &quot;value&quot; : &quot;641&quot;
        }, {
          &quot;label&quot; : &quot;642 - ATTIVITÀ DELLE SOCIETÀ DI PARTECIPAZIONE (HOLDING)&quot;,
          &quot;value&quot; : &quot;642&quot;
        }, {
          &quot;label&quot; : &quot;643 - Società fiduciarie. fondi e altre società simili&quot;,
          &quot;value&quot; : &quot;643&quot;
        }, {
          &quot;label&quot; : &quot;649 - Altre attività creditizie&quot;,
          &quot;value&quot; : &quot;649&quot;
        }, {
          &quot;label&quot; : &quot;651 - Assicurazioni diverse da quelle sulla vita&quot;,
          &quot;value&quot; : &quot;651&quot;
        }, {
          &quot;label&quot; : &quot;652 - RIASSICURAZIONI&quot;,
          &quot;value&quot; : &quot;652&quot;
        }, {
          &quot;label&quot; : &quot;653 - Fondi pensione&quot;,
          &quot;value&quot; : &quot;653&quot;
        }, {
          &quot;label&quot; : &quot;661 - Altre attività ausiliarie dei servizi finanziari (escluse le assicurazioni e i fondi pensione)&quot;,
          &quot;value&quot; : &quot;661&quot;
        }, {
          &quot;label&quot; : &quot;662 - ATTIVITÀ AUSILIARIE DELLE ASSICURAZIONI E DEI FONDI PENSIONE&quot;,
          &quot;value&quot; : &quot;662&quot;
        }, {
          &quot;label&quot; : &quot;663 - Attività di gestione dei fondi&quot;,
          &quot;value&quot; : &quot;663&quot;
        }, {
          &quot;label&quot; : &quot;681 - Compravendita di beni immobili effettuata su beni propri&quot;,
          &quot;value&quot; : &quot;681&quot;
        }, {
          &quot;label&quot; : &quot;682 - Affitto e gestione di immobili di proprietà o in leasing&quot;,
          &quot;value&quot; : &quot;682&quot;
        }, {
          &quot;label&quot; : &quot;683 - Gestione di immobili per conto terzi&quot;,
          &quot;value&quot; : &quot;683&quot;
        }, {
          &quot;label&quot; : &quot;691 - Attività degli studi legali e notarili&quot;,
          &quot;value&quot; : &quot;691&quot;
        }, {
          &quot;label&quot; : &quot;692 - Contabilità. controllo e revisione contabile. consulenza in materia fiscale e del lavoro&quot;,
          &quot;value&quot; : &quot;692&quot;
        }, {
          &quot;label&quot; : &quot;701 - Attività delle holding impegnate nelle attività gestionali (holding operative)&quot;,
          &quot;value&quot; : &quot;701&quot;
        }, {
          &quot;label&quot; : &quot;702 - ATTIVITÀ DI CONSULENZA GESTIONALE&quot;,
          &quot;value&quot; : &quot;702&quot;
        }, {
          &quot;label&quot; : &quot;711 - ATTIVITÀ DEGLI STUDI DI ARCHITETTURA. INGEGNERIA ED ALTRI STUDI TECNICI&quot;,
          &quot;value&quot; : &quot;711&quot;
        }, {
          &quot;label&quot; : &quot;712 - Collaudi ed analisi tecniche&quot;,
          &quot;value&quot; : &quot;712&quot;
        }, {
          &quot;label&quot; : &quot;721 - Altre attività di ricerca e sviluppo sperimentale nel campo delle scienze naturali e dellingegneria&quot;,
          &quot;value&quot; : &quot;721&quot;
        }, {
          &quot;label&quot; : &quot;722 - Ricerca e sviluppo sperimentale nel campo delle scienze sociali e umanistiche&quot;,
          &quot;value&quot; : &quot;722&quot;
        }, {
          &quot;label&quot; : &quot;731 - Attività delle concessionarie e degli altri intermediari di servizi pubblicitari&quot;,
          &quot;value&quot; : &quot;731&quot;
        }, {
          &quot;label&quot; : &quot;732 - RICERCHE DI MERCATO E SONDAGGI DI OPINIONE&quot;,
          &quot;value&quot; : &quot;732&quot;
        }, {
          &quot;label&quot; : &quot;741 - Attività di design specializzate&quot;,
          &quot;value&quot; : &quot;741&quot;
        }, {
          &quot;label&quot; : &quot;742 - Attività fotografiche&quot;,
          &quot;value&quot; : &quot;742&quot;
        }, {
          &quot;label&quot; : &quot;743 - Traduzione e interpretariato&quot;,
          &quot;value&quot; : &quot;743&quot;
        }, {
          &quot;label&quot; : &quot;749 - Altre attività professionali. scientifiche e tecniche nca&quot;,
          &quot;value&quot; : &quot;749&quot;
        }, {
          &quot;label&quot; : &quot;750 - SERVIZI VETERINARI&quot;,
          &quot;value&quot; : &quot;750&quot;
        }, {
          &quot;label&quot; : &quot;771 - NOLEGGIO DI AUTOVEICOLI&quot;,
          &quot;value&quot; : &quot;771&quot;
        }, {
          &quot;label&quot; : &quot;772 - Noleggio di altri beni per uso personale e domestico (escluse le attrezzature sportive e ricreative)&quot;,
          &quot;value&quot; : &quot;772&quot;
        }, {
          &quot;label&quot; : &quot;773 - Noleggio di macchine e attrezzature agricole&quot;,
          &quot;value&quot; : &quot;773&quot;
        }, {
          &quot;label&quot; : &quot;774 - Concessione dei diritti di sfruttamento di proprietà intellettuale e prodotti simili (escluse le opere protette dal copyright)&quot;,
          &quot;value&quot; : &quot;774&quot;
        }, {
          &quot;label&quot; : &quot;781 - ATTIVITÀ DI AGENZIE DI COLLOCAMENTO&quot;,
          &quot;value&quot; : &quot;781&quot;
        }, {
          &quot;label&quot; : &quot;782 - Attività delle agenzie di lavoro temporaneo (interinale)&quot;,
          &quot;value&quot; : &quot;782&quot;
        }, {
          &quot;label&quot; : &quot;783 - Altre attività di fornitura e gestione di risorse umane&quot;,
          &quot;value&quot; : &quot;783&quot;
        }, {
          &quot;label&quot; : &quot;791 - ATTIVITÀ DELLE AGENZIE DI VIAGGIO E DEI TOUR OPERATOR&quot;,
          &quot;value&quot; : &quot;791&quot;
        }, {
          &quot;label&quot; : &quot;799 - Altri servizi di prenotazione e altre attività di assistenza turistica non svolte dalle agenzie di viaggio&quot;,
          &quot;value&quot; : &quot;799&quot;
        }, {
          &quot;label&quot; : &quot;801 - Servizi di vigilanza privata&quot;,
          &quot;value&quot; : &quot;801&quot;
        }, {
          &quot;label&quot; : &quot;802 - Servizi connessi ai sistemi di vigilanza&quot;,
          &quot;value&quot; : &quot;802&quot;
        }, {
          &quot;label&quot; : &quot;803 - Servizi investigativi privati&quot;,
          &quot;value&quot; : &quot;803&quot;
        }, {
          &quot;label&quot; : &quot;811 - Servizi integrati di gestione agli edifici&quot;,
          &quot;value&quot; : &quot;811&quot;
        }, {
          &quot;label&quot; : &quot;812 - Pulizia generale (non specializzata) di edifici&quot;,
          &quot;value&quot; : &quot;812&quot;
        }, {
          &quot;label&quot; : &quot;813 - Cura e manutenzione del paesaggio&quot;,
          &quot;value&quot; : &quot;813&quot;
        }, {
          &quot;label&quot; : &quot;821 - Servizi integrati di supporto per le funzioni dufficio&quot;,
          &quot;value&quot; : &quot;821&quot;
        }, {
          &quot;label&quot; : &quot;822 - Attività dei call center&quot;,
          &quot;value&quot; : &quot;822&quot;
        }, {
          &quot;label&quot; : &quot;823 - ORGANIZZAZIONE DI CONVEGNI E FIERE&quot;,
          &quot;value&quot; : &quot;823&quot;
        }, {
          &quot;label&quot; : &quot;829 - Attività di imballaggio e confezionamento per conto terzi&quot;,
          &quot;value&quot; : &quot;829&quot;
        }, {
          &quot;label&quot; : &quot;841 - Regolamentazione delle attività relative alla fornitura di servizi di assistenza sanitaria. dellistruzione. di servizi culturali e ad altri servizi sociali (esclusa lassicurazione sociale obbligatoria)&quot;,
          &quot;value&quot; : &quot;841&quot;
        }, {
          &quot;label&quot; : &quot;842 - Affari esteri&quot;,
          &quot;value&quot; : &quot;842&quot;
        }, {
          &quot;label&quot; : &quot;843 - Assicurazione sociale obbligatoria&quot;,
          &quot;value&quot; : &quot;843&quot;
        }, {
          &quot;label&quot; : &quot;851 - Istruzione prescolastica&quot;,
          &quot;value&quot; : &quot;851&quot;
        }, {
          &quot;label&quot; : &quot;852 - Istruzione primaria&quot;,
          &quot;value&quot; : &quot;852&quot;
        }, {
          &quot;label&quot; : &quot;853 - ISTRUZIONE SECONDARIA&quot;,
          &quot;value&quot; : &quot;853&quot;
        }, {
          &quot;label&quot; : &quot;854 - ISTRUZIONE POST-SECONDARIA UNIVERSITARIA E NON UNIVERSITARIA&quot;,
          &quot;value&quot; : &quot;854&quot;
        }, {
          &quot;label&quot; : &quot;855 - Attività delle scuole guida&quot;,
          &quot;value&quot; : &quot;855&quot;
        }, {
          &quot;label&quot; : &quot;856 - Attività di supporto allistruzione&quot;,
          &quot;value&quot; : &quot;856&quot;
        }, {
          &quot;label&quot; : &quot;861 - Servizi ospedalieri&quot;,
          &quot;value&quot; : &quot;861&quot;
        }, {
          &quot;label&quot; : &quot;862 - Servizi degli studi medici di medicina generale&quot;,
          &quot;value&quot; : &quot;862&quot;
        }, {
          &quot;label&quot; : &quot;869 - Altri servizi di assistenza sanitaria&quot;,
          &quot;value&quot; : &quot;869&quot;
        }, {
          &quot;label&quot; : &quot;871 - Strutture di assistenza infermieristica residenziale&quot;,
          &quot;value&quot; : &quot;871&quot;
        }, {
          &quot;label&quot; : &quot;872 - Strutture di assistenza residenziale per persone affette da ritardi mentali. disturbi mentali o che abusano di sostanze stupefacenti&quot;,
          &quot;value&quot; : &quot;872&quot;
        }, {
          &quot;label&quot; : &quot;873 - Strutture di assistenza residenziale per anziani e disabili&quot;,
          &quot;value&quot; : &quot;873&quot;
        }, {
          &quot;label&quot; : &quot;879 - Altre strutture di assistenza sociale residenziale&quot;,
          &quot;value&quot; : &quot;879&quot;
        }, {
          &quot;label&quot; : &quot;881 - Assistenza sociale non residenziale per anziani e disabili&quot;,
          &quot;value&quot; : &quot;881&quot;
        }, {
          &quot;label&quot; : &quot;889 - ALTRE ATTIVITÀ DI ASSISTENZA SOCIALE NON RESIDENZIALE&quot;,
          &quot;value&quot; : &quot;889&quot;
        }, {
          &quot;label&quot; : &quot;900 - Creazioni artistiche e letterarie&quot;,
          &quot;value&quot; : &quot;900&quot;
        }, {
          &quot;label&quot; : &quot;910 - Attività degli orti botanici. dei giardini zoologici e delle riserve naturali&quot;,
          &quot;value&quot; : &quot;910&quot;
        }, {
          &quot;label&quot; : &quot;920 - ATTIVITÀ RIGUARDANTI LE LOTTERIE, LE SCOMMESSE, LE CASE DA GIOCO&quot;,
          &quot;value&quot; : &quot;920&quot;
        }, {
          &quot;label&quot; : &quot;931 - Altre attività sportive&quot;,
          &quot;value&quot; : &quot;931&quot;
        }, {
          &quot;label&quot; : &quot;932 - Altre attività ricreative e di divertimento&quot;,
          &quot;value&quot; : &quot;932&quot;
        }, {
          &quot;label&quot; : &quot;941 - ATTIVITÀ DI ORGANIZZAZIONI ECONOMICHE. DI DATORI DI LAVORO E PROFESSIONALI&quot;,
          &quot;value&quot; : &quot;941&quot;
        }, {
          &quot;label&quot; : &quot;942 - ATTIVITÀ DEI SINDACATI DI LAVORATORI DIPENDENTI&quot;,
          &quot;value&quot; : &quot;942&quot;
        }, {
          &quot;label&quot; : &quot;949 - Attività di altre organizzazioni associative nca&quot;,
          &quot;value&quot; : &quot;949&quot;
        }, {
          &quot;label&quot; : &quot;951 - RIPARAZIONE DI COMPUTER E DI APPARECCHIATURE PER LE COMUNICAZIONI&quot;,
          &quot;value&quot; : &quot;951&quot;
        }, {
          &quot;label&quot; : &quot;952 - Riparazione di altri beni per uso personale e per la casa&quot;,
          &quot;value&quot; : &quot;952&quot;
        }, {
          &quot;label&quot; : &quot;960 - Servizi di pompe funebri e attività connesse&quot;,
          &quot;value&quot; : &quot;960&quot;
        }, {
          &quot;label&quot; : &quot;970 - ATTIVITÀ DI FAMIGLIE E CONVIVENZE COME DATORI DI LAVORO PER PERSONALE DOMESTICO&quot;,
          &quot;value&quot; : &quot;970&quot;
        }, {
          &quot;label&quot; : &quot;981 - PRODUZIONE DI BENI INDIFFERENZIATI PER USO PROPRIO DA PARTE DI FAMIGLIE E CONVIVENZE&quot;,
          &quot;value&quot; : &quot;981&quot;
        }, {
          &quot;label&quot; : &quot;982 - PRODUZIONE DI SERVIZI INDIFFERENZIATI PER USO PROPRIO DA PARTE DI FAMIGLIE E CONVIVENZE&quot;,
          &quot;value&quot; : &quot;982&quot;
        } ],
        &quot;isOptionSetting&quot; : null,
        &quot;RAEValues&quot; : [ {
          &quot;value&quot; : &quot;000&quot;,
          &quot;label&quot; : &quot;000 - SCONOSCIUTO&quot;
        }, {
          &quot;value&quot; : &quot;011&quot;,
          &quot;label&quot; : &quot;011 - PRODOTTI VEGETALI DELL AGRICOLTURA E DELLE FORESTE&quot;
        }, {
          &quot;value&quot; : &quot;012&quot;,
          &quot;label&quot; : &quot;012 - VINO&quot;
        }, {
          &quot;value&quot; : &quot;013&quot;,
          &quot;label&quot; : &quot;013 - OLIO DI OLIVA NON RAFFINATO&quot;
        }, {
          &quot;value&quot; : &quot;014&quot;,
          &quot;label&quot; : &quot;014 - PRODOTTI ANIMALI DELL AGRICOLTURA E DELLA CACCIA&quot;
        }, {
          &quot;value&quot; : &quot;019&quot;,
          &quot;label&quot; : &quot;019 - PRODOTTI AGRICOLI ESCLUSIVAMENTE IMPORTATI&quot;
        }, {
          &quot;value&quot; : &quot;020&quot;,
          &quot;label&quot; : &quot;020 - PRODOTTI DELLA SILVICOLTURA&quot;
        }, {
          &quot;value&quot; : &quot;030&quot;,
          &quot;label&quot; : &quot;030 - PRODOTTI DELLA PESCA&quot;
        }, {
          &quot;value&quot; : &quot;051&quot;,
          &quot;label&quot; : &quot;051 - PRODOTTI DELL AGRICOLTURA, SILCOLTURA E PESCA&quot;
        }, {
          &quot;value&quot; : &quot;052&quot;,
          &quot;label&quot; : &quot;052 - PRODOTTI ENERGETICI&quot;
        }, {
          &quot;value&quot; : &quot;053&quot;,
          &quot;label&quot; : &quot;053 - MINERALI E METALLI FERROSI E NON&quot;
        }, {
          &quot;value&quot; : &quot;054&quot;,
          &quot;label&quot; : &quot;054 - MINER.E PROD.A BASE DI M.NON MET&quot;
        }, {
          &quot;value&quot; : &quot;055&quot;,
          &quot;label&quot; : &quot;055 - PRODOTTI CHIMICI&quot;
        }, {
          &quot;value&quot; : &quot;056&quot;,
          &quot;label&quot; : &quot;056 - P.IN METALLO ESCL.MACC./MEZZI TR&quot;
        }, {
          &quot;value&quot; : &quot;057&quot;,
          &quot;label&quot; : &quot;057 - MACCHINE AGRICOLE E INDUSTRIALI&quot;
        }, {
          &quot;value&quot; : &quot;058&quot;,
          &quot;label&quot; : &quot;058 - MAC.PER UFF./EL.DATI/PRECISIONE&quot;
        }, {
          &quot;value&quot; : &quot;059&quot;,
          &quot;label&quot; : &quot;059 - MATERIALE E FORNITURE ELETTRICHE&quot;
        }, {
          &quot;value&quot; : &quot;060&quot;,
          &quot;label&quot; : &quot;060 - MEZZI DI TRASPORTO&quot;
        }, {
          &quot;value&quot; : &quot;061&quot;,
          &quot;label&quot; : &quot;061 - PROD.ALIMENTARI/BEVANDE/TABACCO&quot;
        }, {
          &quot;value&quot; : &quot;062&quot;,
          &quot;label&quot; : &quot;062 - PRODOTTI TESSILI, CUOIO E CALZATURE, ABBIGLIAMENTO&quot;
        }, {
          &quot;value&quot; : &quot;063&quot;,
          &quot;label&quot; : &quot;063 - CARTA/PROD.DELLA STAMPA-EDITORIA&quot;
        }, {
          &quot;value&quot; : &quot;064&quot;,
          &quot;label&quot; : &quot;064 - PRODOTTI IN GOMMA E IN PLASTICA&quot;
        }, {
          &quot;value&quot; : &quot;065&quot;,
          &quot;label&quot; : &quot;065 - ALTRI PRODOTTI INDUSTRIALI&quot;
        }, {
          &quot;value&quot; : &quot;066&quot;,
          &quot;label&quot; : &quot;066 - EDILIZIA E OPERE PUBBLICHE&quot;
        }, {
          &quot;value&quot; : &quot;067&quot;,
          &quot;label&quot; : &quot;067 - SERVIZI DEL COMMERCIO, RECUPERI E RIPARAZIONI&quot;
        }, {
          &quot;value&quot; : &quot;068&quot;,
          &quot;label&quot; : &quot;068 - SERVIZI DEGLI ALBERGHI E PUBBLICI ESERCIZI&quot;
        }, {
          &quot;value&quot; : &quot;069&quot;,
          &quot;label&quot; : &quot;069 - SERVIZI DEI TRASPORTI INTERNI&quot;
        }, {
          &quot;value&quot; : &quot;070&quot;,
          &quot;label&quot; : &quot;070 - SERVIZI DEI TRASPORTI MARITTIMI E AEREI&quot;
        }, {
          &quot;value&quot; : &quot;071&quot;,
          &quot;label&quot; : &quot;071 - SERVIZI CONNESSI AI TRASPORTI&quot;
        }, {
          &quot;value&quot; : &quot;072&quot;,
          &quot;label&quot; : &quot;072 - SERVIZI DELLE COMUNICAZIONI&quot;
        }, {
          &quot;value&quot; : &quot;073&quot;,
          &quot;label&quot; : &quot;073 - ALTRI SERVIZI DESTINABILI ALLA VENDITA&quot;
        }, {
          &quot;value&quot; : &quot;092&quot;,
          &quot;label&quot; : &quot;092 - 092 - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;096&quot;,
          &quot;label&quot; : &quot;096 - 096 - DA ELIDERE&quot;
        }, {
          &quot;value&quot; : &quot;111&quot;,
          &quot;label&quot; : &quot;111 - CARBONE E AGGLOMERATI DI CARBONE&quot;
        }, {
          &quot;value&quot; : &quot;112&quot;,
          &quot;label&quot; : &quot;112 - LIGNITE E MATTONELLE DI LIGNITE&quot;
        }, {
          &quot;value&quot; : &quot;120&quot;,
          &quot;label&quot; : &quot;120 - PRODOTTI DELLA COKEFAZIONE&quot;
        }, {
          &quot;value&quot; : &quot;130&quot;,
          &quot;label&quot; : &quot;130 - PETROLIO GREGGIO, GAS NATURALE E SCISTI BITUMINOSI&quot;
        }, {
          &quot;value&quot; : &quot;140&quot;,
          &quot;label&quot; : &quot;140 - PRODOTTI PETROLIFERI RAFFINATI&quot;
        }, {
          &quot;value&quot; : &quot;151&quot;,
          &quot;label&quot; : &quot;151 - MINERALI CONTENENTI MATERIE FISSILI E FERTILI&quot;
        }, {
          &quot;value&quot; : &quot;152&quot;,
          &quot;label&quot; : &quot;152 - PRODUZIONE DI MATERIE FISSILI&quot;
        }, {
          &quot;value&quot; : &quot;161&quot;,
          &quot;label&quot; : &quot;161 - ENERGIA ELETTRICA&quot;
        }, {
          &quot;value&quot; : &quot;162&quot;,
          &quot;label&quot; : &quot;162 - GAS DISTRIBUITO&quot;
        }, {
          &quot;value&quot; : &quot;163&quot;,
          &quot;label&quot; : &quot;163 - VAPORE, ACQUA CALDA, ARIA COMPRESSA&quot;
        }, {
          &quot;value&quot; : &quot;170&quot;,
          &quot;label&quot; : &quot;170 - ACQUA (RACCOLTA, DEPURAZIONE E DISTRIBUZIONE)&quot;
        }, {
          &quot;value&quot; : &quot;211&quot;,
          &quot;label&quot; : &quot;211 - MINERALI DI FERRO&quot;
        }, {
          &quot;value&quot; : &quot;212&quot;,
          &quot;label&quot; : &quot;212 - MINERALI NON FERROSI&quot;
        }, {
          &quot;value&quot; : &quot;221&quot;,
          &quot;label&quot; : &quot;221 - GHISA E ACCIAIO (PRODOTTI CECA)&quot;
        }, {
          &quot;value&quot; : &quot;222&quot;,
          &quot;label&quot; : &quot;222 - TUBI IN ACCIAIO&quot;
        }, {
          &quot;value&quot; : &quot;223&quot;,
          &quot;label&quot; : &quot;223 - TRAFILATI/LAMINATI/PROFILATI&quot;
        }, {
          &quot;value&quot; : &quot;224&quot;,
          &quot;label&quot; : &quot;224 - METALLI NON FERROSI&quot;
        }, {
          &quot;value&quot; : &quot;231&quot;,
          &quot;label&quot; : &quot;231 - MATERIALI DA COSTRUZIONE E TERRE REFRATTARIE&quot;
        }, {
          &quot;value&quot; : &quot;232&quot;,
          &quot;label&quot; : &quot;232 - SALI DI POTASSIO E DI FOSFATI NATURALI&quot;
        }, {
          &quot;value&quot; : &quot;233&quot;,
          &quot;label&quot; : &quot;233 - SALGEMMA E SALE MARINO&quot;
        }, {
          &quot;value&quot; : &quot;239&quot;,
          &quot;label&quot; : &quot;239 - ALTRI MINERALI E TORBA&quot;
        }, {
          &quot;value&quot; : &quot;241&quot;,
          &quot;label&quot; : &quot;241 - MATERIALI DA COSTRUZIONE IN TERRACOTTA&quot;
        }, {
          &quot;value&quot; : &quot;242&quot;,
          &quot;label&quot; : &quot;242 - CEMENTO, CALCE E GESSO&quot;
        }, {
          &quot;value&quot; : &quot;243&quot;,
          &quot;label&quot; : &quot;243 - MAT. DA COSTR. IN CALCESTRUZZO&quot;
        }, {
          &quot;value&quot; : &quot;244&quot;,
          &quot;label&quot; : &quot;244 - ARTICOLI IN AMIANTO&quot;
        }, {
          &quot;value&quot; : &quot;245&quot;,
          &quot;label&quot; : &quot;245 - PIETRE E PRODOTTI MINERALI NON METALLIFERI&quot;
        }, {
          &quot;value&quot; : &quot;246&quot;,
          &quot;label&quot; : &quot;246 - MOLE E ALTRI PRODOTTI ABRASIVI&quot;
        }, {
          &quot;value&quot; : &quot;247&quot;,
          &quot;label&quot; : &quot;247 - VETRO&quot;
        }, {
          &quot;value&quot; : &quot;248&quot;,
          &quot;label&quot; : &quot;248 - PRODOTTI IN CERAMICA&quot;
        }, {
          &quot;value&quot; : &quot;252&quot;,
          &quot;label&quot; : &quot;252 - PRODOTTI DELLA PETROLCHIMICA E DELLA CARBOCHIMICA&quot;
        }, {
          &quot;value&quot; : &quot;253&quot;,
          &quot;label&quot; : &quot;253 - ALTRI PRODOTTI CHIMICI DI BASE&quot;
        }, {
          &quot;value&quot; : &quot;255&quot;,
          &quot;label&quot; : &quot;255 - INTONACI, PITTURE, VERNICI E INCHIOSTRI DA STAMPA&quot;
        }, {
          &quot;value&quot; : &quot;256&quot;,
          &quot;label&quot; : &quot;256 - ALTRI PROD.CHIMICI PER IND./AGR.&quot;
        }, {
          &quot;value&quot; : &quot;257&quot;,
          &quot;label&quot; : &quot;257 - PRODOTTI FARMACEUTICI&quot;
        }, {
          &quot;value&quot; : &quot;258&quot;,
          &quot;label&quot; : &quot;258 - SAPONI DETERSIVI PROFUMI&quot;
        }, {
          &quot;value&quot; : &quot;259&quot;,
          &quot;label&quot; : &quot;259 - ALTRI PROD.CHIMICI USO DOMESTICO&quot;
        }, {
          &quot;value&quot; : &quot;260&quot;,
          &quot;label&quot; : &quot;260 - FIBRE ARTIFICIALI E SINTETICHE&quot;
        }, {
          &quot;value&quot; : &quot;311&quot;,
          &quot;label&quot; : &quot;311 - PRODOTTI DELLE FONDERIE&quot;
        }, {
          &quot;value&quot; : &quot;312&quot;,
          &quot;label&quot; : &quot;312 - PRODUZIONE DI PEZZI IN ACCIAIO&quot;
        }, {
          &quot;value&quot; : &quot;313&quot;,
          &quot;label&quot; : &quot;313 - PRODOTTI DI SECONDA TRASFORMAZIONE DEI METALLI&quot;
        }, {
          &quot;value&quot; : &quot;314&quot;,
          &quot;label&quot; : &quot;314 - PRODOTTI PER COSTRUZIONI METALLICHE&quot;
        }, {
          &quot;value&quot; : &quot;315&quot;,
          &quot;label&quot; : &quot;315 - PRODOTTI DELL ARTE E DEL LAVORO DEL CALDERAIO&quot;
        }, {
          &quot;value&quot; : &quot;316&quot;,
          &quot;label&quot; : &quot;316 - UTENSILI/ART. FINITI IN METALLO&quot;
        }, {
          &quot;value&quot; : &quot;321&quot;,
          &quot;label&quot; : &quot;321 - MACCHINE E TRATTORI AGRICOLI&quot;
        }, {
          &quot;value&quot; : &quot;322&quot;,
          &quot;label&quot; : &quot;322 - MACCHINE UTENSILI E UTENSILERIA&quot;
        }, {
          &quot;value&quot; : &quot;323&quot;,
          &quot;label&quot; : &quot;323 - MACCHINE TESSILI E LORO ACESSORI&quot;
        }, {
          &quot;value&quot; : &quot;324&quot;,
          &quot;label&quot; : &quot;324 - MACCHINE/APPARECCHI INDUSTRIALI&quot;
        }, {
          &quot;value&quot; : &quot;325&quot;,
          &quot;label&quot; : &quot;325 - MACCHINE PER EDILIZIA E MINIERE&quot;
        }, {
          &quot;value&quot; : &quot;326&quot;,
          &quot;label&quot; : &quot;326 - INGRANAGGI E CUSCINETTI A SFERE&quot;
        }, {
          &quot;value&quot; : &quot;327&quot;,
          &quot;label&quot; : &quot;327 - MACCHINE LAV.LEGNO/CARTA/CUOIO&quot;
        }, {
          &quot;value&quot; : &quot;328&quot;,
          &quot;label&quot; : &quot;328 - ALTRE MACCHINE E APPARECCHI MECCANICI&quot;
        }, {
          &quot;value&quot; : &quot;330&quot;,
          &quot;label&quot; : &quot;330 - MACCHINE PER UFFICIO/ELABORATORI&quot;
        }, {
          &quot;value&quot; : &quot;341&quot;,
          &quot;label&quot; : &quot;341 - FILI E CAVI ELETTRICI&quot;
        }, {
          &quot;value&quot; : &quot;342&quot;,
          &quot;label&quot; : &quot;342 - APPARECCHI ELETTRICI&quot;
        }, {
          &quot;value&quot; : &quot;343&quot;,
          &quot;label&quot; : &quot;343 - MAT.ELETT.IND./PILE/ACCUMULATORI&quot;
        }, {
          &quot;value&quot; : &quot;344&quot;,
          &quot;label&quot; : &quot;344 - MAT.PER TELECOMUNICAZ./CONTATORI&quot;
        }, {
          &quot;value&quot; : &quot;345&quot;,
          &quot;label&quot; : &quot;345 - APP.ELETTRON/RADIO-TV/DISCHI/ECC&quot;
        }, {
          &quot;value&quot; : &quot;346&quot;,
          &quot;label&quot; : &quot;346 - ELETTRODOMESTICI&quot;
        }, {
          &quot;value&quot; : &quot;347&quot;,
          &quot;label&quot; : &quot;347 - LAMPADE, APPARECCHI PER ILLUMINAZIONE&quot;
        }, {
          &quot;value&quot; : &quot;351&quot;,
          &quot;label&quot; : &quot;351 - AUTOVEICOLI E RELATIVI MOTORI&quot;
        }, {
          &quot;value&quot; : &quot;352&quot;,
          &quot;label&quot; : &quot;352 - CARROZZERIE DI OGNI TIPO, RIMORCHI&quot;
        }, {
          &quot;value&quot; : &quot;353&quot;,
          &quot;label&quot; : &quot;353 - ACCESSORI E PEZZI DI RICAMBIO PER AUTOVEICOLI&quot;
        }, {
          &quot;value&quot; : &quot;361&quot;,
          &quot;label&quot; : &quot;361 - CANTIERI NAVALI&quot;
        }, {
          &quot;value&quot; : &quot;362&quot;,
          &quot;label&quot; : &quot;362 - MATERIALE ROTABILE FERROTRAMVIARIO E FILOVIARIO&quot;
        }, {
          &quot;value&quot; : &quot;363&quot;,
          &quot;label&quot; : &quot;363 - BICICLETTE, MOTOVEICOLI, CARROZZELLE PER INVALIDI&quot;
        }, {
          &quot;value&quot; : &quot;364&quot;,
          &quot;label&quot; : &quot;364 - AEREI ELICOTTERI MISSILI CAPSULE&quot;
        }, {
          &quot;value&quot; : &quot;365&quot;,
          &quot;label&quot; : &quot;365 - CARROZZINE/VEICOLI TRAZ. ANIMALE&quot;
        }, {
          &quot;value&quot; : &quot;371&quot;,
          &quot;label&quot; : &quot;371 - STRUMENTI DI PRECISIONE&quot;
        }, {
          &quot;value&quot; : &quot;372&quot;,
          &quot;label&quot; : &quot;372 - MATERIALE MEDICO-CHIRURGICO, APPARECCHI ORTOPEDICI&quot;
        }, {
          &quot;value&quot; : &quot;373&quot;,
          &quot;label&quot; : &quot;373 - STRUMENTI OTTICI, MATERIALE FOTOGRAFICO&quot;
        }, {
          &quot;value&quot; : &quot;374&quot;,
          &quot;label&quot; : &quot;374 - OROLOGI&quot;
        }, {
          &quot;value&quot; : &quot;411&quot;,
          &quot;label&quot; : &quot;411 - GRASSI VEGETALI ED ANIMALI&quot;
        }, {
          &quot;value&quot; : &quot;412&quot;,
          &quot;label&quot; : &quot;412 - CARNI FRESCHE E CONSERVATE&quot;
        }, {
          &quot;value&quot; : &quot;413&quot;,
          &quot;label&quot; : &quot;413 - LATTE E PRODOTTI DELLA TRASFORMAZIONE DEL LATTE&quot;
        }, {
          &quot;value&quot; : &quot;414&quot;,
          &quot;label&quot; : &quot;414 - CONSERVE, SUCCHI DI FRUTTA E LEGUMI&quot;
        }, {
          &quot;value&quot; : &quot;415&quot;,
          &quot;label&quot; : &quot;415 - PESCE CONSERVATO E PROD.DEL MARE&quot;
        }, {
          &quot;value&quot; : &quot;416&quot;,
          &quot;label&quot; : &quot;416 - FARINE, GRANELLE, SEMOLE, FIOCCHI DI CEREALI&quot;
        }, {
          &quot;value&quot; : &quot;417&quot;,
          &quot;label&quot; : &quot;417 - PASTE ALIMENTARI&quot;
        }, {
          &quot;value&quot; : &quot;418&quot;,
          &quot;label&quot; : &quot;418 - PRODOTTI AMIDACEI&quot;
        }, {
          &quot;value&quot; : &quot;419&quot;,
          &quot;label&quot; : &quot;419 - PANE, BISCOTTI, PRODOTTI DI PASTICCERIA&quot;
        }, {
          &quot;value&quot; : &quot;420&quot;,
          &quot;label&quot; : &quot;420 - ZUCCHERO&quot;
        }, {
          &quot;value&quot; : &quot;421&quot;,
          &quot;label&quot; : &quot;421 - PRODOTTI A BASE DI CACAO, CARAMELLE, GELATI&quot;
        }, {
          &quot;value&quot; : &quot;422&quot;,
          &quot;label&quot; : &quot;422 - PRODOTTI PER L ALIMENTAZIONE DEGLI ANIMALI&quot;
        }, {
          &quot;value&quot; : &quot;423&quot;,
          &quot;label&quot; : &quot;423 - ALTRI PRODOTTI ALIMENTARI&quot;
        }, {
          &quot;value&quot; : &quot;424&quot;,
          &quot;label&quot; : &quot;424 - ALCOOL ETILICO E PROD. DERIVATI&quot;
        }, {
          &quot;value&quot; : &quot;425&quot;,
          &quot;label&quot; : &quot;425 - CHAMPAGNE, VINI SPUMANTI, APERITIVI A BASE DI VINO&quot;
        }, {
          &quot;value&quot; : &quot;426&quot;,
          &quot;label&quot; : &quot;426 - SIDRO DI MELE E DI PERE&quot;
        }, {
          &quot;value&quot; : &quot;427&quot;,
          &quot;label&quot; : &quot;427 - MALTO, BIRRA, LIEVITO DI BIRRA&quot;
        }, {
          &quot;value&quot; : &quot;428&quot;,
          &quot;label&quot; : &quot;428 - ACQUE MINERALI E ANALCOOLICI&quot;
        }, {
          &quot;value&quot; : &quot;429&quot;,
          &quot;label&quot; : &quot;429 - PRODOTTI A BASE DI TABACCO&quot;
        }, {
          &quot;value&quot; : &quot;431&quot;,
          &quot;label&quot; : &quot;431 - FIBRE TESSILI, PRODOTTI DELLA FILATURA E SIMILI&quot;
        }, {
          &quot;value&quot; : &quot;432&quot;,
          &quot;label&quot; : &quot;432 - TESSUTI, VELI&quot;
        }, {
          &quot;value&quot; : &quot;436&quot;,
          &quot;label&quot; : &quot;436 - PRODOTTI DELLA MAGLIERIA&quot;
        }, {
          &quot;value&quot; : &quot;438&quot;,
          &quot;label&quot; : &quot;438 - TAPPETI E TAPPEZZERIE, TELE INCERATE E LINOLEUM&quot;
        }, {
          &quot;value&quot; : &quot;439&quot;,
          &quot;label&quot; : &quot;439 - ALTRI PRODOTTI TESSILI&quot;
        }, {
          &quot;value&quot; : &quot;441&quot;,
          &quot;label&quot; : &quot;441 - CONCIA E TINTURA PELLI E CUOIO&quot;
        }, {
          &quot;value&quot; : &quot;442&quot;,
          &quot;label&quot; : &quot;442 - ARTICOLI IN CUOIO E PELLE&quot;
        }, {
          &quot;value&quot; : &quot;451&quot;,
          &quot;label&quot; : &quot;451 - CALZATURE E PANTOFOLE IN CUOIO&quot;
        }, {
          &quot;value&quot; : &quot;453&quot;,
          &quot;label&quot; : &quot;453 - ARTICOLI DI ABBIGLIAMENTO ED ACCESSORI&quot;
        }, {
          &quot;value&quot; : &quot;455&quot;,
          &quot;label&quot; : &quot;455 - BIANCHERIA CASA E ARREDAMENTO&quot;
        }, {
          &quot;value&quot; : &quot;456&quot;,
          &quot;label&quot; : &quot;456 - ARTICOLI DI PELLICCERIA&quot;
        }, {
          &quot;value&quot; : &quot;461&quot;,
          &quot;label&quot; : &quot;461 - LEGNO SEGATO/PIALLATO/ESSICCATO&quot;
        }, {
          &quot;value&quot; : &quot;462&quot;,
          &quot;label&quot; : &quot;462 - LEGNO IMPIALLACCIATO, PANNELLI FIBRO-LEGNOSI&quot;
        }, {
          &quot;value&quot; : &quot;463&quot;,
          &quot;label&quot; : &quot;463 - CARPENTERIA E FALEGNAMERIA&quot;
        }, {
          &quot;value&quot; : &quot;464&quot;,
          &quot;label&quot; : &quot;464 - IMBALLAGGI IN LEGNO&quot;
        }, {
          &quot;value&quot; : &quot;465&quot;,
          &quot;label&quot; : &quot;465 - ARTICOLI IN LEGNO (ESCL.MOBILI)&quot;
        }, {
          &quot;value&quot; : &quot;466&quot;,
          &quot;label&quot; : &quot;466 - ART.IN SUGHERO/VIMINI/SCOPE ECC.&quot;
        }, {
          &quot;value&quot; : &quot;467&quot;,
          &quot;label&quot; : &quot;467 - MOBILI DI LEGNO E DI GIUNCO, MATERASSI&quot;
        }, {
          &quot;value&quot; : &quot;471&quot;,
          &quot;label&quot; : &quot;471 - PASTA PER CARTA, CARTA, CARTONI&quot;
        }, {
          &quot;value&quot; : &quot;472&quot;,
          &quot;label&quot; : &quot;472 - ARTICOLI IN PASTA DI CARTA, CARTA E CARTONI&quot;
        }, {
          &quot;value&quot; : &quot;473&quot;,
          &quot;label&quot; : &quot;473 - PRODOTTI DELLA STAMPA&quot;
        }, {
          &quot;value&quot; : &quot;474&quot;,
          &quot;label&quot; : &quot;474 - PRODOTTI DELL EDITORIA&quot;
        }, {
          &quot;value&quot; : &quot;481&quot;,
          &quot;label&quot; : &quot;481 - ARTICOLI IN GOMMA&quot;
        }, {
          &quot;value&quot; : &quot;482&quot;,
          &quot;label&quot; : &quot;482 - PNEUMATICI RIGENERATI&quot;
        }, {
          &quot;value&quot; : &quot;483&quot;,
          &quot;label&quot; : &quot;483 - ARTICOLI E MATERIALI PLASTICI&quot;
        }, {
          &quot;value&quot; : &quot;491&quot;,
          &quot;label&quot; : &quot;491 - GIOIELLI OREFICERIA E MONETE&quot;
        }, {
          &quot;value&quot; : &quot;492&quot;,
          &quot;label&quot; : &quot;492 - STRUMENTI MUSICALI&quot;
        }, {
          &quot;value&quot; : &quot;493&quot;,
          &quot;label&quot; : &quot;493 - PRODOTTI DELLA CINEMATOGRAFIA E DELLA FOTOGRAFIA&quot;
        }, {
          &quot;value&quot; : &quot;494&quot;,
          &quot;label&quot; : &quot;494 - GIOCHI, GIOCATTOLI E ARTICOLI SPORTIVI&quot;
        }, {
          &quot;value&quot; : &quot;495&quot;,
          &quot;label&quot; : &quot;495 - PENNE E PROD. NON CLASSIFICATI&quot;
        }, {
          &quot;value&quot; : &quot;505&quot;,
          &quot;label&quot; : &quot;505 - ABITAZIONI&quot;
        }, {
          &quot;value&quot; : &quot;506&quot;,
          &quot;label&quot; : &quot;506 - FABBRICATI NON RESIDENZIALI&quot;
        }, {
          &quot;value&quot; : &quot;507&quot;,
          &quot;label&quot; : &quot;507 - OPERE PUBBLICHE&quot;
        }, {
          &quot;value&quot; : &quot;509&quot;,
          &quot;label&quot; : &quot;509 - DEMOLIZIONE DI IMMOBILI&quot;
        }, {
          &quot;value&quot; : &quot;610&quot;,
          &quot;label&quot; : &quot;610 - SERVIZI DEL COMMERCIO ALL INGROSSO&quot;
        }, {
          &quot;value&quot; : &quot;611&quot;,
          &quot;label&quot; : &quot;611 - C.I. MATERIE PRIME AGRICOLE&quot;
        }, {
          &quot;value&quot; : &quot;612&quot;,
          &quot;label&quot; : &quot;612 - C.I. COMBUSTIBILI E PROD.CHIMICI&quot;
        }, {
          &quot;value&quot; : &quot;613&quot;,
          &quot;label&quot; : &quot;613 - C.I. LEGNAME E MAT.COSTRUZIONE&quot;
        }, {
          &quot;value&quot; : &quot;614&quot;,
          &quot;label&quot; : &quot;614 - C.I. MACCHINE E ATTREZZATURE&quot;
        }, {
          &quot;value&quot; : &quot;615&quot;,
          &quot;label&quot; : &quot;615 - C.I. MOBILI E ELETTRODOMESTICI&quot;
        }, {
          &quot;value&quot; : &quot;616&quot;,
          &quot;label&quot; : &quot;616 - C.I. TESSILI E ABBIGLIAMENTO&quot;
        }, {
          &quot;value&quot; : &quot;617&quot;,
          &quot;label&quot; : &quot;617 - C.I. ALIMENTI BEVANDE E TABACCO&quot;
        }, {
          &quot;value&quot; : &quot;618&quot;,
          &quot;label&quot; : &quot;618 - C.I. FARMACI SAPONI E PROFUMI&quot;
        }, {
          &quot;value&quot; : &quot;619&quot;,
          &quot;label&quot; : &quot;619 - C.I. PRODOTTI NON ALIMENTARI&quot;
        }, {
          &quot;value&quot; : &quot;620&quot;,
          &quot;label&quot; : &quot;620 - BENI DI RECUPERO&quot;
        }, {
          &quot;value&quot; : &quot;630&quot;,
          &quot;label&quot; : &quot;630 - INTERMEDIARI DEL COMMERCIO&quot;
        }, {
          &quot;value&quot; : &quot;640&quot;,
          &quot;label&quot; : &quot;640 - SERVIZI DEL COMMERCIO AL MINUTO&quot;
        }, {
          &quot;value&quot; : &quot;641&quot;,
          &quot;label&quot; : &quot;641 - C.M. FRUTTA LATTE CARNE E PESCE&quot;
        }, {
          &quot;value&quot; : &quot;642&quot;,
          &quot;label&quot; : &quot;642 - C.M. BEVANDE E SUPERMERCATI&quot;
        }, {
          &quot;value&quot; : &quot;643&quot;,
          &quot;label&quot; : &quot;643 - FARMACIE&quot;
        }, {
          &quot;value&quot; : &quot;644&quot;,
          &quot;label&quot; : &quot;644 - C.M. ARTICOLI DI BELLEZZA&quot;
        }, {
          &quot;value&quot; : &quot;645&quot;,
          &quot;label&quot; : &quot;645 - COMMERCIO AL MINUTO DI ARTICOLI DI ABBIGLIAMENTO&quot;
        }, {
          &quot;value&quot; : &quot;646&quot;,
          &quot;label&quot; : &quot;646 - C.M. CALZATURE E PELLETTERIA&quot;
        }, {
          &quot;value&quot; : &quot;647&quot;,
          &quot;label&quot; : &quot;647 - C.M. TESSUTI TAPPETI BIANCHERIA&quot;
        }, {
          &quot;value&quot; : &quot;648&quot;,
          &quot;label&quot; : &quot;648 - C.M. MOBILI E ELETTRODOMESTICI&quot;
        }, {
          &quot;value&quot; : &quot;649&quot;,
          &quot;label&quot; : &quot;649 - C.M. ARTICOLI DA REGALO E VARIE&quot;
        }, {
          &quot;value&quot; : &quot;651&quot;,
          &quot;label&quot; : &quot;651 - C.M. AUTO MOTO CICLI E RICAMBI&quot;
        }, {
          &quot;value&quot; : &quot;652&quot;,
          &quot;label&quot; : &quot;652 - COMMERCIO AL MINUTO DI CARBURANTI E LUBRIFICANTI&quot;
        }, {
          &quot;value&quot; : &quot;653&quot;,
          &quot;label&quot; : &quot;653 - C.M. LIBRI/GIORNALI/ART.UFFICIO&quot;
        }, {
          &quot;value&quot; : &quot;654&quot;,
          &quot;label&quot; : &quot;654 - C.M. ARTICOLI NON ALTROVE CLASS.&quot;
        }, {
          &quot;value&quot; : &quot;655&quot;,
          &quot;label&quot; : &quot;655 - C.M. ARTICOLI D OCCASIONE&quot;
        }, {
          &quot;value&quot; : &quot;656&quot;,
          &quot;label&quot; : &quot;656 - C.M. PROD.DIVERSI NON ALIMENTARI&quot;
        }, {
          &quot;value&quot; : &quot;660&quot;,
          &quot;label&quot; : &quot;660 - SERVIZI DEGLI ALBERGHI E PUBBLICI ESERCIZI&quot;
        }, {
          &quot;value&quot; : &quot;671&quot;,
          &quot;label&quot; : &quot;671 - RIPARAZIONE VEICOLI E BICICLETTE&quot;
        }, {
          &quot;value&quot; : &quot;672&quot;,
          &quot;label&quot; : &quot;672 - RIPARAZIONI VARIE (ESCL.VEICOLI)&quot;
        }, {
          &quot;value&quot; : &quot;710&quot;,
          &quot;label&quot; : &quot;710 - TRASPORTI E SERVIZI FERROVIARI&quot;
        }, {
          &quot;value&quot; : &quot;721&quot;,
          &quot;label&quot; : &quot;721 - TRASPORTI METRO/TRAM/AUTOBUS&quot;
        }, {
          &quot;value&quot; : &quot;722&quot;,
          &quot;label&quot; : &quot;722 - SERVIZI TRASPORTI SU STRADA DI PERSONE&quot;
        }, {
          &quot;value&quot; : &quot;723&quot;,
          &quot;label&quot; : &quot;723 - SERVIZI TRASPORTI SU STRADA DI MERCI&quot;
        }, {
          &quot;value&quot; : &quot;724&quot;,
          &quot;label&quot; : &quot;724 - SERVIZI PER OLEODOTTO E GASDOTTO&quot;
        }, {
          &quot;value&quot; : &quot;725&quot;,
          &quot;label&quot; : &quot;725 - TRASPORTI NON ALTROVE CLASSIF.&quot;
        }, {
          &quot;value&quot; : &quot;730&quot;,
          &quot;label&quot; : &quot;730 - SERVIZI DI NAVIGAZIONE INTERNA&quot;
        }, {
          &quot;value&quot; : &quot;741&quot;,
          &quot;label&quot; : &quot;741 - SERVIZI DEI TRASPORTI MARITTIMI&quot;
        }, {
          &quot;value&quot; : &quot;742&quot;,
          &quot;label&quot; : &quot;742 - SERVIZI DEI TRASPORTI DI CABOTAGGIO&quot;
        }, {
          &quot;value&quot; : &quot;750&quot;,
          &quot;label&quot; : &quot;750 - SERVIZI DEI TRASPORTI AEREI&quot;
        }, {
          &quot;value&quot; : &quot;761&quot;,
          &quot;label&quot; : &quot;761 - SERV.CONNESSI AI TRASP.TERRESTRI&quot;
        }, {
          &quot;value&quot; : &quot;762&quot;,
          &quot;label&quot; : &quot;762 - SERVIZI CONNESSI ALLA NAVIGAZIONE INTERNA&quot;
        }, {
          &quot;value&quot; : &quot;763&quot;,
          &quot;label&quot; : &quot;763 - SERV.CONNESSI AI TRASP.MARITTIMI&quot;
        }, {
          &quot;value&quot; : &quot;764&quot;,
          &quot;label&quot; : &quot;764 - SERVIZI CONNESSI AI TRASPORTI AEREI&quot;
        }, {
          &quot;value&quot; : &quot;771&quot;,
          &quot;label&quot; : &quot;771 - SERVIZI DELLE AGENZIE DI VIAGGIO&quot;
        }, {
          &quot;value&quot; : &quot;772&quot;,
          &quot;label&quot; : &quot;772 - SERVIZI DEGLI INTERMEDIARI DEI TRASPORTI&quot;
        }, {
          &quot;value&quot; : &quot;773&quot;,
          &quot;label&quot; : &quot;773 - SERVIZI DI CUSTODIA E DI DEPOSITO&quot;
        }, {
          &quot;value&quot; : &quot;790&quot;,
          &quot;label&quot; : &quot;790 - SERVIZI DELLE COMUNICAZIONI&quot;
        }, {
          &quot;value&quot; : &quot;830&quot;,
          &quot;label&quot; : &quot;830 - SERV.FIN./IMM./FISCALI/TECNICI&quot;
        }, {
          &quot;value&quot; : &quot;840&quot;,
          &quot;label&quot; : &quot;840 - SERV.DI NOLEGGIO DI BENI MOBILI&quot;
        }, {
          &quot;value&quot; : &quot;850&quot;,
          &quot;label&quot; : &quot;850 - SERVIZI DELLA LOCAZIONE DI BENI IMMOBILI&quot;
        }, {
          &quot;value&quot; : &quot;920&quot;,
          &quot;label&quot; : &quot;920 - SERV.NETTEZZA URB/DISIFEZ/PULIZ.&quot;
        }, {
          &quot;value&quot; : &quot;930&quot;,
          &quot;label&quot; : &quot;930 - SERVIZI DELL INSEGNAMENTO&quot;
        }, {
          &quot;value&quot; : &quot;940&quot;,
          &quot;label&quot; : &quot;940 - SERVIZI DELLA RICERCA E SVILUPPO&quot;
        }, {
          &quot;value&quot; : &quot;950&quot;,
          &quot;label&quot; : &quot;950 - SERVIZI SANITARI DESTINABILI ALLA VENDITA&quot;
        }, {
          &quot;value&quot; : &quot;960&quot;,
          &quot;label&quot; : &quot;960 - SERV.ASSOCIAZIONI/ORGANIZZAZIONI&quot;
        }, {
          &quot;value&quot; : &quot;970&quot;,
          &quot;label&quot; : &quot;970 - SERVIZI RICREATIVI E CULTURALI&quot;
        }, {
          &quot;value&quot; : &quot;981&quot;,
          &quot;label&quot; : &quot;981 - SERVIZI DI LAVANDERIA, TINTORIA E SIMILI&quot;
        }, {
          &quot;value&quot; : &quot;982&quot;,
          &quot;label&quot; : &quot;982 - SERV.PARRUCCHIERI/IST.BELLEZZA&quot;
        }, {
          &quot;value&quot; : &quot;983&quot;,
          &quot;label&quot; : &quot;983 - SERVIZI DEGLI STUDI FOTOGRAFICI&quot;
        }, {
          &quot;value&quot; : &quot;984&quot;,
          &quot;label&quot; : &quot;984 - ALTRI SERVIZI PERSONALI&quot;
        } ],
        &quot;AtecoShortCode&quot; : &quot;&quot;,
        &quot;RecordId&quot; : &quot;a1i9O000005UQS1QAO&quot;,
        &quot;AtecoFlagSociety&quot; : false,
        &quot;IsRaeDisabled&quot; : true,
        &quot;AtecoTypeCode&quot; : &quot;&quot;,
        &quot;IsAtecoDisabled&quot; : true,
        &quot;IsSaeDisabled&quot; : true,
        &quot;IsActivityDisabled&quot; : true,
        &quot;IsShortAtecoDisabled&quot; : true,
        &quot;AtecoFlagConnection&quot; : false,
        &quot;AtecoShort&quot; : &quot;&quot;,
        &quot;AtecoRAE&quot; : &quot;&quot;,
        &quot;AtecoActivityType&quot; : &quot;&quot;,
        &quot;AtecoSAE&quot; : &quot;&quot;,
        &quot;AtecoCode&quot; : &quot;&quot;
      }
    }
  },
  &quot;CompagnieMandato&quot; : &quot;null&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>AnagDetailTransformPG_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
