import { LightningElement, wire, track, api} from 'lwc';
import { OmniscriptBaseMixin } from 'omnistudio/omniscriptBaseMixin';
import { CurrentPageReference } from 'lightning/navigation';

import apexGetTitoliInScadenzaFromIntegrationProcedure from '@salesforce/apex/TitoliFeiPermissionHelper.getTitoliInScadenzaFromIntegrationProcedure';
import apexGetUserMandati from '@salesforce/apex/TitoliFeiPermissionHelper.getUserMandati';
import apexGetParamsForFei from '@salesforce/apex/TitoliFeiPermissionHelper.getParamsForFei';
import apexEnableActionsAsNeeded from '@salesforce/apex/TitoliFeiPermissionHelper.enableActionsAsNeeded';
import apexGetPrt from '@salesforce/apex/TitoliFeiPermissionHelper.getPrt';
import apexGetAccountData from '@salesforce/apex/TitoliFeiPermissionHelper.getAccountData';
import apexGetAccountDetailsData from '@salesforce/apex/TitoliFeiPermissionHelper.getAccountDetailsData';
import apexGetPostalizzatoreMotiviRistampa from '@salesforce/apex/TitoliFeiPermissionHelper.getPostalizzatoreMotiviRistampa';
import apexInvioRemoto from '@salesforce/apex/TitoliFeiPermissionHelper.invioRemoto';
import abilitaAzioneRiprendi from '@salesforce/apex/TitoliFeiPermissionHelper.abilitaAzioneRiprendi';

const actionsProdottoUnico = [
    { label: 'Completa', name: 'completa', disabled: false }, 
    { label: 'Incassa', name: 'incassa', disabled: false },
    { label: 'FEA', name: 'fea', disabled: true },
    { label: 'Firma', name: 'firma', disabled: true },
    { label: 'Rettifica', name: 'rettifica', disabled: true },
    { label: 'Stampa', name: 'stampa', disabled: false },
    { label: 'Scheda QT', name: 'schedaQT', disabled: false }
];

const actionsVecchiPortafogli = [
    { label: 'Incasso', name: 'incasso', disabled: false },
    { label: 'Incasso multiplo', name: 'incassoMultiplo', disabled: false },
    { label: 'Stampa', name: 'stampa', disabled: false },
    { label: 'FEA', name: 'fea', disabled: false },
    { label: 'Riprendi', name: 'riprendi', disabled: true },
    { label: 'Rettifica', name: 'rettifica', disabled: false },
    { label: 'Vedi Documento', name: 'vediDocumento', disabled: false },
    { label: 'Sostituzione', name: 'sostituzione', disabled: false },
    { label: 'Variazione', name: 'variazione', disabled: false },
    { label: 'Scheda QT', name: 'schedaQT', disabled: false },
    { label: 'Invio da Remoto', name: 'invioDaRemoto', disabled: false },
];

export default class TitoliInScadenzaCmp extends OmniscriptBaseMixin(LightningElement) {

    actionsProdottoUnico = actionsProdottoUnico;
    actionsVecchiPortafogli = actionsVecchiPortafogli;
    titoliInScadenzaColumn = [];
    titoliInScadenzaData = [];
    datatableRendered = false;
    isFlowModalOpened = false;
    isLWCModalOpened = false;
    isInvioRemotoModalOpened = false;
    postalizzaVisible = true;
    selectedMotivo = null;
    emailType = null;
    isConfermaDisabled = true;
    labelLoadingTitoli = 'Caricamento titoli';
    showSpinner = false;

    motivoRistampaOptions = [
        {descrizione: "", codice: ""},
        {descrizione: "Stampa precedente rovinata", codice: 1},
        {descrizione: "Stampa precedente non avvenuta", codice: 2},
        {descrizione: "Stampa precedente smarrita/rubata", codice: 3},
        {descrizione: "Incasso in punto vendita diverso da quello dove si è stampata la precedente", codice: 4},
        {descrizione: "Carta Verde per certificato dematerializzato", codice: 5},
        {descrizione: "Richiesta agenziale", codice: 6},
        {descrizione: "Richiesta agenziale - documento annullato", codice: 7},
        {descrizione: "Email non ricevuta", codice: 8},
        {descrizione: "Lettera non ricevuta", codice: 9},
        {descrizione: "Raccolta firma", codice: 10},
        {descrizione: "Altro", codice: 999}
    ];

    motivoRistampaMap = {};

    rowSelected = {};

    societyNameToIdMap = new Map([['Unipol', "SOC_1"],['UniSalute', "SOC_4"]]);

    params = {
        feiId: "feiId",
        fiscalcode: "CF",
        feiRequestPayload: "{}",
        permissionSetName: "permissionSetName",
        society:"society"
    }

    @wire(CurrentPageReference)
    currentPageReference;

    @api accountId;
    accountData;
    accountDetailsData;
    
    connectedCallback() {

        if(this.currentPageReference?.state.c__AccountId != undefined){
            this.accountId = this.currentPageReference.state.c__AccountId;
        }

        this.getTitoliInScadenzaData();
        this.getUserMandati();
        this.getAccountData();
        this.getAccountDetailsData();
        //this.getPostalizzatoreMotiviRistampa();
    }

    renderedCallback(){

        /*
        if(this.postalizzaVisible){
            this.template.querySelector('.postalizza')?.classList.remove('slds-hide');
        } else if(!this.postalizzaVisible){
            this.template.querySelector('.postalizza')?.classList.add('slds-hide');
        }
        */
    }

    getTitoliInScadenzaData(){
        this.showSpinner = true;
        apexGetTitoliInScadenzaFromIntegrationProcedure({accountId : this.accountId})
        .then((result) => {
            this.titoliInScadenzaData = Array.isArray(result['Response']) ? result['Response'] : [result['Response']];
            //this.titoliInScadenzaData = JSON.parse('[{"isFEAAttiva":false,"NPOLFLDValue":"*********","isFirmaAttiva":false,"isProdottoUnico":true,"omnicanalita":"Multicanalità off","tipo":"Nuova Emissione","premio":4.56,"isCompletaAttiva":true,"isIncassoAttiva":true,"isVariazioneAttiva":true,"titoliAlLegale":"NO","isFEAVisualizzata":false,"nFolder":"101853*********","inviiRemoti":"0","ITCTValue":"61952f50-bbe1-4191-ad9f-dee55a8f06ac","numeroAppendice":"0","agenzia":1853,"dataEffettoTitolo":"2025-09-04","polizza":"*********","idContrattoPTF":"61952f50-bbe1-4191-ad9f-dee55a8f06ac","prodottoAbilitatoInvioFea":true,"isRettificaAttiva":false,"dataScadenzaTitolo":"04-09-2025","operazione":"I","inviiAr":"0","idTitolo":"*********","firmata":"NO","emessaNonIncassabile":false,"rettificabile":"NO","compagnia":"unipol","isIncassaAttiva":false,"frazionamento":"9","unibox":0,"isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true},{"isFEAAttiva":false,"NPOLFLDValue":"*********","isFirmaAttiva":false,"isProdottoUnico":true,"omnicanalita":"Multicanalità off","tipo":"Quietanza","numeroArchivioProdottoUnico":"00161672419","premio":4.43,"isCompletaAttiva":true,"isIncassoAttiva":true,"isVariazioneAttiva":true,"titoliAlLegale":"NO","isFEAVisualizzata":true,"contId":"697152820061986202","nFolder":"101853*********","xquietanzaId":"248887430","ambito":"Danni non auto","tipoOp":"13","inviiRemoti":"0","ITCTValue":"61952f50-bbe1-4191-ad9f-dee55a8f06ac","numeroAppendice":"0","numeroArchivio":"00161357960","dataEffettoPolizza":"2025-09-03T22:00:00.000Z","agenzia":1853,"dtEffVar":"2025-09-04","dataEffettoTitolo":"2025-10-04","polizza":"*********","idContrattoPTF":"61952f50-bbe1-4191-ad9f-dee55a8f06ac","prodottoAbilitatoInvioFea":true,"idDoc":"117296184","docItemId":"999967059442305826","isRettificaAttiva":false,"subAgenzia":"105","dataScadenzaTitolo":"04-10-2025","operazione":"I","inviiAr":"0","idTitolo":"635477041","firmata":"NO","tipoDocumento":1000,"emessaNonIncassabile":true,"rettificabile":"NO","compagnia":"unipol","isIncassaAttiva":false,"frazionamento":"9","unibox":0,"isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true}]'); //Guy De Roquefeuil: Mock for IP response Unipol
            //this.titoliInScadenzaData = JSON.parse('[{ "isInvioDaRemotoAttiva": true, "isFEAAttiva":false,"NPOLFLDValue":"528","isFirmaAttiva":false,"isProdottoUnico":false,"omnicanalita":"Multicanalità Dispositiva","tipo":"Quietanza","premio":133.65,"isCompletaAttiva":true,"isIncassoAttiva":true,"isVariazioneAttiva":true,"titoliAlLegale":"NO","modello":"-","isFEAVisualizzata":true,"contId":"564967463897515902","nFolder":"-","esposizioneAR":"2024-02-27","xquietanzaId":"203608434","ambito":"Danni non auto","tipoOp":"13","inviiRemoti":"0","numeroAppendice":"0","numeroArchivio":"00000363725","dataEffettoPolizza":"2023-02-28T23:00:00.000Z","agenzia":"4654","dtEffVar":"2023-03-01","dataEffettoTitolo":"2025-02-28","polizza":"011096528","prodottoAbilitatoInvioFea":true,"idDoc":"55603952","docItemId":"999967059380238308","targa":"-","isRettificaAttiva":false,"subAgenzia":"009","dataScadenzaTitolo":"01-03-2025","operazione":"I","inviiAr":"0","idTitolo":"605160514","firmata":"NO","tipoDocumento":1000,"emessaNonIncassabile":false,"rettificabile":"NO","compagnia":"UniSalute","isIncassaAttiva":false,"frazionamento":"ANNUALE","unibox":0,"isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true},{"isFEAAttiva":false,"NPOLFLDValue":"528","isFirmaAttiva":false,"isProdottoUnico":false,"omnicanalita":"Multicanalità Dispositiva","tipo":"Quietanza","premio":129.83,"isCompletaAttiva":true,"isIncassoAttiva":true,"isVariazioneAttiva":true,"titoliAlLegale":"SI","modello":"-","isFEAVisualizzata":true,"contId":"564967463897515902","nFolder":"-","esposizioneAR":"2024-02-27","xquietanzaId":"203608434","ambito":"Danni non auto","tipoOp":"13","inviiRemoti":"0","numeroAppendice":"0","numeroArchivio":"00000363725","dataEffettoPolizza":"2023-02-28T23:00:00.000Z","agenzia":"4654","dtEffVar":"2023-03-01","dataEffettoTitolo":"2024-02-29","polizza":"011096528","prodottoAbilitatoInvioFea":true,"idDoc":"55603952","docItemId":"999967059380238308","targa":"-","isRettificaAttiva":false,"subAgenzia":"009","dataScadenzaTitolo":"01-03-2024","operazione":"I","inviiAr":"0","idTitolo":"578684790","firmata":"NO","tipoDocumento":1000,"emessaNonIncassabile":false,"rettificabile":"NO","compagnia":"UniSalute","isIncassaAttiva":false,"frazionamento":"ANNUALE","unibox":0,"isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true, "isInvioDaRemotoAttiva": true}]'); //TB Mock for IP response Essig Unisalute
            //this.titoliInScadenzaData = JSON.parse('[{"isFEAAttiva":false,"NPOLFLDValue":"097","isFirmaAttiva":false,"tipo":"Quietanza","titoliAlLegale":"NO","isVediDocumentoAttiva":true,"ambito":"Danni non auto","inviiRemoti":"0","isSostituzioneAttiva":true,"numeroAppendice":"0","dataEffettoPolizza":"2025-07-22T22:00:00.000Z","agenzia":"1853","dataEffettoTitolo":"2026-07-22","polizza":"204559097","ramo":"071","prodottoAbilitatoInvioFea":true,"targa":"-","inviiAr":"0","idTitolo":"626584273","tipoDocumento":1000,"rettificabile":"NO","isInvioDaRemotoAttiva":true,"compagnia":"Unipol","isIncassaAttiva":false,"unibox":0,"polizzaPosizione":"071/204559097","isProdottoUnico":false,"omnicanalita":"Multicanalità off","premio":12125,"isCompletaAttiva":true,"isStampaEssigAttiva":true,"isIncassoAttiva":true,"isVariazioneAttiva":true,"modello":"-","isFEAVisualizzata":true,"contId":"906539385165102801","nFolder":"-","xquietanzaId":"246409204","tipoOp":"13","numeroArchivio":"00175344907","dtEffVar":"2025-07-23","idDoc":"117282590","docItemId":"999967059442292232","isRettificaAttiva":false,"subAgenzia":"100","dataScadenzaTitolo":"23-07-2026","operazione":"I","firmata":"NO","emessaNonIncassabile":false,"isStampabileInFEA":true,"frazionamento":"ANNUALE","isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true}]');
            console.log('titoliInScadenzaDataResult: '+JSON.stringify(this.titoliInScadenzaData));
            
            this.enableActionsAsNeeded();
            this.showSpinner = false;
        })
        .catch((error) => {
            console.log('err getTitoli: ' + JSON.stringify(error));
            this.showSpinner = false;
        });
    }

    getUserMandati(){
        apexGetUserMandati({accId: this.accountId})
        .then((result) => {
            this.showUnipolButton = result['hasMandatoUnipol']; //TODO: aggiungi custom permission
            this.showUniSaluteButton = result['hasMandatoUnisalute'];
        })
        .catch((error) => {
            console.log(JSON.stringify('err in getUserMandati: ' + error));
        });
    }

    getAccountData(){
        apexGetAccountData({accountId: this.accountId})
        .then(resultAcc => {
            this.accountData = resultAcc;
            console.log('accountData:  ' + JSON.stringify(resultAcc));
        })
        .catch((error) => 
            console.log(JSON.stringify('err in geAccountData: ' + error))
        )
    }

    getAccountDetailsData(){
        apexGetAccountDetailsData({accountId: this.accountId})
        .then(resultDetails => {
            this.accountDetailsData = resultDetails;
            console.log('detailsData: ' + JSON.stringify(resultDetails));
        })
        .catch((error) => 
            console.log(JSON.stringify('err in getAccountDetailsData: ' + error))
        )
    }

    getPostalizzatoreMotiviRistampa(){
        apexGetPostalizzatoreMotiviRistampa({})
        .then(result => {

            const postalizzazione = result?.Postalizzazione;
            const motiviRistampa = result?.ListaRistampa;
            //abilitato
            if (postalizzazione?.attivo === true) {

                this.postalizzaVisible = true; 
            } else {
                this.postalizzaVisible = false;
            }

            this.motivoRistampaOptions = Array.isArray(motiviRistampa) ? motiviRistampa : [];

            if (Array.isArray(motiviRistampa)) {
                motiviRistampa.forEach(motivo => {
                    
                    this.motivoRistampaMap[motivo.codice] = motivo.descrizione;
                });
            }
        })
        .catch((error) => 
            console.log(JSON.stringify('err in getPostalizzatore: ' + error))
        )
    }

    getRowActions(row, doneCallback) {
        const matchingRow = this.titoliInScadenzaData.find(r => r.rowId === row.rowId);
        const actions = matchingRow?.rowActions || [];
        doneCallback(actions);
    }

    enableActionsAsNeeded(){
        this.datatableRendered = false;
        this.showSpinner = true;
        apexEnableActionsAsNeeded({actionsJSON: JSON.stringify(this.actionsProdottoUnico)})
        .then((result) => {
            let arrayTitoli = [];
            let rowsProdottoUnico = this.titoliInScadenzaData.filter(prodottoUnicoRow => prodottoUnicoRow.isProdottoUnico === true);
            rowsProdottoUnico.forEach(titolo => {

                let feaPresent = true;
                let feaAction = {};
                let firmaAction = {};
                let currentRow = { ...titolo };

                currentRow.rowId = `${currentRow.nFolder || ''}_${currentRow.agenzia || ''}_${Math.random().toString(36).substring(2, 8)}`;
                if(currentRow.polizza != null && currentRow.polizza != undefined){
                    currentRow.polizza = currentRow.polizza.length > 1 ? currentRow.polizza.toString().replaceAll(',', '-') : currentRow.polizza.toString();
                    currentRow.ramoPolizza = currentRow.ramo != undefined ? currentRow.ramo + '/' + currentRow.polizza : currentRow.polizza;
                }
                if (currentRow.esposizioneAR != null && currentRow.esposizioneAR != undefined) {
                    let esposizioneArray = currentRow.esposizioneAR.split('-');
                    currentRow.esposizioneAR = esposizioneArray[2] + '-' +  esposizioneArray[1] + '-' +  esposizioneArray[0];
                }
                currentRow.rowActions = result.map(action => ({ ...action }));

                currentRow.rowActions.forEach(action => {

                    if (action.disabled === true && (action.name !== 'fea' || (action.name !== 'firma' && feaPresent === false))) {
                        return;
                    }

                    switch (action.name) {
                        case 'fea':
                            
                            if (currentRow?.tipo !== 'Quietanza') {
                                feaAction = action;
                                feaPresent = false;
                            } else {

                                if (currentRow?.isStampabileInFEA === false || currentRow?.prodottoAbilitatoInvioFea === false || currentRow?.inviiRemoti !== '0' || currentRow.inviiAr !== '0')  {
                                    action.disabled = true;
                                }
                            }
                                    
                            break;
                        
                        case 'completa':
                            if (currentRow?.isCompletaAttiva !== true) {
                                action.disabled = true;
                            }
                            /*
                            if (currentRow?.operazione === 'R' || (currentRow?.firmata === 'Firmato' && (currentRow?.emessaNonIncassabile === false || (currentRow?.frazionamento !== undefined && currentRow?.frazionamento.toLowerCase() !== 'annuale'))) || (currentRow?.idContrattoPTF === null || currentRow?.idContrattoPTF === undefined)){
                                action.disabled = true;
                            }
                            */
                            break;
                    
                        case 'incassa':
                            
                            if (currentRow?.isIncassaAttiva !== true) {
                                action.disabled = true;
                            }
                            /*
                            if(currentRow?.operazione === 'R' || currentRow?.firmata === 'Non firmato' || currentRow?.emessaNonIncassabile === true){
                                action.disabled = true;
                            }
                            */
                            break;

                        case 'rettifica':
                           
                            if (currentRow?.isRettificaAttiva !== true) {
                                action.disabled = true;
                            }
                            /*
                            if (currentRow?.operazione === 'I') {
                                action.disabled = true;
                            }
                            */
                            break;
                        
                        case 'firma':

                            if (feaPresent === true) {
                                firmaAction = action;
                            } else {
                                if (currentRow?.isFirmaAttiva !== true) {
                                    action.disabled = true;
                                }
                                /*
                                if (currentRow?.firmata === 'Firmato' || (currentRow?.idContrattoPTF === null || currentRow?.idContrattoPTF === undefined)) {
                                    action.disabled = true;
                                }
                                */
                            }
                            break;

                        case 'schedaQT':
                                
                            if ((currentRow?.uplCrmQuietId === null || currentRow?.uplCrmQuietId  === undefined) || (currentRow?.contId === null || currentRow.contId === undefined)) {
                                action.disabled = true;
                            }
                            break;

                        default:
                            break;
                    }
                    
                });

                if (feaPresent == false) {
                    currentRow.rowActions.splice(currentRow.rowActions.indexOf(feaAction), 1);
                } else if (feaPresent == true) {
                    currentRow.rowActions.splice(currentRow.rowActions.indexOf(firmaAction), 1);
                }

                arrayTitoli.push(currentRow);
            });

            let rowsVecchiPortafogli = this.titoliInScadenzaData.filter(prodottoUnicoRow => prodottoUnicoRow.isProdottoUnico !== true);
            this.titoliInScadenzaData = [];
            rowsVecchiPortafogli.forEach(titolo => {

                let currentRow = { ...titolo };

                currentRow.rowId = `${currentRow.nFolder || ''}_${currentRow.agenzia || ''}_${Math.random().toString(36).substring(2, 8)}`;
                if(currentRow.polizza != null && currentRow.polizza != undefined){
                    currentRow.polizza = currentRow.polizza.length > 1 ? currentRow.polizza.toString().replaceAll(',', '-') : currentRow.polizza.toString();
                    currentRow.ramoPolizza = currentRow.ramo != undefined ? currentRow.ramo + '/' + currentRow.polizza : currentRow.polizza;
                }
                if (currentRow.esposizioneAR != null && currentRow.esposizioneAR != undefined) {
                    let esposizioneArray = currentRow.esposizioneAR.split('-');
                    currentRow.esposizioneAR = esposizioneArray[2] + '-' +  esposizioneArray[1] + '-' +  esposizioneArray[0];
                }
                console.log('actions vecchiP: ' + JSON.stringify(this.actionsVecchiPortafogli));
                currentRow.rowActions = this.actionsVecchiPortafogli.map(action => ({ ...action }));
                console.log('row actions vecchi: ' + JSON.stringify(currentRow.rowActions));
                currentRow.rowActions.forEach(action => {

                    switch (action.name) {
                        case 'incasso':

                            if (currentRow?.isIncassoAttiva !== true) {
                                action.disabled = true;
                            }
                            break;
                        
                        case 'incassoMultiplo':

                            if (currentRow?.isIncassoAttiva !== true) {
                                action.disabled = true;
                            }
                            break;
                        
                        case 'rettifica':
                            if (currentRow?.isRettificaAttiva !== true) {
                                
                                action.disabled = true;
                            }
                            break;
                        
                        case 'schedaQT':
                                
                            if ((currentRow?.uplCrmQuietId === null || currentRow?.uplCrmQuietId  === undefined) || (currentRow?.contId === null || currentRow.contId === undefined)) {
                                action.disabled = true;
                            }
                            break;
                        
                        case 'vediDocumento':
                                
                            if (currentRow?.isVediDocumentoAttiva !== true) {
                                
                                action.disabled = true;
                            }
                            break;

                        case 'sostituzione':
                                
                            if (currentRow?.isSostituzioneAttiva !== true) {
                                
                                action.disabled = true;
                            }
                            break;
                        
                        case 'variazione':
                                
                            if (currentRow?.isVariazioneAttiva !== true) {
                                
                                action.disabled = true;
                            }
                            break;

                        case 'stampa':

                            if (currentRow?.isStampaEssigAttiva !== true) {
                                action.disabled = true;
                            }

                        case 'invioDaRemoto':

                            if (currentRow?.isInvioDaRemotoAttiva !== true) {
                                action.disabled = true;
                            }

                        case 'fea':

                            if (currentRow?.xquietanzaId === '' || currentRow?.xquietanzaId === null) {
                                action.disabled = true;
                            }

                        default:
                            break;
                    }
                    
                });

                arrayTitoli.push(currentRow);
            });

            console.log('titoliInScadenzaData post lavorazione: ' + JSON.stringify(arrayTitoli));
            this.titoliInScadenzaData = [...arrayTitoli];
            this.titoliInScadenzaColumn = [
                { label: 'Società', fieldName: 'compagnia' },
                { label: 'Tipo', fieldName: 'tipo' },
                { label: 'Ambito', fieldName: 'ambito' },
                { label: 'N. Folder', fieldName: 'nFolder' },
                { label: 'N. Polizza/Posizione', fieldName: 'ramoPolizza' },
                { label: 'Scadenza', fieldName: 'dataScadenzaTitolo' },
                { label: 'Frazionamento', fieldName: 'frazionamento' },
                { label: 'Premio (comprensivo di Unibox)', fieldName: 'premio', type: 'currency' },
                { label: 'di cui Unibox', fieldName: 'unibox', type: 'currency' },
                { label: 'Omnicanalità', fieldName: 'omnicanalita' },
                { label: 'Esposizione AR', fieldName: 'esposizioneAR', cellAttributes: { alignment: 'center' }},
                { label: 'Targa', fieldName: 'targa' },
                { label: 'Modello', fieldName: 'modello' },
                { label: 'Titoli al legale', fieldName: 'titoliAlLegale' },
                { label: 'Rettificabile', fieldName: 'rettificabile' },
                { label: 'Firmato', fieldName: 'firmata' },
                { type: 'action', typeAttributes: { rowActions: this.getRowActions.bind(this), menuAlignment: 'auto'} }
            ];
            this.datatableRendered = true;
            this.showSpinner = false;
        })
        .catch((error) => {
            console.log('err in ROWS: ' + JSON.stringify(error));
            this.showSpinner = false;
        });
    }

    handleStampe(event){
         const feiId = event.currentTarget.dataset.feiId;
        const society = event.currentTarget.dataset.societyId;
        this.flowInputs = [
            { name: 'FEIID', type: 'String', value: feiId},
            { name: 'recordId', type: 'String', value: this.accountId },
            { name: 'society', type: 'String', value: society }         
        ];
        this.toggleFlowModal();
    }

    toggleFlowModal(){
        this.isFlowModalOpened = !this.isFlowModalOpened;
    }

    toggleLWCModal(){
        this.isLWCModalOpened = !this.isLWCModalOpened;
    }

    handleFlowStatusChange(event) {
        if (event.detail.status === 'FINISHED' || event.detail.status === 'ERROR')
           this.toggleFlowModal();
    }

    handleRowAction(event){
        const actionName = event.detail.action.name;
        const row = event.detail.row;
        const compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
        console.log("actionName: " + actionName);
        switch (actionName) {
        case 'completa': {
                let feiId = 'PU.COMPLETA';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({contractid: row.idContrattoPTF}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'incassa': {
                let feiId = 'PU.INCASSO';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({agencyCode: '{$agenzia}', companyCode: '{$compagnia}', folderId: row.nFolder}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'firma': {
                let feiId = 'PU.FIRMA';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({contractid: row.idContrattoPTF}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'rettifica': {

                let feiId = row.isProdottoUnico == true ? 'PU.RETTIFICA' : 'CP.INCASSO';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {

                    if (feiId === 'PU.RETTIFICA') {
                        
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({agencyCode: '{$agenzia}', companyCode: '{$compagnia}', folderId: row.nFolder}),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || "",
                            society: this.societyNameToIdMap.get(row.compagnia) || ''
                        };

                    } else if(feiId === 'CP.INCASSO'){
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({operazione: 'R', w_Codage: row.agenzia, w_Compag: compagniaCode, w_Data: row.dataEffettoTitolo, w_Numpol: row.polizza, w_Ramopo: row.ramo}),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || "",
                            society: this.societyNameToIdMap.get(row.compagnia) || ''
                        };
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'schedaQT': {
                let feiId = 'SCHEDA.QT';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({xqt: row.uplCrmQuietId, contid: row.contId, agency: row.agenzia}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'stampa': {
                let stampaParams = this.getDefaultFEAParams(row);
                console.log('row selected: ' +JSON.stringify(row));
                stampaParams.prtForPrint = true;
                let uuid = self.crypto.randomUUID();
                console.log('uuid: '+uuid+'\n');
                let tipoDocumento = row.tipo != undefined && row.tipo == 'Quietanza' ? "Q" : row.tipo != undefined && row.tipo == "Regolazione Premio" ? "QTREG" : "";
                stampaParams.body = JSON.stringify({
                    ristampa: true,
                    fromFe: true,
                    motRistampaTpCd: "-1",
                    testoMotRistampa: "Stampa precedente rovinata",
                    flagAnteprima: false,
                    flagGeneraDocumenti: false,
                    listaDocumentiDaStampare: [
                        {
                            tipoDocumento: tipoDocumento,
                            idDocumento: row?.idDoc
                        }
                    ],
                    docTpCd: row?.tipoDocumento,
                    guid: uuid,
                    agenziaFiglia: row?.agenzia.toString(),
                    agenziaMadre: row?.agenzia.toString(),
                    compagnia: compagniaCode
                });

                console.log(`stampaParams: ${JSON.stringify(stampaParams)}`);
                apexGetPrt({inputParams: stampaParams})
                .then(resultPrt => {
                    console.log('resultPrt: ' + JSON.stringify(resultPrt));
                    const feiId = 'SFEA.PRINT.PAPER';
                    apexGetParamsForFei({feiId: feiId})
                    .then(result => {
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({
                                feiRequest: {
                                    requestCode: "SFEA.PRINT.PAPER",
                                    feiRequest: {
                                        feasRequest: {
                                            mnuAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/scheda_cliente',
                                            srcAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page',
                                            srcAppFailBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page', //rotta da definire
                                            cmp: compagniaCode,
                                            prj: "LEON",
                                            app: "SFCRM",
                                            finalPrint: false,
                                            saveStatus: false,
                                            sendMail: false,
                                            sendSms: false,
                                            keys: {
                                                NARC: {
                                                    value: row.numeroArchivio,
                                                    name: "NARC"
                                                },
                                                SUBAG: {
                                                    value: row.subAgenzia,
                                                    name: "SUBAG"
                                                },
                                                CODFIS: {
                                                    value: this.accountData.ExternalId__c,
                                                    name: "CODFIS"
                                                },
                                                NPOL: {
                                                    value: row.polizza,
                                                    name: "NPOL"
                                                },
                                                AGEN: {
                                                    value: row.agenzia,
                                                    name: "AGEN"
                                                },
                                                COMP: {
                                                    value: compagniaCode,
                                                    name: "COMP"
                                                },
                                                MSGID: {
                                                    value: uuid
                                                },
                                                BTCID: {
                                                    value: "15596523", //-> da valorizzare con idQueue ritornato dal servizio recuperaPrt/motiviRistampa, se non già presente nella response originaria
                                                    name: "BTCID"
                                                },
                                                DOCID: {
                                                    "value": resultPrt?.idQueue, //-> da valorizzare con campo idDoc da output servizio titoli
                                                    name: "DOCID"
                                                },
                                                ITEMID: {
                                                    "value": row.docItemId,
                                                    name: "ITEMID"
                                                }
                                            },
                                            descriptors: [
                                                {
                                                    type: "7266",
                                                    descriptor: "prt0",
                                                    keys: {
                                                        COMP: {
                                                            value: compagniaCode,
                                                            name: "COMP"
                                                        },
                                                        NPOL: {
                                                            value: row.polizza,
                                                            name: "NPOL"
                                                        },
                                                        NARC: {
                                                            value: row.numeroArchivio,
                                                            name: "NARC"
                                                        },
                                                        NAPP: {
                                                            value: row.numeroAppendice,
                                                            name: "NAPP"
                                                        },
                                                        TIPOP: {
                                                            value: row.tipoOp,
                                                            name: "TIPOP"
                                                        },
                                                        EFFVAR: {
                                                            value: row.dtEffVar,
                                                            name: "EFFVAR"
                                                        },
                                                        EFFTIT: {
                                                            value: row.dataEffettoTitolo,
                                                            name: "EFFTIT"
                                                        }
                                                    }
                                                }
                                            ],
                                            signTyp: "A",
                                        }
                                    },
                                    requestId: "SFEA.PRINT.PAPER-1747294655933"
                                },
                                feiAddress: {
                                    hostpoint: 'https://essig-inte.unipolsai.it',
                                    entrypoint: "",
                                    endpoint: "/WorkspaceWeb/app/crm/scheda_cliente",
                                    identity: "SFCRM"
                                }
                            }),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || "",
                            society: this.societyNameToIdMap.get(row.compagnia) || ''
                        };
                        console.log("params: "+JSON.stringify(this.params));
                        this.toggleLWCModal();
                    });
                }).catch(error => {
                    console.error('Error retrieving PRT:', JSON.stringify(error));
                })

                break;
            }

            case 'fea': {
                let feaParams = this.getDefaultFEAParams(row);
                feaParams.prtForPrint = false;
                console.log('FEAParams: ' + JSON.stringify(feaParams));
                apexGetPrt({inputParams: feaParams})
                .then(result => {
                    console.log('PRT retrieved successfully:', JSON.stringify(result));
                    this.executeFeaFei(result, row);
                }).catch(error => {
                        console.error('Error retrieving PRT:', JSON.stringify(error));
                })
                break;
            }

            case 'incasso': {
                let feiId = 'CP.INCASSO';
                console.log('dataEffettoTitolo: ' + row.dataEffettoTitolo);
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({operazione: 'I', w_Codage: row.agenzia, w_Compag: compagniaCode, w_Data: row.dataEffettoTitolo, w_Numpol: row.polizza, w_Ramopo: row.ramo}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                })

                break;
            }
            case 'incassoMultiplo': {
                let feiId = 'CP.INCASSO.MULTIPLO';
                console.log('dataEffettoTitolo: ' + row.dataEffettoTitolo);
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({operazione: 'I', w_Codage: row.agenzia, w_Compag: compagniaCode, w_Data: row.dataEffettoTitolo, w_Codcl: this.accountData.ExternalId__c }),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                })
                break;
            }
            case 'sostituzione': {
                let feiId = row.ambito === 'Auto' ? 'NPAC.SOSTITUZIONE.POLIZZA' : 'RE.SOSTITUZIONE.POLIZZA';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({
                            agenzia: row.agenzia, 
                            agenziaSostituita: row.agenzia,
                            codiceFiscale: this.accountData.ExternalId__c,
                            compagnia: compagniaCode, //da capire dove prendere nuovo valore compagnia per la sostituzione
                            compagniaSostituita: compagniaCode,
                            polizzaSostituita: row.polizza, // verifica se soltanto primo numero polizza
                            ramoSostituita: row.ramo,
                            subAgenzia: row.subAgenzia,
                            tipoOperazione: "SO",
                            prodotto: "", // da capire dove prendere valore prodotto
                            
                        }),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                })
                break;
            }
            case 'variazione': {
                let feiId = row.ambito === 'Auto' ? 'RA.VARIAZIONE.POLIZZA' : 'RE.VARIAZIONE.POLIZZA';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({
                            agenzia: row.agenzia, 
                            agenziaSostituita: row.agenzia,
                            codiceFiscale: this.accountData.ExternalId__c,
                            compagnia: compagniaCode,
                            compagniaSostituita: compagniaCode,
                            polizzaSostituita: row.polizza,
                            ramoSostituita: row.ramo,
                            subAgenzia: row.subAgenzia,
                            tipoOperazione: "VA",
                            prodotto: "", // da capire dove prendere valore prodotto
                        }),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                })
                break;
            }
            case 'vediDocumento': {
                let feiId = 'SFEA.PRINT.DRAFT';
                let vediDocParams = this.getDefaultFEAParams(row);
                console.log('row selected: ' +JSON.stringify(row));
                vediDocParams.prtForPrint = true;
                let uuid = self.crypto.randomUUID();
                let tipoDocumento = row.tipo != undefined && row.tipo == 'Quietanza' ? "Q" : row.tipo != undefined && row.tipo == "Regolazione Premio" ? "QTREG" : "";
                vediDocParams.body = JSON.stringify({
                    ristampa: true,
                    fromFe: true,
                    motRistampaTpCd: "-1",
                    testoMotRistampa: "Stampa precedente rovinata",
                    flagAnteprima: false,
                    flagGeneraDocumenti: false,
                    listaDocumentiDaStampare: [
                        {
                            tipoDocumento: tipoDocumento,
                            idDocumento: row?.idDoc
                        }
                    ],
                    docTpCd: row?.tipoDocumento,
                    guid: uuid,
                    agenziaFiglia: row?.agenzia.toString(),
                    agenziaMadre: row?.agenzia.toString(),
                    compagnia: compagniaCode
                });
                vediDocParams.vediDocumento = true;
                apexGetPrt({inputParams: vediDocParams})
                .then(result => {
                    console.log('result: ' + JSON.stringify(result));
                    
                    apexGetParamsForFei({feiId: feiId})
                    .then(result => {
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({
                                feasRequest: {
                                    mnuAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page',
                                    srcAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page',
                                    srcAppFailBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page', //rotta da definire
                                    cmp: compagniaCode,
                                    prj: 'LEON', //da confermare
                                    app: 'SFCRM', //statico
                                    finalPrint: false, //da confermare
                                    saveStatus: false, //da confermare
                                    sendMail: false, //da confermare
                                    sendSms: false, //da confermare
                                    watermarkTyp: 'QUIET', //da confermare
                                    keys: {
                                        NARC: {
                                            value: row.numeroArchivio, //da confermare
                                            name: "NARC"
                                        },
                                        SUBAG: {
                                            value: row.subAgenzia, //da confermare
                                            name: "SUBAG"
                                        },
                                        CODFIS: {
                                            value: this.accountData?.ExternalId__c,
                                            name: "CODFIS"
                                        },
                                        RAM: {
                                            value: row.ramo,
                                            name: "RAM"
                                        },
                                        NPOL: {
                                            value: row.polizza,
                                            name: "NPOL"
                                        },
                                        NTRG: {
                                            value: row.targa,
                                            name: "NTRG"
                                        },
                                        AGEN: {
                                            value: row.agenzia,
                                            name: "AGEN"
                                        },
                                        COMP: {
                                            value: compagniaCode,
                                            name: "COMP"
                                        },
                                        MSGID: {
                                            value: uuid, //UUID generato per recuperaPrt
                                            name: "MSGID"
                                        },
                                        BTCID: {
                                            value: result?.idQueue, //da mappare con idQueue risposta del servizio recuperaPrt
                                            name: "STCID"
                                        },
                                        DOCID: {
                                            value: row.idDoc,
                                            name: "DOCID"
                                        },
                                        ITEMID: {
                                            value: row.docItemId,
                                            name: "ITEMID"
                                        },
                                    }
                                }
                            }),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || "",
                            society: this.societyNameToIdMap.get(row.compagnia) || ''
                        }
                        console.log("params: "+JSON.stringify(this.params));
                        this.toggleLWCModal();
                    }).catch(error => {
                        console.log(JSON.stringify(error));
                    })
                }).catch(error => {
                    console.log(JSON.stringify(error));
                })
                break;
            }
            case 'riprendi': {
                let feiId = 'SFEA.VIEW.DASHBOARD';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({
                            feasRequest: {
                                mnuAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/scheda_cliente',
                                srcAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page',
                                srcAppFailBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page', //rotta da definire
                                keys: {
                                    AGEN: {
                                        value: row.agenzia,
                                        name: "AGEN"
                                    },
                                    COMP: {
                                        value: compagniaCode,
                                        name: "COMP"
                                    },
                                    RAM: {
                                        value: row.ramo,
                                        name: "RAM"
                                    },
                                    NPOL: {
                                        value: row.polizza,
                                        name: "NPOL"
                                    },
                                    TIPOP: {
                                        value: row.tipoOp,
                                        name: "TIPOP"
                                    }
                                }
                            }
                        }),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                })
                break;
            }

            case 'invioDaRemoto': {

                //apertura modale
                
                this.isInvioRemotoModalOpened = true;
                this.rowSelected = row;
                break;
            }

            default:
                console.warn('Azione non riconosciuta:', actionName);
                this.params = null;
        }
    }

    executeFeaFei(params, row){
        let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
        if (params.FEI == 'requestSign'){
            let feiId = 'SFEA.SIGN';
            apexGetParamsForFei({feiId: feiId})
            .then(result => {
                console.log('result getParamsForFei: ' + JSON.stringify(result));
                this.params = {
                    feiId: feiId,
                    feiRequestPayload: JSON.stringify({
                        feiRequest: {
                            requestCode: "SFEA.SIGN",
                            feiRequest: {
                                feasRequest: {
                                    mnuAppBckUrl: "https://essig-inte.unipolsai.it/WorkspaceWeb/app//courtesy-page",
                                    srcAppBckUrl: "https://essig-inte.unipolsai.it/WorkspaceWeb/app//courtesy-page",
                                    srcAppFailBckUrl: "https://essig-inte.unipolsai.it/WorkspaceWeb/app/courtesy-page", //rotta da definire
                                    cmp: compagniaCode,
                                    prj: "LEON",
                                    app: "SFCRM",
                                    signTyp: "M",
                                    contactTyp: "F",
                                    channelTyp: "AGZ",
                                    saveStatus: true,
                                    finalPrint: false,
                                    sendMail: true,
                                    sendSms: true,
                                    sendCash: true,
                                    keys: {
                                        NUMTIT: {
                                            value: row?.idTitolo,
                                            name: "NUMTIT"
                                        },
                                        ICTR: {
                                            value: row?.ITCTValue,
                                            name: "ICTR"
                                        },
                                        NFLD: {
                                            value: row?.nFolder,
                                            name: "NFLD"
                                        },
                                        NAPP: {
                                            value: row?.numeroAppendice,
                                            name: "NAPP"
                                        },
                                        TIPOP: {
                                            value: row?.tipoOp,
                                            name: "TIPOP"
                                        },
                                        CIUID: {
                                            value: compagniaCode === 1 ? this.accountDetailsData.Unipol.SourceSystemIdentifier__c : compagniaCode === 4 ? this.accountDetailsData.UniSalute.SourceSystemIdentifier__c : '',
                                            name: "CIUID"
                                        },
                                        INIVAL: {
                                            value: row?.dataEffettoPolizza,
                                            name: "INIVAL"
                                        },
                                        EFFTIT: {
                                            value: row?.dataEffettoTitolo,
                                            name: "EFFTIT"
                                        },
                                        EFFVAR: {
                                            value: row?.dtEffVar,
                                            name: "EFFVAR"
                                        },
                                        NVER: {
                                            value: "1",
                                            name: "NVER"
                                        },
                                        DATRIF: {
                                            value: new Date().toISOString(),
                                            name: "DATRIF"
                                        },
                                        FAS: {
                                            value: "VEN",
                                            name: "FAS"
                                        },
                                        JRN: {
                                            value: "AGE",
                                            name: "JRN"
                                        },
                                        INOUT: {
                                            value: "OUTBOUND",
                                            name: "INOUT"
                                        },
                                        TIPFIRM: {
                                            value: "FEA",
                                            name: "TIPFIRM"
                                        },
                                        PRSFIRM: {
                                            value: "SI",
                                            name: "PRSFIRM"
                                        },
                                        FRMDOC: {
                                            value: "DIG",
                                            name: "FRMDOC"
                                        },
                                        UMOD: {
                                            value: "Online",
                                            name: "UMOD"
                                        },
                                        USYS: {
                                            value: "DO",
                                            name: "USYS"
                                        },
                                        STAT: {
                                            value: "VLD",
                                            name: "STAT"
                                        },
                                        SMSSNDKEY: {
                                            "value": "12c389e3-b692-46cc-bf35-c4776be4c702", //-> da verificare e/o sostituire con chiave corretta per SF
                                            "name": "SMSSNDKEY"
                                        },
                                        MAILSNDKEY: {
                                            value: "4867dbb1-d49c-405f-ba97-326800f06901", //-> da verificare e/o sostituire con chiave corretta per SF
                                            name: "MAILSNDKEY"
                                        },
                                        NARCFLD: {
                                            value: row?.numeroArchivioProdottoUnico,
                                            name: "NARCFLD"
                                        },
                                        NPOLFLD: {
                                            value: row?.NPOLFLDValue,
                                            name: "NPOLFLD"
                                        },
                                        NARC: {
                                            value: row?.numeroArchivio,
                                            name: "NARC"
                                        },
                                        SUBAG: {
                                            value: row?.subagenzia,
                                            name: "SUBAG"
                                        },
                                        CODFIS: {
                                            value: this.accountData.ExternalId__c,
                                            name: "CODFIS"
                                        },
                                        RAM: {
                                            value: row?.ramo,
                                            name: "RAM"
                                        },
                                        NPOL: {
                                            value: row?.polizza,
                                            name: "NPOL"
                                        },
                                        AGEN: {
                                            value: row?.agenzia,
                                            name: "AGEN"
                                        },
                                        COMP: {
                                            value: compagniaCode,
                                            name: "COMP"
                                        }
                                    },
                                    descriptors: [
                                        {
                                            type: "POL",
                                            descriptor: "prt0",
                                            keys: {
                                                NFLD: {
                                                    value: row?.nFolder,
                                                    name: "NFLD"
                                                },
                                                COMP: {
                                                    value: compagniaCode,
                                                    name: "COMP"
                                                },
                                                NPOL: {
                                                    value: row?.polizza,
                                                    name: "NPOL"
                                                },
                                                NARC: {
                                                    value: row?.numeroArchivio,
                                                    name: "NARC"
                                                },
                                                NAPP: {
                                                    value: row?.numeroAppendice,
                                                    name: "NAPP"
                                                },
                                                TIPOP: {
                                                    value: row?.tipoOp,
                                                    name: "TIPOP"
                                                },
                                                EFFVAR: {
                                                    value: row?.dtEffVar,
                                                    name: "EFFVAR"
                                                },
                                                EFFTIT: {
                                                    value: row?.dataEffettoTitolo,
                                                    name: "EFFTIT"
                                                }
                                            }
                                        }
                                    ]
                                }
                            },
                            requestId: "SFEA.SIGN-1747298088710"
                            },
                            feiAddress: {
                                hostpoint: "https://essig-inte.unipolsai.it",
                                entrypoint: "",
                                endpoint: "/WorkspaceWeb/app/crm/scheda_cliente",
                                identity: "WKCS"
                            }
                        }
                    ), //TODO: metti json completo
                    fiscalcode: result["fiscalCode"] || "CF",
                    permissionSetName: result["permissionSetName"] || "",
                    society: this.societyNameToIdMap.get(row.compagnia) || ''
                };
                console.log("params: "+JSON.stringify(this.params));
                this.toggleLWCModal();
            }).catch(error => {
                console.log('Error retrieving parameters for FEA:' + JSON.stringify(error));
                }
            );
            return;
        }
        if (params.FEI == 'resumeSign'){
            let feiId = 'SFEA.SIGN.DASHBOARD';
            apexGetParamsForFei({feiId: feiId})
            .then(result => {
                let feiRequestPayload;
                if (row.isProdottoUnico === true) {
                    feiRequestPayload = JSON.stringify(
                        {
                            feiRequest: {
                                requestCode: "SFEA.SIGN.DASHBOARD",
                                feiRequest: {
                                    feasRequest: {
                                        mnuAppBckUrl: "https://essig-inte.unipolsai.it/WorkspaceWeb/app/crm/scheda_cliente",
                                        srcAppBckUrl: "https://essig-inte.unipolsai.it/WorkspaceWeb/app/courtesy-page",
                                        srcAppFailBckUrl: "https://essig-inte.unipolsai.it/WorkspaceWeb/app/courtesy-page", //rotta da definire
                                        keys: {
                                            AGEN: {
                                                value: row?.agenzia,
                                                name: "AGEN"
                                            },
                                            COMP: {
                                                value: compagniaCode,
                                                name: "COMP"
                                            },
                                            NPOL: {
                                                value: row?.polizza,
                                                name: "NPOL"
                                            },
                                            TIPOP: {
                                                value: row?.tipoOp,
                                                name: "TIPOP"
                                            },
                                            NAPP: {
                                                value: row?.numeroAppendice,
                                                name: "NAPP"
                                            },
                                            EFFTIT: {
                                                value: row?.dataEffettoTitolo,
                                                name: "EFFTIT"
                                            },
                                            MODFIRM: {
                                                value: "M",
                                                name: "MODFIRM"
                                            },
                                            TIPRIC: {
                                                value: "10",
                                                name: "TIPRIC"
                                            },
                                            RETFEISRC: {
                                                value: "true",
                                                name: "RETFEISRC"
                                            }
                                        },
                                    },
                                    "requestId": "SFEA.SIGN.DASHBOARD-1747297517069"
                                },
                                feiAddress: {
                                    hostpoint: "https://essig-inte.unipolsai.it",
                                    entrypoint: "",
                                    endpoint: "/WorkspaceWeb/app/crm/scheda_cliente",
                                    identity: "WKCS"
                                }   
                            }
                        }
                    )
                } else {
                    feiRequestPayload = JSON.stringify(
                        {
                            feiRequest: {
                                requestCode: "SFEA.VIEW.DASHBOARD",
                                feiRequest: {
                                    feasRequest: {
                                        mnuAppBckUrl: "https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/scheda_cliente",
                                        srcAppBckUrl: "https://essig-form.unipolsai.it/WorkspaceWeb/app/courtesy-page",
                                        srcAppFailBckUrl: "https://essig-form.unipolsai.it/WorkspaceWeb/app/courtesy-page", // rotta da definire
                                        keys: {
                                            AGEN: {
                                                value: row?.agenzia,
                                                name: "AGEN"
                                            },
                                            COMP: {
                                                value: compagniaCode,
                                                name: "COMP"
                                            },
                                            RAM: {
                                                value: row?.ramo,
                                                "name": "RAM"
                                            },
                                            NPOL: {
                                                value: row?.polizza,
                                                name: "NPOL"
                                            },
                                            TIPOP: {
                                                value: row?.tipoOp,
                                                name: "TIPOP"
                                            }
                                        },
                                        
                                    }
                                },
                                "requestId": "SFEA.VIEW.DASHBOARD-1752650554358"
                            },
                            feiAddress: {
                                hostpoint: "https://essig-form.unipolsai.it",
                                entrypoint: "",
                                endpoint: "/WorkspaceWeb/app/crm/scheda_cliente", //-> indirizzo pagina SF
                                identity: "WKCS"
                            }
                        }

                    )
                }
                this.params = {
                    feiId: feiId,
                    feiRequestPayload: JSON.stringify(feiRequestPayload), //TODO: metti json completo
                    fiscalcode: result["fiscalCode"] || "CF",
                    permissionSetName: result["permissionSetName"] || "",
                    society: this.societyNameToIdMap.get(row.compagnia) || ''
                };
                console.log("params: "+JSON.stringify(this.params));
                this.toggleLWCModal();
            }).catch(error => {
                console.log('Error retrieving parameters for FEA:' + JSON.stringify(error));
                }
            );
        }
    }

    getDefaultFEAParams(row) {
        return {
            idDoc: row.idDoc,
            ramo: row.ramo,
            polizza: row.polizza,
            polizzaProdottoUnico: row.isProdottoUnico,
            numeroAppendice: row.numeroAppendice,
            dataEffettoTitolo: row.dataEffettoTitolo,
            prtForPrint: false,
            vediDocumento: false
        };

    }

    handleMotivoRistampa(event) {
        let motivoRistampa = event.target.value;
        console.log('motivoRistampa: ' + motivoRistampa);
        if(motivoRistampa !== ""){
            console.log('true');
            this.selectedMotivo = motivoRistampa;
            this.isConfermaDisabled = false;
        } else {
            console.log('false');
            this.selectedMotivo = null;
            this.isConfermaDisabled = true;
        }
        console.log('postalizzaVisible: ' + this.postalizzaVisible);
        console.log('selectedMotivo:  ' + this.selectedMotivo);
        console.log('emailType: ' + this.emailType);
        console.log('isConfermaDisabled: ' + this.isConfermaDisabled);
    }

    handleAnnulla() {
        this.isInvioRemotoModalOpened = false;
        this.selectedMotivo = null;
        this.rowSelected = {};
    }

    handleConferma(event) {
        console.log('handleConferma');
        const modalitaInvio = event.dataset.key === 'posta' ? 1003 : this.emailType === 'dispositiva' ? 1001 : this.emailType === 'agenzia' ? 1002 : '';
        const testoMotRistampa = this.motivoRistampaMap[this.selectedMotivo];
        apexInvioRemoto(
            {
                body: JSON.stringify({
                    cf: this.accountData.ExternalId__c,
                    contId: this.rowSelected.contId,
                    quietanzaId: this.rowSelected.xquietanzaId,
                    modalitaInvio: modalitaInvio, // 1001 = email dispositiva, 1002 = email anag agenzia, 1003 = postalizza
                    testoMotRistampa: testoMotRistampa,
                    motRistampaTpCd: this.selectedMotivo
                })
            }
        ).then(result => {
            console.log('result: ' + JSON.stringify(result));
            this.isInvioRemotoModalOpened = false;
        })
        .catch(error => {
            console.log('error in invioRemoto: ' + JSON.stringify(error));
        })
    }

    handleEmailType(event){
        this.emailType = event.target.value;
    }
}