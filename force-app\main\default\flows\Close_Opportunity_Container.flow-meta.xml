<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <assignments>
        <name>Add_prod_to_list</name>
        <label>Add prod to list</label>
        <locationX>2096</locationX>
        <locationY>2438</locationY>
        <assignmentItems>
            <assignToReference>Product_Loop_Container.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>oppsToUpdateList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Product_Loop_Container</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Empty_product_variables_and_Add_draft_product_to_list</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Amount_to_0</name>
        <label>Amount to 0</label>
        <locationX>468</locationX>
        <locationY>1838</locationY>
        <assignmentItems>
            <assignToReference>OpportunityAmount</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Copy_1_of_Update_Sottoesito_Di_Chiusura_and_Esito_Chiusura</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Areas_of_Need2</name>
        <label>Areas of Need</label>
        <locationX>424</locationX>
        <locationY>2462</locationY>
        <assignmentItems>
            <assignToReference>AreasOfNeed</assignToReference>
            <operator>AddItem</operator>
            <value>
                <elementReference>Loop_quotes.AreasOfNeed__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_quotes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Amounts_and_Area_of_Need</name>
        <label>Assign Amounts and Area of Need</label>
        <locationX>2448</locationX>
        <locationY>1730</locationY>
        <assignmentItems>
            <assignToReference>TotalProductAmount</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Sold_Quotes_Container.QuoteAmount__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>TotalBundleOpportunityAmount</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Sold_Quotes_Container.QuoteAmount__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>AreasOfNeed</assignToReference>
            <operator>AddItem</operator>
            <value>
                <elementReference>Loop_Sold_Quotes_Container.AreasOfNeed__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>BundleAreasOfNeed</assignToReference>
            <operator>AddItem</operator>
            <value>
                <elementReference>Loop_Sold_Quotes_Container.AreasOfNeed__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>chech_prod_status</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_default_values_for_Bundle_Opportunity_Container</name>
        <label>Assign default values for Bundle Opportunity - Container</label>
        <locationX>1788</locationX>
        <locationY>890</locationY>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.Substatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerStatus</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.ClosureSubstatus__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.LossReason__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ChosenSubstatus</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <connector>
            <targetReference>Product_Loop_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_default_values_for_Product_Container</name>
        <label>Assign default values for Product - Container</label>
        <locationX>2096</locationX>
        <locationY>1214</locationY>
        <assignmentItems>
            <assignToReference>Product_Loop_Container.Substatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerStatus</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Product_Loop_Container.ClosureSubstatus__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Product_Loop_Container.LossReason__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ChosenSubstatus</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Product_Loop_Container.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <connector>
            <targetReference>Get_Quotes_from_Product_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Empty_Container</name>
        <label>Assign Empty Container</label>
        <locationX>732</locationX>
        <locationY>998</locationY>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.Amount</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.ClosingDate__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.ClosureSubstatus__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.LossReason__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.StageName</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.Substatus__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>oppsToUpdateList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_Target_Opportunity</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Close_Opps_From_List</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_new_amount_and_area_of_need_to_bundle_opportunity</name>
        <label>Assign new amount and area of need to bundle opportunity</label>
        <locationX>1788</locationX>
        <locationY>2822</locationY>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.Amount</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>TotalBundleOpportunityAmount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>BundleAreasOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>oppsToUpdateList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_Target_Opportunity</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Close_Opps_From_List</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Not_Interested_Substatus</name>
        <label>Assign Not Interested Substatus</label>
        <locationX>204</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>closureSubstatus</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>productSubstatus</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Product_Closing_Variables</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Opp_Parent_To_Update_List</name>
        <label>Assign Opp Parent To Update List</label>
        <locationX>182</locationX>
        <locationY>3686</locationY>
        <assignmentItems>
            <assignToReference>oppsToUpdateList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>parentOppToUpdate</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Close_Opps_From_List</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Opp_Product_To_Update_List</name>
        <label>Assign Opp Product To Update List</label>
        <locationX>336</locationX>
        <locationY>3062</locationY>
        <assignmentItems>
            <assignToReference>oppsToUpdateList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>productToUpdate</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Product_Count2</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Parent_Opp_Variables</name>
        <label>Assign Parent Opp Variables</label>
        <locationX>182</locationX>
        <locationY>3278</locationY>
        <assignmentItems>
            <assignToReference>parentOppToUpdate.Amount</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>OpportunityAmount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>parentOppToUpdate.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>AreasOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>parentOppToUpdate.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>parentOppToUpdate.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Target_Opportunity.Parent__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>At_least_1_sold_quote</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Product_Closing_Variables</name>
        <label>Assign Product Closing Variables</label>
        <locationX>336</locationX>
        <locationY>974</locationY>
        <assignmentItems>
            <assignToReference>closureStatus</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>productStatus</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>closureNotes</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>productNotes</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Other_Open_Products</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Product_Count</name>
        <label>Assign Product Count</label>
        <locationX>336</locationX>
        <locationY>1190</locationY>
        <assignmentItems>
            <assignToReference>openProductCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Other_Open_Products</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_all_quotes_from_opp_Prod</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Unreachable_Substatus</name>
        <label>Assign Unreachable Substatus</label>
        <locationX>468</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>closureSubstatus</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>UnreachableProductSubstatus</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Product_Closing_Variables</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_Update_Sottoesito_Di_Chiusura_and_Esito_Chiusura</name>
        <label>Copy 1 Update Sottoesito Di Chiusura and Esito Chiusura</label>
        <locationX>468</locationX>
        <locationY>1946</locationY>
        <assignmentItems>
            <assignToReference>productToUpdate.ClosureSubstatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>notInterestedChoice</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productToUpdate.LossReason__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>closureSubstatus</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_quotes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Empty_product_variables_and_Add_draft_product_to_list</name>
        <label>Empty product variables and Add draft product to list</label>
        <locationX>2228</locationX>
        <locationY>2630</locationY>
        <assignmentItems>
            <assignToReference>TotalProductAmount</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>AreasOfNeed</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <connector>
            <targetReference>Product_Loop_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Number_of_quotes</name>
        <label>Number of quotes</label>
        <locationX>336</locationX>
        <locationY>1406</locationY>
        <assignmentItems>
            <assignToReference>NumberOfQuotes</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_all_quotes_from_opp_Prod</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Filter_Only_Sold_Quotes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Number_Of_Sold_Quotes</name>
        <label>Number Of Sold Quotes</label>
        <locationX>336</locationX>
        <locationY>1622</locationY>
        <assignmentItems>
            <assignToReference>NumberOfSoldQuotes</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Filter_Only_Sold_Quotes</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>At_least_1_sold</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>OneSoldQuoteToTrue</name>
        <label>OneSoldQuoteToTrue</label>
        <locationX>204</locationX>
        <locationY>1838</locationY>
        <assignmentItems>
            <assignToReference>ThereAreSoldQuotes</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Sottoesito_Di_Chiusura_and_Esito_Chiusura</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>overwrite_Container_fields</name>
        <label>overwrite Container fields</label>
        <locationX>2448</locationX>
        <locationY>2138</locationY>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.Substatus__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.ClosureSubstatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Polizza emessa</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Target_Opportunity.LossReason__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Sold_Quotes_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Guy De Roquefeuil: poca efficienza nel riassegnare N volte la stessa variabile, ma si assume che i preventivi siano pochi ed è per mantenere il flow più leggibile</description>
        <name>Overwrite_Product_status_fields</name>
        <label>Overwrite Product status fields</label>
        <locationX>2316</locationX>
        <locationY>1946</locationY>
        <assignmentItems>
            <assignToReference>Product_Loop_Container.Substatus__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Product_Loop_Container.ClosureSubstatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Polizza emessa</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Product_Loop_Container.LossReason__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Product_Loop_Container.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>AreasOfNeed</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>overwrite_Container_fields</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Sum_amount_on_quotes2</name>
        <label>Sum amount on quotes</label>
        <locationX>424</locationX>
        <locationY>2354</locationY>
        <assignmentItems>
            <assignToReference>OpportunityAmount</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_quotes.QuoteAmount__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Areas_of_Need2</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Opp_Product</name>
        <label>Update Opp Product</label>
        <locationX>336</locationX>
        <locationY>2738</locationY>
        <assignmentItems>
            <assignToReference>productToUpdate.ClosingDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productToUpdate.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productToUpdate.Substatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>closureStatus</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productToUpdate.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Contact_History_Product</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Parent_Opp_Variables</name>
        <label>Update Parent Opp Variables</label>
        <locationX>50</locationX>
        <locationY>3494</locationY>
        <assignmentItems>
            <assignToReference>parentOppToUpdate.ClosureSubstatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Polizza emessa</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Opp_Parent_To_Update_List</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Sottoesito_Di_Chiusura_and_Esito_Chiusura</name>
        <label>Update Sottoesito Di Chiusura and Esito Chiusura</label>
        <locationX>204</locationX>
        <locationY>1946</locationY>
        <assignmentItems>
            <assignToReference>productToUpdate.ClosureSubstatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Polizza emessa</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productToUpdate.LossReason__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue></stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_quotes</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>boughtFromOtherCompanyChoice</name>
        <choiceText>Acquisto da altra compagnia</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Acquisto da altra compagnia</stringValue>
        </value>
    </choices>
    <choices>
        <name>doesNotAnswerChoice</name>
        <choiceText>Non risponde</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Non risponde</stringValue>
        </value>
    </choices>
    <choices>
        <name>doesNotExistChoice</name>
        <choiceText>Numero inesistente</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Numero inesistente</stringValue>
        </value>
    </choices>
    <choices>
        <name>notAGoodFitChoice</name>
        <choiceText>Non adatto alle esigenze</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Non adatto alle esigenze</stringValue>
        </value>
    </choices>
    <choices>
        <name>notInterestedChoice</name>
        <choiceText>Non interessato</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Non interessato</stringValue>
        </value>
    </choices>
    <choices>
        <name>otherChoice</name>
        <choiceText>Altro</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Altro</stringValue>
        </value>
    </choices>
    <choices>
        <name>priceTooHighChoice</name>
        <choiceText>Prezzo troppo alto</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Prezzo troppo alto</stringValue>
        </value>
    </choices>
    <choices>
        <name>unreachableChoice</name>
        <choiceText>Non contattabile</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Non contattabile</stringValue>
        </value>
    </choices>
    <collectionProcessors>
        <name>Filter_Only_Sold_Quotes</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter Only Sold Quotes</label>
        <locationX>336</locationX>
        <locationY>1514</locationY>
        <assignNextValueToReference>currentItem_Filter_Only_Sold_Quotes</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_all_quotes_from_opp_Prod</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_Only_Sold_Quotes.CommercialStatus__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>SOLD</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Number_Of_Sold_Quotes</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <name>Filter_Sold_Quotes_Container</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter Sold Quotes - Container</label>
        <locationX>2228</locationX>
        <locationY>1514</locationY>
        <assignNextValueToReference>currentItem_Filter_Sold_Quotes_Container</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_Quotes_from_Product_Container</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_Sold_Quotes_Container.CommercialStatus__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>SOLD</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Loop_Sold_Quotes_Container</targetReference>
        </connector>
    </collectionProcessors>
    <decisions>
        <name>At_least_1_sold</name>
        <label>At least 1 sold</label>
        <locationX>336</locationX>
        <locationY>1730</locationY>
        <defaultConnector>
            <targetReference>Amount_to_0</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>X1_Sold</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>NumberOfSoldQuotes</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>OneSoldQuoteToTrue</targetReference>
            </connector>
            <label>1 Sold</label>
        </rules>
    </decisions>
    <decisions>
        <name>At_least_1_sold_quote</name>
        <label>At least 1 sold quote</label>
        <locationX>182</locationX>
        <locationY>3386</locationY>
        <defaultConnector>
            <targetReference>Assign_Opp_Parent_To_Update_List</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Sold_quote_at_least</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ThereAreSoldQuotes</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Parent_Opp_Variables</targetReference>
            </connector>
            <label>Sold quote at least</label>
        </rules>
    </decisions>
    <decisions>
        <name>chech_prod_status</name>
        <label>chech prod status</label>
        <locationX>2448</locationX>
        <locationY>1838</locationY>
        <defaultConnector>
            <targetReference>overwrite_Container_fields</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>valid_prod</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Product_Loop_Container.StageName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Chiuso</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Overwrite_Product_status_fields</targetReference>
            </connector>
            <label>valid prod</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_add_prod_to_list</name>
        <label>check add prod to list</label>
        <locationX>2228</locationX>
        <locationY>2330</locationY>
        <defaultConnector>
            <targetReference>Empty_product_variables_and_Add_draft_product_to_list</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>valid_prod_to_add</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Product_Loop_Container.StageName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Chiuso</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_prod_to_list</targetReference>
            </connector>
            <label>valid prod to add</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Product</name>
        <label>Check if Product</label>
        <locationX>798</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>CloseContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is Container</defaultConnectorLabel>
        <rules>
            <name>Is_Product</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isProduct</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CloseProductScreen</targetReference>
            </connector>
            <label>Is Product</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Sold</name>
        <label>Check if Sold</label>
        <locationX>556</locationX>
        <locationY>2246</locationY>
        <defaultConnector>
            <targetReference>Loop_quotes</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Sold</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_quotes.CommercialStatus__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>SOLD</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_quotes.IsStored__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Sum_amount_on_quotes2</targetReference>
            </connector>
            <label>Sold</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Product_Closing_Reason</name>
        <label>Check Product Closing Reason</label>
        <locationX>336</locationX>
        <locationY>674</locationY>
        <defaultConnector>
            <targetReference>Assign_Unreachable_Substatus</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Unreachable</defaultConnectorLabel>
        <rules>
            <name>Not_Interested</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>productStatus</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>notInterestedChoice</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Not_Interested_Substatus</targetReference>
            </connector>
            <label>Not Interested</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Product_Count2</name>
        <label>Check Product Count</label>
        <locationX>336</locationX>
        <locationY>3170</locationY>
        <defaultConnector>
            <targetReference>Close_Opps_From_List</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>More Than 0</defaultConnectorLabel>
        <rules>
            <name>Zero</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>openProductCount</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Parent_Opp_Variables</targetReference>
            </connector>
            <label>Zero</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_product_status</name>
        <label>check product status</label>
        <locationX>2228</locationX>
        <locationY>1106</locationY>
        <defaultConnector>
            <targetReference>Get_Quotes_from_Product_Container</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Outcome_1_of_check_product_status</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Product_Loop_Container.StageName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Chiuso</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_default_values_for_Product_Container</targetReference>
            </connector>
            <label>Valid status</label>
        </rules>
    </decisions>
    <decisions>
        <name>Product_count_check</name>
        <label>Product count check</label>
        <locationX>1260</locationX>
        <locationY>782</locationY>
        <defaultConnector>
            <targetReference>Assign_default_values_for_Bundle_Opportunity_Container</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No_Products</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_All_Products_Opportunites_Container</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Contact_History_Empty_Container</targetReference>
            </connector>
            <label>No Products</label>
        </rules>
    </decisions>
    <description>fix calcolo quote vendute</description>
    <environments>Default</environments>
    <formulas>
        <name>ChosenSubstatus</name>
        <dataType>String</dataType>
        <expression>BLANKVALUE({!containerSubstatusEmpty}, BLANKVALUE({!containerSubstatusNotInterested}, {!containerSubstatusNotReachable}))</expression>
    </formulas>
    <formulas>
        <name>containerStatusWasNotChosen</name>
        <dataType>Boolean</dataType>
        <expression>ISBLANK({!containerStatus})</expression>
    </formulas>
    <formulas>
        <name>currentUserName</name>
        <dataType>String</dataType>
        <expression>&apos;Agente: &apos; &amp; {!$User.FirstName} &amp; &apos; &apos; &amp; {!$User.LastName}</expression>
    </formulas>
    <formulas>
        <name>isProduct</name>
        <dataType>Boolean</dataType>
        <expression>{!Get_Target_Record_Type.DeveloperName} = &apos;Prodotto&apos;</expression>
    </formulas>
    <formulas>
        <name>noteBodyActual</name>
        <dataType>String</dataType>
        <expression>IF(NOT(ISBLANK({!closureNotes})), {!noteBodyActual}, &apos;&apos;)</expression>
    </formulas>
    <formulas>
        <name>noteBodySubstatus</name>
        <dataType>String</dataType>
        <expression>&apos;Motivo: &apos; &amp; {!closureSubstatus}</expression>
    </formulas>
    <formulas>
        <name>productStatusWasNotChosen</name>
        <dataType>Boolean</dataType>
        <expression>ISBLANK({!productStatus})</expression>
    </formulas>
    <interviewLabel>Close Opportunity Container {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Close Opportunity Container</label>
    <loops>
        <name>Loop_quotes</name>
        <label>Loop quotes</label>
        <locationX>336</locationX>
        <locationY>2138</locationY>
        <collectionReference>Get_all_quotes_from_opp_Prod</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Check_if_Sold</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Opp_Product</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Sold_Quotes_Container</name>
        <label>Loop Sold Quotes - Container</label>
        <locationX>2228</locationX>
        <locationY>1622</locationY>
        <collectionReference>Filter_Sold_Quotes_Container</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Amounts_and_Area_of_Need</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>check_add_prod_to_list</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Product_Loop_Container</name>
        <label>Product Loop - Container</label>
        <locationX>1788</locationX>
        <locationY>998</locationY>
        <collectionReference>Get_All_Products_Opportunites_Container</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>check_product_status</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Assign_new_amount_and_area_of_need_to_bundle_opportunity</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Contact_History_Empty_Container</name>
        <label>Create Contact History Empty Container</label>
        <locationX>732</locationX>
        <locationY>890</locationY>
        <connector>
            <targetReference>Assign_Empty_Container</targetReference>
        </connector>
        <inputAssignments>
            <field>Description__c</field>
            <value>
                <elementReference>closureNotes</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <stringValue>Trattativa chiusa</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity__c</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Event_Contact_History_Type.Id</elementReference>
            </value>
        </inputAssignments>
        <object>ContactHistory__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Contact_History_Product</name>
        <label>Create Contact History Product</label>
        <locationX>336</locationX>
        <locationY>2846</locationY>
        <connector>
            <targetReference>Create_Product_Closing_Note</targetReference>
        </connector>
        <inputAssignments>
            <field>Description__c</field>
            <value>
                <elementReference>closureNotes</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Executor__c</field>
            <value>
                <elementReference>currentUserName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <stringValue>Linea di prodotto chiusa</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity__c</field>
            <value>
                <elementReference>Get_Target_Opportunity.Parent__c</elementReference>
            </value>
        </inputAssignments>
        <object>ContactHistory__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Product_Closing_Note</name>
        <label>Create Product Closing Note</label>
        <locationX>336</locationX>
        <locationY>2954</locationY>
        <connector>
            <targetReference>Assign_Opp_Product_To_Update_List</targetReference>
        </connector>
        <inputAssignments>
            <field>Body</field>
            <value>
                <elementReference>productNotes</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ParentId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Title</field>
            <value>
                <stringValue>Chiusura linea di prodotto</stringValue>
            </value>
        </inputAssignments>
        <object>Note</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_All_Products_Opportunites_Container</name>
        <label>Get All Products Opportunites - Container</label>
        <locationX>1260</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Product_count_check</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Parent__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_all_quotes_from_opp_Prod</name>
        <label>Get all quotes from opp Prod</label>
        <locationX>336</locationX>
        <locationY>1298</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Number_of_quotes</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OpportunityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Target_Opportunity.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Quote</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Event_Contact_History_Type</name>
        <label>Get Event Contact History Type</label>
        <locationX>798</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_if_Product</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ContactHistory__c</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Event</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Other_Open_Products</name>
        <label>Get Other Open Products</label>
        <locationX>336</locationX>
        <locationY>1082</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Product_Count</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Parent__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Target_Opportunity.Parent__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Guy De Roquefeui: sì, una query dentro un ciclo qundi pericolosa. si suppone ci siano POCHE linee di prodotto per trattativa (meno di 100)</description>
        <name>Get_Quotes_from_Product_Container</name>
        <label>Get Quotes from Product - Container</label>
        <locationX>2228</locationX>
        <locationY>1406</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Filter_Sold_Quotes_Container</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OpportunityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Product_Loop_Container.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Quote</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Target_Opportunity</name>
        <label>Get Target Opportunity</label>
        <locationX>798</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Target_Record_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Target_Record_Type</name>
        <label>Get Target Record Type</label>
        <locationX>798</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Event_Contact_History_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Target_Opportunity.RecordTypeId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Close_Opps_From_List</name>
        <label>Close Opps From List</label>
        <locationX>798</locationX>
        <locationY>3962</locationY>
        <inputReference>oppsToUpdateList</inputReference>
    </recordUpdates>
    <runInMode>DefaultMode</runInMode>
    <screens>
        <name>CloseContainerScreen</name>
        <label>CloseContainerScreen</label>
        <locationX>1260</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>Get_All_Products_Opportunites_Container</targetReference>
        </connector>
        <fields>
            <name>CloseContainerTitle</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;Chiudi trattativa&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AcquistoEffettuatoBody</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Sei sicuro di voler chiudere la trattativa?&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>CloseContainerScreen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>CloseContainerScreen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>CloseContainerScreen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>containerStatus</name>
                    <choiceReferences>notInterestedChoice</choiceReferences>
                    <choiceReferences>unreachableChoice</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Esito</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>containerSubstatusEmpty</name>
                    <choiceReferences>boughtFromOtherCompanyChoice</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Sottoesito</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>true</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>containerStatus</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>containerSubstatusNotInterested</name>
                    <choiceReferences>boughtFromOtherCompanyChoice</choiceReferences>
                    <choiceReferences>notAGoodFitChoice</choiceReferences>
                    <choiceReferences>priceTooHighChoice</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Sottoesito</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <elementReference>containerStatusWasNotChosen</elementReference>
                    </isDisabled>
                    <isRequired>true</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>containerStatus</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <elementReference>notInterestedChoice</elementReference>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>containerSubstatusNotReachable</name>
                    <choiceReferences>doesNotAnswerChoice</choiceReferences>
                    <choiceReferences>doesNotExistChoice</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Sottoesito</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <elementReference>containerStatusWasNotChosen</elementReference>
                    </isDisabled>
                    <isRequired>true</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>containerStatus</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <elementReference>unreachableChoice</elementReference>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>CloseContainerScreen_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>CloseContainerScreen_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>CloseContainerScreen_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>2</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>CloseContainerScreen_Section2_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>containerNotes</name>
                    <fieldText>Note</fieldText>
                    <fieldType>LargeTextArea</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <validationRule>
                        <errorMessage>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(215, 0, 0);&quot;&gt;Inserire il motivo di chiusura&lt;/span&gt;&lt;/p&gt;</errorMessage>
                        <formulaExpression>OR(NOT({!containerStatus} = &apos;Non interessato&apos;), NOT({!containerSubstatusNotInterested} = &apos;Altro&apos;), NOT(ISBLANK({!containerNotes})))</formulaExpression>
                    </validationRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>8</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>CloseContainerScreen_Section2_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>2</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>CloseProductScreen</name>
        <label>CloseProductScreen</label>
        <locationX>336</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>Check_Product_Closing_Reason</targetReference>
        </connector>
        <fields>
            <name>CloseProductTitle</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;Chiudi linea di prodotto&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Copy_1_of_AcquistoEffettuatoBody</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Sei sicuro di voler chiudere la linea di prodotto?&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>CloseProductScreen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>CloseProductScreen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>CloseProductScreen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>productStatus</name>
                    <choiceReferences>notInterestedChoice</choiceReferences>
                    <choiceReferences>unreachableChoice</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Esito</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>SubstatusProductNotChosen</name>
                    <choiceReferences>boughtFromOtherCompanyChoice</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Sottoesito</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>true</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>productStatus</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>productSubstatus</name>
                    <choiceReferences>boughtFromOtherCompanyChoice</choiceReferences>
                    <choiceReferences>notAGoodFitChoice</choiceReferences>
                    <choiceReferences>priceTooHighChoice</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Sottoesito</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <elementReference>productStatusWasNotChosen</elementReference>
                    </isDisabled>
                    <isRequired>true</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>productStatus</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <elementReference>notInterestedChoice</elementReference>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>UnreachableProductSubstatus</name>
                    <choiceReferences>doesNotAnswerChoice</choiceReferences>
                    <choiceReferences>doesNotExistChoice</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Sottoesito</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <elementReference>productStatusWasNotChosen</elementReference>
                    </isDisabled>
                    <isRequired>true</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>productStatus</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <elementReference>unreachableChoice</elementReference>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>CloseProductScreen_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>CloseProductScreen_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>CloseProductScreen_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>2</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>CloseProductScreen_Section2_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>productNotes</name>
                    <fieldText>Note</fieldText>
                    <fieldType>LargeTextArea</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <validationRule>
                        <errorMessage>&lt;p&gt;&lt;span style=&quot;color: rgb(215, 0, 0);&quot;&gt;Inserire il motivo di chiusura&lt;/span&gt;&lt;/p&gt;</errorMessage>
                        <formulaExpression>OR(NOT({!productSubstatus} = &apos;Altro&apos;), NOT(ISBLANK({!productNotes})))</formulaExpression>
                    </validationRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>8</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>CloseProductScreen_Section2_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>2</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>672</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Target_Opportunity</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>noteBody</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>{!noteBodySubstatus}
{!noteBodyActual}</text>
    </textTemplates>
    <variables>
        <name>AreasOfNeed</name>
        <dataType>Multipicklist</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>BundleAreasOfNeed</name>
        <dataType>Multipicklist</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>closureNotes</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>closureStatus</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>closureSubstatus</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>currentItem_Filter_Only_Sold_Quotes</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>currentItem_Filter_Sold_Quotes_Container</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>DraftProduct</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>NumberOfQuotes</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>NumberOfSoldQuotes</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>openProductCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>OpportunityAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>oppsToUpdateList</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>parentOppToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>productsToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>productToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ThereAreSoldQuotes</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>TotalBundleOpportunityAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>TotalProductAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
</Flow>
