<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_AccountDetailsInfo_Agente_c</name>
        <label>Assign AccountDetailsInfo.Agente__c</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.NomeChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetAccountDetailsInfo.Agente__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_email_agente</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Agente_contact_info</name>
        <label>Assign Agente contact info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.EmailChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>EmailAgente.value</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.TelChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>TelefonoAgente.value</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assign_invio_email_automatico</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_agente_name</name>
        <label>Assign agente name</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.NomeChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>NomeAgenteSubAg</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_email_agente</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_AccountId</name>
        <label>Assign Case.AccountId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Veicoli.firstSelectedRow.ServiceContract__r.ParentServiceContract.AccountId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_case_fields</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_case_fields</name>
        <label>Assign case fields</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.ContractAsset__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Veicoli.firstSelectedRow.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PicklistUfficio</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.UfficioApertura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getNameSelectedGroup.Name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.UtAssegnatario__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.DriverId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Veicoli.firstSelectedRow.ServiceContract__r.Account.PersonContact.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_case_RT</name>
        <label>Assign case RT</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getCaseRecordTypeInfo.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.Origin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Screen_Veicolo_Contratto</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Cliente_contact_info</name>
        <label>Assign Cliente contact info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.NomeChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>NominativoCliente</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.EmailChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>EmailCliente.value</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.TelChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>TelefonoCliente.value</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assign_invio_email_automatico</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_FM_contact_info</name>
        <label>Assign FM contact info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getFMInfo.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.NomeChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>NominativoFM</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.EmailChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>EmailFM.value</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.TelChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>TelefonoFM.value</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assign_invio_email_automatico</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assign_invio_email_automatico</name>
        <label>assign invio email automatico</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.InvioEmailAutomatico__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Screen_campi_case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_showErrorAgente</name>
        <label>Assign showErrorAgente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>showErrorAgente</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenDatiAgente</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_showErrorAgente2</name>
        <label>Assign showErrorAgente2</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>showErrorAgente</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenDatiAgente</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ShowRequiredMsg</name>
        <label>Assign ShowRequiredMsg</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsg</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_campi_case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_showrequiredmsg_false</name>
        <label>Assign showRequiredMsg false</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsg</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Risolto_in_chiamata_false</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ShowRequiredMsgAR</name>
        <label>Assign ShowRequiredMsgAR</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsgAR</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenDatiAltroRichiedente</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ShowRequiredMsgEC</name>
        <label>Assign ShowRequiredMsgEC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsgEC</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenEntitaChiamante</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ShowRequiredMsgNote</name>
        <label>Assign ShowRequiredMsgNote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsgNote</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_campi_case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Util_contact_info</name>
        <label>Assign Util contact info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getCurrentAccountDriverInfo.PersonContactId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.NomeChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>NominativoDriver</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.EmailChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>EmailDriver.value</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.TelChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>TelefonoDriver.value</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assign_invio_email_automatico</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignStatusClosed</name>
        <label>AssignStatusClosed</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Chiuso - Risolto</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.ClosedDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.UtenteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_AccountId</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignStatusInGestione</name>
        <label>AssignStatusInGestione</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>In gestione</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_AccountId</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Clean_Note_chiusura</name>
        <label>Clean Note chiusura</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.NoteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>null</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>CheckZonaCircRequired</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Clean_showErrorMsgAgente</name>
        <label>Clean showErrorMsgAgente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>showErrorAgente</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_AccountDetailsInfo_Agente_c_Subagente_false</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>CollectContractId</name>
        <label>CollectContractId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>contractIdList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>LoopContractDriver.ServiceContract__r.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>LoopContractDriver</targetReference>
        </connector>
    </assignments>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>assegnato_a_uffici_rental</name>
        <label>è assegnato a uffici rental?</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>ScreenErrorMsgUfficio</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>true2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ufficiQueueList</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getRecordTypeDriver</targetReference>
            </connector>
            <label>true</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_AccountDetailsInfo_Agente_c_Subagente_false</name>
        <label>Check AccountDetailsInfo.Agente__c &amp;&amp; Subagente == false</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Check_Nome_agente</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetAccountDetailsInfo.Agente__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.SubAgente__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_AccountDetailsInfo_Agente_c</targetReference>
            </connector>
            <label>is not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Agente_or_cliente</name>
        <label>Check Agente or cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>ScreenDatiCliente</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Cliente</defaultConnectorLabel>
        <rules>
            <name>Agente</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Agente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ScreenDatiAgente</targetReference>
            </connector>
            <label>Agente</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Altro_Rich_Info</name>
        <label>Check Altro Rich Info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>assign_invio_email_automatico</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>some_fields_null_AR</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.NomeChiamante__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.TelChiamante__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.EmailChiamante__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ShowRequiredMsgAR</targetReference>
            </connector>
            <label>some fields null AR</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_contractAsset</name>
        <label>check contractAsset</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>ScreenErrorMsg</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>contractAsset_not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getContractAssetInfo</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getCaseRecordTypeInfo</targetReference>
            </connector>
            <label>contractAsset not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_contractIdList</name>
        <label>Check contractIdList</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_ScreenErrorMsg</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>contractIdList_not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>contractIdList</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getContractAssetInfo</targetReference>
            </connector>
            <label>contractIdList not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_current_Record</name>
        <label>Check current record</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>FM</defaultConnectorLabel>
        <rules>
            <name>Driver</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getCurrentAccountDriverInfo</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getContractDriverInfo</targetReference>
            </connector>
            <label>Driver</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_email_agente</name>
        <label>Check email agente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Assign_Agente_contact_info</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>email_agente_is_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>EmailAgente.value</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_showErrorAgente2</targetReference>
            </connector>
            <label>email agente is null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Ent_Chiamante</name>
        <label>Check Ent Chiamante</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Check_Entit_chiamante_selezionata</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>selezionata</defaultConnectorLabel>
        <rules>
            <name>non_selezionata</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ShowRequiredMsgEC</targetReference>
            </connector>
            <label>non selezionata</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Entit_chiamante_selezionata</name>
        <label>Check Entità chiamante selezionata</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>assign_invio_email_automatico</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Utilizzatore</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Utilizzatore</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ScreenDatiUtilizzatore</targetReference>
            </connector>
            <label>Utilizzatore</label>
        </rules>
        <rules>
            <name>Fleet_Manager</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fleet Manager</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getFMRecordTypeInfo</targetReference>
            </connector>
            <label>Fleet Manager</label>
        </rules>
        <rules>
            <name>Agente_Cliente</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Agente</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Cliente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getAccountSocRental</targetReference>
            </connector>
            <label>Agente/Cliente</label>
        </rules>
        <rules>
            <name>Altro_Richiedente</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Altro Richiedente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ScreenDatiAltroRichiedente</targetReference>
            </connector>
            <label>Altro Richiedente</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_flag_result</name>
        <label>Check flag result</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Screen_Ufficio</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_true</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isZonaCircrRequired</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.Zona_di_circolazione__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Screen_campi_case</targetReference>
            </connector>
            <label>is true</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Nome_agente</name>
        <label>Check Nome agente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Assign_agente_name</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>nome_agente_null</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>NomeAgenteSubAg</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_showErrorAgente</targetReference>
            </connector>
            <label>nome agente  null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Checkcampirequired</name>
        <label>Check campi required</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Assign_showrequiredmsg_false</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>esistecamponull</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.Origin</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.TipoRichiesta__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.Categoria__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.SottoCategoria__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.Subject</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.Description</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ShowRequiredMsg</targetReference>
            </connector>
            <label>esiste campo null</label>
        </rules>
        <rules>
            <name>RisoltoInChiamata_true_and_note_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.Risolto__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.NoteChiusura__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ShowRequiredMsgNote</targetReference>
            </connector>
            <label>RisoltoInChiamata true and note null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Risolto_in_Chiamata</name>
        <label>Risolto in Chiamata</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>AssignStatusInGestione</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isTrue</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.Risolto__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignStatusClosed</targetReference>
            </connector>
            <label>isTrue</label>
        </rules>
    </decisions>
    <decisions>
        <name>Risolto_in_chiamata_false</name>
        <label>Risolto in chiamata false</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>CheckZonaCircRequired</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_false</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.Risolto__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.NoteChiusura__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Clean_Note_chiusura</targetReference>
            </connector>
            <label>is false</label>
        </rules>
    </decisions>
    <description>Flusso di creazione case da/per conto di un Driver su contratto singolo</description>
    <dynamicChoiceSets>
        <name>GroupCollection</name>
        <collectionReference>ufficiQueueList</collectionReference>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <object>Group</object>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <interviewLabel>urcs_CaseCRMDriver {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_CaseCRMDriver</label>
    <loops>
        <name>LoopContractDriver</name>
        <label>LoopContractDriver</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>getContractDriverInfo</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>CollectContractId</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Check_contractIdList</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Case</name>
        <label>Create Case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <faultConnector>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <inputReference>caseRecord</inputReference>
    </recordCreates>
    <recordLookups>
        <name>getAccountAccountRel</name>
        <label>getAccountAccountRel</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>FinServ__Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Veicoli.firstSelectedRow.ServiceContract__r.ParentServiceContract.AccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>FinServ__RelatedAccount__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getAccountSocRental.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>FinServ__AccountAccountRelation__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetAccountDetailsInfo</name>
        <label>GetAccountDetailsInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Agente_or_cliente</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Relation__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getAccountAccountRel.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>AccountDetails__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getAccountSocRental</name>
        <label>getAccountSocRental</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getAccountAccountRel</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ExternalId__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Setup.urcs_GeneralSettings__c.GroupSocRentalName__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getCaseRecordTypeInfo</name>
        <label>getCaseRecordTypeInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_case_RT</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_CaseCRM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getContractAssetInfo</name>
        <label>getContractAssetInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>check_contractAsset</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ServiceContract__c</field>
            <operator>In</operator>
            <value>
                <elementReference>contractIdList</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ContractAsset__c</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Asset__c</queriedFields>
        <queriedFields>ServiceContract__c</queriedFields>
        <queriedFields>NomeAsset__c</queriedFields>
        <queriedFields>NomeContratto__c</queriedFields>
        <queriedFields>StatoContratto__c</queriedFields>
        <queriedFields>ClienteContratto__c</queriedFields>
        <queriedFields>tsDa__c</queriedFields>
        <queriedFields>tsA__c</queriedFields>
        <sortField>tsDa__c</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getContractDriverInfo</name>
        <label>getContractDriverInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>LoopContractDriver</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ContractDriver__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getCurrentAccountDriverInfo</name>
        <label>getCurrentAccountDriverInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_current_Record</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsPersonAccount</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getRecordTypeDriver.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getDriverPrinc</name>
        <label>getDriverPrinc</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ScreenEntitaChiamante</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Veicoli.firstSelectedRow.ServiceContract__r.AccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getFMInfo</name>
        <label>getFMInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ScreenDatiFM</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getFMRecordTypeInfo.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Veicoli.firstSelectedRow.ServiceContract__r.ParentServiceContract.AccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getFMRecordTypeInfo</name>
        <label>getFMRecordTypeInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getFMInfo</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Contact</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_FleetManager</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getNameSelectedGroup</name>
        <label>getNameSelectedGroup</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Risolto_in_Chiamata</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>PicklistUfficio</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getRecordTypeDriver</name>
        <label>getRecordTypeDriver</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getCurrentAccountDriverInfo</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Account</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_Utilizzatore</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Copy_1_of_ScreenErrorMsg</name>
        <label>ScreenErrorMsg Contratti</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Copy_1_of_ErrorMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Non sono disponibili contratti da selezionare.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>ErrorOnInsert</name>
        <label>ErrorOnInsert</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>ErrorMsgonInsert</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Si è verificato un errore: impossibile completare l&apos;operazione.&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Screen_campi_case</name>
        <label>Screen campi case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Checkcampirequired</targetReference>
        </connector>
        <fields>
            <name>RequiredMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;I seguenti campi sono obbligatori: &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;Origine, Tipologia Richiesta, Categoria, Sottocategoria, Oggetto, Descrizione.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ShowRequiredMsg</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>requiredMsgZonacirc</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;Il campo Zona di Circolazione è obbligatorio per questa combinazione di categoria e sottocategoria.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>isZonaCircrRequired</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>RequiredMsgNote</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Il campo Note chiusura è obbligatorio in caso di risoluzione in chiamata.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ShowRequiredMsgNote</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Screen_campi_case_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Screen_campi_case_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Origin</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.TipoRichiesta__c</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Categoria__c</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.SottoCategoria__c</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Screen_campi_case_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Zona_di_circolazione__c</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Subject</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Description</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Risolto__c</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.NoteChiusura__c</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>caseRecord.Risolto__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.InvioEmailAutomatico__c</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Step_2</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>Screen_Ufficio</name>
        <label>Screen Ufficio</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>getNameSelectedGroup</targetReference>
        </connector>
        <fields>
            <name>PicklistUfficio</name>
            <choiceReferences>GroupCollection</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Ufficio Apertura:</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Screen_Veicolo_Contratto</name>
        <label>Screen Veicolo e Contratto</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>getDriverPrinc</targetReference>
        </connector>
        <fields>
            <name>Veicoli</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>ContractAsset__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Veicoli e Contratti</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>getContractAssetInfo</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;NomeAsset__c&quot;,&quot;guid&quot;:&quot;column-b406&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Targa Veicolo&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Targa&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;NomeContratto__c&quot;,&quot;guid&quot;:&quot;column-3858&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Contratto&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Contratto&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;StatoContratto__c&quot;,&quot;guid&quot;:&quot;column-a373&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Stato Contratto&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;ClienteContratto__c&quot;,&quot;guid&quot;:&quot;column-a011&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Cliente Contratto&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;tsDa__c&quot;,&quot;guid&quot;:&quot;column-7d06&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:4,&quot;label&quot;:&quot;Da&quot;,&quot;type&quot;:&quot;customDateTime&quot;},{&quot;apiName&quot;:&quot;tsA__c&quot;,&quot;guid&quot;:&quot;column-6015&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:5,&quot;label&quot;:&quot;A&quot;,&quot;type&quot;:&quot;customDateTime&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Step_1</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenDatiAgente</name>
        <label>ScreenDatiAgente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Clean_showErrorMsgAgente</targetReference>
        </connector>
        <fields>
            <name>ScreenDatiAgente_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenDatiAgente_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_3_of_EntChiamSelLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;Entità chiamante selezionata&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>Copy_3_of_EntChiamSel</name>
                    <fieldText>&lt;p&gt;{!caseRecord.EntChiamante__c}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenDatiAgente_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_3_of_Copy_1_of_LabelDriver</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;per conto dell&apos;utilizzatore&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>Copy_3_of_Copy_1_of_driverName</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!getDriverPrinc.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>Dati_Agente</name>
            <fieldText>Dati Chiamante</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Dati_Agente_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.SubAgente__c</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>showErrorSubagenteText</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;Il nome e la mail dell&apos;agente sono obbligatori.&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>showErrorAgente</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>NomeAgenteSubAg</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>GetAccountDetailsInfo.Agente__c</elementReference>
                    </defaultValue>
                    <fieldText>Agente</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                    <visibilityRule>
                        <conditionLogic>or</conditionLogic>
                        <conditions>
                            <leftValueReference>caseRecord.SubAgente__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>GetAccountDetailsInfo.Agente__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>NomeAgente</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 12px; color: rgb(68, 68, 68);&quot;&gt;Agente&lt;/span&gt;&lt;/p&gt;&lt;p&gt;{!GetAccountDetailsInfo.Agente__c}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>caseRecord.SubAgente__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>GetAccountDetailsInfo.Agente__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>EmailAgente</name>
                    <extensionName>flowruntime:email</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Email Agente</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>GetAccountDetailsInfo.EmailAgente__c</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>TelefonoAgente</name>
                    <extensionName>flowruntime:phone</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Telefono Agente</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>GetAccountDetailsInfo.AgenteTelefono__c</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>step4</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenDatiAltroRichiedente</name>
        <label>ScreenDatiAltroRichiedente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Check_Altro_Rich_Info</targetReference>
        </connector>
        <fields>
            <name>ShowRequiredMsgARText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Le informazioni dell&apos;altro richiedente sono obbligatorie.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ShowRequiredMsgAR</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>ScreenDatiAltroRichiedente_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenDatiAltroRichiedente_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_2_of_EntChiamSelLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;Entità chiamante selezionata&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>Copy_2_of_EntChiamSel</name>
                    <fieldText>&lt;p&gt;{!caseRecord.EntChiamante__c}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenDatiAltroRichiedente_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_2_of_Copy_1_of_LabelDriver</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;per conto dell&apos;utilizzatore&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>Copy_2_of_Copy_1_of_driverName</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!getDriverPrinc.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>Dati_Altro_Richiedente</name>
            <fieldText>Dati Chiamante</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Dati_Altro_Richiedente_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.NomeChiamante__c</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.EmailChiamante__c</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.TelChiamante__c</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>step4</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenDatiCliente</name>
        <label>ScreenDatiCliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_Cliente_contact_info</targetReference>
        </connector>
        <fields>
            <name>ScreenDatiCliente_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenDatiCliente_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_Copy_3_of_EntChiamSelLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;Entità chiamante selezionata&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>Copy_1_of_Copy_3_of_EntChiamSel</name>
                    <fieldText>&lt;p&gt;{!caseRecord.EntChiamante__c}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenDatiCliente_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_Copy_3_of_Copy_1_of_LabelDriver</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;per conto dell&apos;utilizzatore&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>Copy_1_of_Copy_3_of_Copy_1_of_driverName</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!getDriverPrinc.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>Dati_Cliente</name>
            <fieldText>Dati Chiamante</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Dati_Cliente_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>NominativoCliente</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Veicoli.firstSelectedRow.ClienteContratto__c</elementReference>
                    </defaultValue>
                    <fieldText>Riferimento entità chiamante</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>EmailCliente</name>
                    <extensionName>flowruntime:email</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Email Cliente</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>GetAccountDetailsInfo.Email__c</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>TelefonoCliente</name>
                    <extensionName>flowruntime:phone</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Telefono Cliente</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>GetAccountDetailsInfo.Phone__c</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>step4</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenDatiFM</name>
        <label>ScreenDatiFM</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_FM_contact_info</targetReference>
        </connector>
        <fields>
            <name>ScreenDatiFM_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenDatiFM_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_EntChiamSelLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;Entità chiamante selezionata&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>Copy_1_of_EntChiamSel</name>
                    <fieldText>&lt;p&gt;{!caseRecord.EntChiamante__c}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenDatiFM_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_Copy_1_of_LabelDriver</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;per conto dell&apos;utilizzatore&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>Copy_1_of_Copy_1_of_driverName</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!getDriverPrinc.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>Dati_Fleet_Manager</name>
            <fieldText>Dati Chiamante</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Dati_Fleet_Manager_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>NominativoFM</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>getFMInfo.Name</elementReference>
                    </defaultValue>
                    <fieldText>Riferimento entità chiamante</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>EmailFM</name>
                    <extensionName>flowruntime:email</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Email Entità chiamante</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>getFMInfo.Email</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>TelefonoFM</name>
                    <extensionName>flowruntime:phone</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Telefono entità chiamante</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>getFMInfo.Phone</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>step4</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenDatiUtilizzatore</name>
        <label>ScreenDatiUtilizzatore</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_Util_contact_info</targetReference>
        </connector>
        <fields>
            <name>ScreenDatiUtilizzatore_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenDatiUtilizzatore_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>EntChiamSelLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;Entità chiamante selezionata&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>EntChiamSel</name>
                    <fieldText>&lt;p&gt;{!caseRecord.EntChiamante__c}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenDatiUtilizzatore_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_LabelDriver</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;per conto dell&apos;utilizzatore&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>Copy_1_of_driverName</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!getDriverPrinc.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>Dati_Utilizzatore</name>
            <fieldText>Dati Chiamante</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Dati_Utilizzatore_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>NominativoDriver</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>getCurrentAccountDriverInfo.Name</elementReference>
                    </defaultValue>
                    <fieldText>Riferimento entità chiamante</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>EmailDriver</name>
                    <extensionName>flowruntime:email</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Email Entità chiamante</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>getCurrentAccountDriverInfo.PersonContact.Email</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>TelefonoDriver</name>
                    <extensionName>flowruntime:phone</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Telefono entità chiamante</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>getCurrentAccountDriverInfo.PersonContact.Phone</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>step4</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenEntitaChiamante</name>
        <label>ScreenEntitaChiamante</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Check_Ent_Chiamante</targetReference>
        </connector>
        <fields>
            <name>ShowRequiredMsgECText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;E&apos; obbligatorio selezionare l&apos;entità chiamante.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ShowRequiredMsgEC</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>ScreenEntitaChiamante_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenEntitaChiamante_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.EntChiamante__c</objectFieldReference>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenEntitaChiamante_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>LabelDriver</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;per conto dell&apos;utilizzatore&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <fields>
                    <name>driverName</name>
                    <fieldText>&lt;p&gt;{!getDriverPrinc.Name}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Step_3</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenErrorMsg</name>
        <label>ScreenErrorMsg Contratti</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ErrorMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Non sono disponibili contratti da selezionare.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>ScreenErrorMsgUfficio</name>
        <label>ScreenErrorMsgUfficio</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>ErrMsgUfficio</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;L&apos;utente corrente non è assegnato a nessun ufficio.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <stages>
        <name>step4</name>
        <isActive>false</isActive>
        <label>step4</label>
        <stageOrder>4</stageOrder>
    </stages>
    <stages>
        <name>Step_1</name>
        <isActive>true</isActive>
        <label>Step_1</label>
        <stageOrder>1</stageOrder>
    </stages>
    <stages>
        <name>Step_2</name>
        <isActive>false</isActive>
        <label>Step_2</label>
        <stageOrder>2</stageOrder>
    </stages>
    <stages>
        <name>Step_3</name>
        <isActive>false</isActive>
        <label>Step_3</label>
        <stageOrder>3</stageOrder>
    </stages>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>urcs_CaseCheckUfficiOperatore</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>CheckZonaCircRequired</name>
        <label>CheckZonaCircRequired</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_flag_result</targetReference>
        </connector>
        <flowName>urcs_CaseCheckZonaCircRequired</flowName>
        <inputAssignments>
            <name>Categoria</name>
            <value>
                <elementReference>caseRecord.Categoria__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Sottocategoria</name>
            <value>
                <elementReference>caseRecord.SottoCategoria__c</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>isZonaCircrRequired</assignToReference>
            <name>isZonaCircrRequired</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>urcs_CaseCheckUfficiOperatore</name>
        <label>urcs_CaseCheckUfficiOperatore</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>assegnato_a_uffici_rental</targetReference>
        </connector>
        <flowName>urcs_CaseCheckUfficiOperatore</flowName>
        <outputAssignments>
            <assignToReference>ufficiQueueList</assignToReference>
            <name>ufficiQueueList</name>
        </outputAssignments>
    </subflows>
    <variables>
        <name>caseRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>contractIdList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>groupIdList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>isZonaCircrRequired</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>quadrocContractIdList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>QueueIdList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>showErrorAgente</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ShowRequiredMsg</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ShowRequiredMsgAR</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ShowRequiredMsgEC</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ShowRequiredMsgNote</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ufficiQueueList</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Group</objectType>
    </variables>
</Flow>
