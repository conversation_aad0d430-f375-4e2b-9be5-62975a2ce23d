public with sharing class InsurancePolicyController {
    @AuraEnabled(cacheable=true)
    public static List<InsurancePolicy> getInsurancePolicies(String subject) {
        System.debug('getInsurancePolicies subject: ' + subject);
        
        if (!Schema.sObjectType.InsurancePolicy.isAccessible()) {
            throw new AuraHandledException('Non hai i permessi per visualizzare le polizze assicurative.');
        }

        return [
            SELECT Id, Name 
            FROM InsurancePolicy 
            WHERE NameInsuredId = :subject
        ];
    }
    
    @AuraEnabled(cacheable=true)
    public static List<Opportunity> getTrattativa(String Trattativa) {
        System.debug('getTrattativa Trattativa: ' + Trattativa);
        if (!Schema.sObjectType.Opportunity.isAccessible()) {
            throw new AuraHandledException('Non hai i permessi per visualizzare le opportunità.');
        }
        return [
            SELECT Id,Name,AccountId from Opportunity where AccountId= :Trattativa ORDER BY Name
        ];
    }
    
    @AuraEnabled(cacheable=true)
    public static List<Asset> getProdottoNon(String ProdottoNon) {
        System.debug('getProdottoNon ProdottoNon: ' + ProdottoNon);
        if (!Schema.sObjectType.Asset.isAccessible()) {
            throw new AuraHandledException('Non hai i permessi per visualizzare gli asset.');
        }
        return [
            SELECT Id,Name ,AccountId from Asset where AccountId = :ProdottoNon ORDER BY Name
        ];
    }

    @AuraEnabled(cacheable=false)
    public static List<Id> getFilteredPolicyIds(Id accountId) {

        List<InsurancePolicy> policies = [
            SELECT Id, ActiveDate__c
            FROM InsurancePolicy
            WHERE NameInsuredId = :accountId
            AND RecordType.DeveloperName != 'PU_POSITION'
        ];
        
        List<Id> policyIds = new List<Id>();
        for (InsurancePolicy p : policies) {
            if(p.ActiveDate__c == null || Date.TODAY() < p.ActiveDate__c){
                policyIds.add(p.Id);
            }
        }
        return policyIds;
    }

    @AuraEnabled
    public static Map<String, Object> checkAbbinato(String accountId){
        Map<String, Object> returnValues = new Map<String, Object>();
        returnValues.put('isAbbinato', false);
        try {
            Id idAzienda = [SELECT IdAzienda__c FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1]?.IdAzienda__c;
            Id idAccountAgency = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
                .get('AccountAgency')
                .getRecordTypeId();
            Boolean isAbbinato = [
                    SELECT Id
                    FROM FinServ__AccountAccountRelation__c
                    WHERE
                        FinServ__Account__c = :accountId
                        AND FinServ__RelatedAccount__c = :idAzienda
                        AND RecordTypeId = :idAccountAgency
                ]
                .isEmpty();
            returnValues.put('isAbbinato', !isAbbinato);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
        return returnValues;
    }

    @AuraEnabled
    public static List<Id> getUserFromAgency() {
        List<Id> userIds = new List<Id>();
        try {
            if (Schema.sObjectType.User.isAccessible()) {
                Id idAzienda = [SELECT IdAzienda__c FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1]?.IdAzienda__c;
                if (idAzienda != null) {
                    for (User u : [SELECT Id FROM User WHERE IdAzienda__c = :idAzienda AND IsActive = true]) {
                        userIds.add(u.Id);
                    }
                    return userIds;
                } else {
                    throw new AuraHandledException('L\'utente non ha un\'azienda associata.');
                }
            }
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
        return userIds;
    } 

    //metodo per querare l'array in base alla matrixVersion (Essig)
    public static  List<CalculationMatrixRow> getProductData(String matrixVersion){
 
        List<CalculationMatrixRow> calcMatrix = new List<CalculationMatrixRow>();
 
        try {
            calcMatrix =  [ SELECT OutputData FROM CalculationMatrixRow
            WHERE CalculationMatrixVersion.ApiName = :matrixVersion
            AND IsVersionEnabled = true
            AND CalculationMatrixVersion.IsEnabled = true];
 
            /*
            for (CalculationMatrixRow currentRow : calcMatrix) {
 
                matrixRow.add(JSON.deserializeUntyped(currentRow.OutputData));
 
            }
                */
 
       
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
 
        return calcMatrix;
    }
 
 
    //metodo init per chiamare checkifAbbinato e getproductData
 
    @AuraEnabled(cacheable=true)
    public static Map<String, Object> initialize(String matrixVersion, String accountId) {
 
        Map<String, Object> returnValues = new Map<String, Object>();
 
        returnValues.put('error', false);
 
        try {
 
        Map<String, Object> checkAbbinato = checkAbbinato(accountId);
 
        List<CalculationMatrixRow> productData = getProductData(matrixVersion);
 
        returnValues.put('isAbbinato', checkAbbinato.get('isAbbinato'));
        returnValues.put('productData', productData);
 
       
        } catch (Exception e) {
 
            returnValues.put('error', true);
            returnValues.put('errorMessage', e.getMessage());
            returnValues.put('errorStackTraceString', e.getStackTraceString());
 
        }
 
        return returnValues;
 
    }
}