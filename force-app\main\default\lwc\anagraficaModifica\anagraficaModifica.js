import { LightningElement, api, track } from "lwc";
import getStati from "@salesforce/apex/AddressController.getStati";
import getProvince from "@salesforce/apex/AddressController.getProvince";
import getComuni from "@salesforce/apex/AddressController.getComuni";
import getAnagrafica from "@salesforce/apex/AnagraficaController.getAnagrafica";
import aggiornamentoAnagrafica from "@salesforce/apex/AnagraficaController.aggiornamentoAnagrafica";
import pubsub from "omnistudio/pubsub";
import userId from '@salesforce/user/Id';
import Utils from 'c/utils';

export default class AnagraficaModifica extends LightningElement {

    @api
    compagnia = "";
    @api
    userId = "";
    @api
    recordId = "";
    //AC: questa proprietà è necessaria per far funzinare il componente in una flex card.
    //    flex card, ad oggi (02/2025), prima inizializza lwc, poi inizializza i dati della flex card e li passa a lwc.
    //    questo fa si che ci potrebbero essere le proprietà valorizzate (nel caso di aggiornamento) ma che alla connected callback risultano ancora vuote.
    //    se setInit è valorizzata, sono certo che anche le altre proprietà saranno valorizzate.
    //    purtroppo questa proprietà dovrà essere usata anche in contesti diversi da flex card, altrimetni non partirà mai l'init.
    //    inoltre è necessario che il datasource di flex card contenga anche la proprietà setInit.
    @api
    setInit;

    @track
    initDone = false;
    @track
    data = {
        stato: null,
        provincia: null,
        indirizzoResidenza: null,
        indirizzoDomicilio: null
    }
    @track
    options = {
        stati: [],
        province: []
    }
    @track
    isLoading = false;
    @track
    resiDomi = false;
    @track
    errList = [];
    invalid = false;
    @track
    showError = false;
    @track
    titleError = "";
    @track
    listaCompagnie = [];

    isLoadingCnt = 0;
    indDomiOrig;

    get mostraIndirizzi() {
        return this.options.stati.length > 0 && this.options.province.length > 0;
    }
    
    connectedCallback() {
        //AC: come spiegato in testa, questo intervallo è necessario per verificare che gli attributi vengano passati dalla flex card.
        //    per evitare che il ciclo venga eseguito all'infinito in caso di mancata assegnazione di setInit, imposto un contatore.
        //    il contatore arriva fino a 20 (per un totale di 2 secondi), se non viene inizializzato, interrompe l'intervallo e mosta un messaggio di errore.
        let cnt = 0;
        const ci = setInterval(() => {
            if (this.setInit) {
                this.init();
                clearInterval(ci);
            } else {
                cnt++;
                if (cnt > 20) {
                    clearInterval(ci);
                    this.error("errore inzializzazione");
                }
            }
        }, 20);
    }

    disconnectedCallback() {
    }

    renderedCallback() {
    }

    error(err) {
        console.log(err);
        //this.errorInit = true;
    }

    showLoader() {
        this.isLoadingCnt++;
        this.isLoading = true;
    }

    hideLoader() {
        this.isLoadingCnt--;
        if (this.isLoadingCnt === 0) {
            this.isLoading = false;
        }
    }

    init() {
        if (this.userId === "") {
            this.userId = userId;
        }
        //AC: preleva tutti gli stati
        this.refs.loader.showLoader();
        getStati({
            ipInput: {
                "codiceABI": "",
                "codiceBelfiore": "",
                "descrizione": "",
                "filtroGeografico": "DISATTIVO",
                "soloAttivi": "true"
            }
        }).then(response => {
            this.options.stati = Utils.formattaAbiStato(response.returnList);
            this.options.stati = this.formatData(response.returnList, "codiceABI", "descrizione");
            //AC: questo serve per evitare i duplicati (il servizio torna duplicati)
            this.options.stati = Array.from(new Map(this.options.stati.map(d => [d.Id, d])).values());
        }).catch(err => {
            this.error(err);
        }).finally(() => this.refs.loader.hideLoader());
        //AC: preleva tutte le province
        this.refs.loader.showLoader();
        getProvince({
            ipInput: {
                codiceISTATRegione: "",
                codiceUnipolRegione: "",
                descrizione: "",
                sigla: ""
            }
        }).then(response => {
            this.options.province = this.formatData(response.returnList, "sigla", "descrizione");
        }).catch(err => {
            this.error(err);
        }).finally(() => this.refs.loader.hideLoader());
        //AC: preleva i dati anagrafici da modificare
        this.refs.loader.showLoader();
        getAnagrafica({
            ipInput: {
                UserId: this.userId,
                recordId: this.recordId
            }
        }).then(response => {
            this.listaCompagnie = response.returnList.recapiti.inserisci.PropOptions;
            const datiAnagrafici = response.returnList.datiAnagrafici;
            this.data = Array.isArray(datiAnagrafici) ? datiAnagrafici[0].modifica : datiAnagrafici.modifica;
            //AC: da mail di Di Bonito del 10 Apr 2025 16:54:43, tipoAnagrafica deve essere impostato a "D", altrimenti non si può aggiornare.
            this.data.anagrafica.tipoAnagraficaRichiesto = "D";
            //AC: questo controllo si potrà togliere non appena ci saranno dati congruenti
            this.data.anagrafica.nazioneNascita = this.data.anagrafica.nazioneNascita === "Italia" ? "086" : this.data.anagrafica.nazioneNascita;
            this.data.anagrafica.provinciaNascita = this.data.extra.provinciaNascita;
            console.log("this.data.indirizzi: "+ JSON.stringify(this.data.indirizzi));
            //AC: imposto i dati di indirizzo residenza e domicilio
            this.data.indirizzoResidenza = this.data.indirizzi.find(ind => ind.tipoIndirizzo === "RESI");
            this.data.indirizzoDomicilio = this.data.indirizzi.find(ind => ind.tipoIndirizzo === "DOMI");
            const campiInd = this.refs.indirizzoResidenza;
            if(this.data.indirizzoResidenza.codiceBelfioreStato === 'Z000')
                Utils.checkIndirizzo(this.data.indirizzoResidenza, campiInd);

            if(this.data.indirizzoDomicilio.codiceBelfioreStato === 'Z000')
                Utils.checkIndirizzo(this.data.indirizzoDomicilio, campiInd);
            
            /*
            //AC: queste assegnazioni sono fatte per ovviare ai problemi di dati sporchi in ambienti di sviluppo.
            //    una volta verificati i dati corretti in ambienti stabili, bisognerebbe rimuovere queste assegnazioni.
            this.data.indirizzoResidenza.stato = this.data.indirizzoResidenza.stato.toLowerCase() === "it" ? "" : this.data.indirizzoResidenza.stato;
            this.data.indirizzoDomicilio.stato = this.data.indirizzoDomicilio.stato.toLowerCase() === "it" ? "" : this.data.indirizzoResidenza.stato;
            this.data.indirizzoResidenza.indirizzoBreve = `${this.data.indirizzoResidenza.dug} ${this.data.indirizzoResidenza.dus}`;
            this.data.indirizzoDomicilio.indirizzoBreve = `${this.data.indirizzoDomicilio.dug} ${this.data.indirizzoDomicilio.dus}`;
            */
            //AC: a causa di dati sporchi, nazioneNascita potrebbe essere "-", in questo caso metto come nazioneNascita "086" (italia);
            this.data.anagrafica.nazioneNascita = this.data.anagrafica.nazioneNascita === "-" || this.data.anagrafica.nazioneNascita === "" 
                ? "086" 
                : this.data.anagrafica.nazioneNascita;
            this.data.privacy = this.data.privacy ?? {
                "datiAdesione": {
                    "tipoPrivacy": "00",
                    "dataInizioEffetto": "2024-12-04",
                    "applicazioneFine": null,
                    "applicazioneInizio": "PUPTF"
                }
            };
            //AC: questa assegnazione è stata fatta perchè non sono quasi mai presenti i dati di agenzia.
            //    per eseguire i test è necessario passare i dati agenzia
            this.data.datiAgenzia = this.data.datiAgenzia.codiceAgenziaPrevalente ? this.data.datiAgenzia : {
                "flagClienteTop": false,
                "dataClienteTop": null,
                "flagAdesioneFEA": false,
                "flagProprietaContattiFea": false,
                "codiceSubagenzia": "915",
                "codiceAgenziaPrevalente": "01853",
                "flagAutorizzazioneCauzione": true,
                "dataCessazioneCliente": null,
                "statoSoggetto": "PO",
                "codiceSoggettoCanale": null,
                "dataInizioEffetto": "2024-10-24",
                "dataFineEffetto": null,
                "compagnia": "unipolsai",
                "codiceCanale": "AGE"
            };
            //AC: questo è necessario perchè le proprietà valorizzati con null non vengono messe nel json di risposta dal controller apex.
            //    c'è un modo per far tornare tutto?
            [
                "flagClienteTop",
                "dataClienteTop",
                "flagAdesioneFEA",
                "flagProprietaContattiFea",
                "codiceSubagenzia",
                "codiceAgenziaPrevalente",
                "flagAutorizzazioneCauzione",
                "dataCessazioneCliente",
                "statoSoggetto",
                "codiceSoggettoCanale",
                "dataInizioEffetto",
                "dataFineEffetto",
                "compagnia",
                "codiceCanale"
            ].forEach(key => {
                if (this.data.datiAgenzia[key] === undefined) {
                    this.data.datiAgenzia[key] = null;
                }
            })
            //AC: fine assegnazioni
            this.indDomiOrig = {...this.data.indirizzoDomicilio};
            this.resiDomi = this.data.indirizzoResidenza.indirizzoCompleto === this.data.indirizzoDomicilio.indirizzoCompleto || 
            this.data.indirizzoDomicilio.indirizzoCompleto == undefined ||   this.data.indirizzoDomicilio.indirizzoCompleto == null ||  this.data.indirizzoDomicilio.indirizzoCompleto == '';
            if (this.resiDomi) {
                this.data.indirizzoDomicilio.skipPrimaNormalizzazione = true;
                this.data.indirizzoDomicilio.edit = false;  
            } else {
                this.data.indirizzoDomicilio.skipPrimaNormalizzazione = this.data.indirizzoDomicilio.edit;
            }
            //this.data.indirizzoDomicilio.skipPrimaNormalizzazione = this.resiDomi && !this.data.indirizzoResidenza.edit;
            //this.data.indirizzoDomicilio.edit = !this.resiDomi && this.data.indirizzoDomicilio.tipoNormalizzato === "N";
            //AC: verifica che siano presenti tutti i dati di professione
            ["professione", "mercatoPreferenziale", "impiego", "settore", "specializzazione"].forEach(str => {
                if (this.data.professione[str] === undefined) {
                    this.data.professione[str] = {};
                    this.data.professione[str].codice = "";
                }
            });
            //AC: preleva il comune di nascita in base alla provincia di nascita
            this.refs.loader.showLoader();
            getComuni({
                ipInput: {
                    siglaProvincia: this.data.extra.provinciaNascita,
                    soloAttivi: "true",
                    limiteOccorrenze: 150,
                    codiceBelfiore: "",
                    descrizione: ""
                }
            }).then(response => {
                this.options.comuni = this.formatData(response.returnList, "codiceBelfiore", "descrizione");
                this.data.comune = this.options.comuni.find(obj => obj.codiceBelfiore === this.data.anagrafica.comuneNascita);
            }).catch(err => {
                this.error(err);
            }).finally(() => this.refs.loader.hideLoader());
            //AC: avvia l'intervallo che verifica che regione e provincia siano stati caricati per poter
            //    impostare il dato
            let ci = setInterval(() => {
                if (this.options.stati.length > 0 && this.options.province.length > 0) {
                    this.data.stato = this.options.stati.find(obj => obj.Id === this.data.anagrafica.nazioneNascita);
                    this.data.provincia = this.options.province.find(obj => obj.Id === this.data.extra.provinciaNascita);
                    clearInterval(ci);
                } else {
                    clearInterval(ci);
                }
            }, 20);
            this.initDone = true;
        }).catch(err => {
            this.error(err);
        }).finally(() => this.refs.loader.hideLoader());
    }

    //AC: formatta i dati rendendoli compatibili al componente lookup
    formatData(data, idField, descriptionField) {
        const destination = [];
        data.forEach(obj => {
            //AC: non posso assegnare direttamente perchè bisogna indicare un Id che, dal servizio, non torna
            obj.Id = obj.value =obj[idField];
            obj.primaryDisplayField = obj.label = obj[descriptionField];
            obj.searchDisplayName = obj[descriptionField]; 
            destination.push(obj);
        });
        return destination;
    }

    resiDomiExec() {          
        if(this.data.indirizzoDomicilio.codiceBelfioreStato !== "Z000" && this.data.indirizzoDomicilio.codiceBelfioreStato )
            //29/08/2025: aggiunto check su codiceBelfioreStato (se valorizzato E diverso da z000). Quando era vuoto domicilio, veniva skippata la logica sottostante.
            return;
        this.data.indirizzoDomicilio = this.resiDomi
            ? {...this.data.indirizzoResidenza}
            : {...this.indDomiOrig};
        this.data.indirizzoDomicilio.idIndirizzo = this.indDomiOrig?.idIndirizzo;
        this.data.indirizzoDomicilio.tipoIndirizzo = "DOMI";
        const domi = this.data.indirizzoDomicilio;
        //AC: in teoria bisognerebbe usare indirizzoBreve, in dev questo campo non c'è, quindi lo devo ricostruire.
        //    verificare se i dati 
        /*
        let indirizzo = `${domi.dug} ${domi.dus}`;
        this.refs.indirizzoDomicilio.setData(indirizzo,
            domi.numeroCivico,
            domi.localita,
            domi.cap,
            domi.presso,
            domi.stato,
            domi.abbreviazioneProvincia,
            domi.codiceBelfioreComune,
            this.resiDomi,
            this.resiDomi ? true : null,
            !this.resiDomi
        );
        */

        if(this.data.indirizzoDomicilio.codiceBelfioreStato === "Z000")
            domi.indirizzo = `${domi.dug} ${domi.dus}`; 
        this.refs.indirizzoDomicilio.setData(domi,
            this.resiDomi,
            this.resiDomi ? true : null,
            !this.resiDomi
        );
    }

    handleCheckboxChange(e) {
        this.resiDomi = e.target.checked;
        this.data.indirizzoDomicilio = this.data.indirizzoResidenza;
            
        this.data.indirizzoDomicilio.idIndirizzo = this.indDomiOrig?.idIndirizzo;
        this.data.indirizzoDomicilio.tipoIndirizzo = "DOMI";
        let domi = this.data.indirizzoDomicilio;
        
        if(this.data.indirizzoDomicilio.codiceBelfioreStato === "Z000")
            domi.indirizzo = `${domi.dug} ${domi.dus}`; 
        
        console.log("domi: "+JSON.stringify(domi));
       this.refs.indirizzoDomicilio.setData(domi,
            this.resiDomi,
            this.resiDomi ? true : null,
            !this.resiDomi
        );
    }

    handleIndirizzoResidenza(e) {
        console.log("indirizzo residenza in handleIndirizzoResidenza: "+ JSON.stringify(e.detail));
        const indResi = e.detail;
        const decode = this.refs.indirizzoResidenza.getNormalizzatoToAggiornamento();
        Object.keys(decode).forEach(key => {
            this.data.indirizzoResidenza[decode[key]] = indResi[key];
        });
        this.resiDomiExec();
    }

    handleIndirizzoDomicilio(e) {
        console.log("indirizzo domicilio", e);
    }

    handleAnnulla() {
        pubsub.fire("close_modal", "close", null);
    }

    handleConferma() {
        this.errList = [];
        const validAnag = this.refs.anagrafica.valid();
        let validIndRes = this.refs.indirizzoResidenza.valid();
        let validIndDom = this.refs.indirizzoDomicilio.valid();
        const validProf = this.refs.professione.valid();
        console.log("validazione anagrafica", JSON.stringify(validAnag));
        console.log("validazione indirizzoResidenza", JSON.stringify(validIndRes));
        console.log("validazione indirizzoDomicilio", JSON.stringify(validIndDom));
        console.log("validazione professione", JSON.stringify(validProf));
        console.log('REFS: ' + JSON.stringify(this.refs));
        if (validAnag.valid
            && validIndRes.valid
            && (this.resiDomi || (!this.resiDomi && validIndDom.valid))
            && validProf.valid
        ) {
            //AC: verificare se questo è ancora necessario o se è stato risolto con la modifica alla normalizzazione e validazione
            validIndRes.data.idIndirizzo = this.data.indirizzoResidenza?.idIndirizzo;
            validIndRes.data.tipoIndirizzo = this.data.indirizzoResidenza.tipoIndirizzo;
            //validIndDom.data = this.resiDomi ? {...validIndRes.data} : validIndDom.data;
            validIndDom.data.idIndirizzo = this.data.indirizzoDomicilio?.idIndirizzo;
            validIndDom.data.tipoIndirizzo = this.data.indirizzoDomicilio.tipoIndirizzo;
            validIndDom.data.latitudine = this.data.indirizzoDomicilio?.latitudine || 0;
            validIndDom.data.longitudine = this.data.indirizzoDomicilio?.longitudine || 0;

            console.log("validIndDom: "+ JSON.stringify(validIndDom));
            validIndRes.data.indirizzoCompleto = (validIndRes.data.codiceBelfioreStato === 'Z000') ?
            `${validIndRes.data.dug} ${validIndRes.data.dus}` : validIndRes.data.indirizzoBreve;
            validIndDom.data.indirizzoCompleto = (validIndDom.data.codiceBelfioreStato === 'Z000') ? 
            `${validIndDom.data.dug} ${validIndDom.data.dus}` : validIndDom.data.indirizzoBreve;
            
            const indirizzi = [validIndRes.data]
                .concat(validIndDom.data);
            
            const professioni = {
                professione: {
                    codice: validProf.data.professione?.value,
                    descrizione: validProf.data.professione?.label
                },
                mercatoPreferenziale: {
                    codice: validProf.data.mercatoPreferenziale,
                    //descrizione: validProf.data.mercatoPreferenziale.label
                },
                settore: {
                    codice: validProf.data.settore?.value,
                    descrizione: validProf.data.settore?.label
                },
                impiego: {
                    codice: validProf.data.tipoProfessione?.value,
                    descrizione: validProf.data.tipoProfessione?.label
                },
                specializzazione: {
                    codice: validProf.data.specializzazione?.value,
                    descrizione: validProf.data.specializzazione?.label
                }
            };
            validAnag.data.nazioneNascita = validAnag.data.nazioneNascita === "" ? "086" : validAnag.data.nazioneNascita;
            //AC: questa decodifica è necessaria perchè potrebbe arrivare sia il codice sia la descrizione
            const cfPiStato = Utils.getCfPiStato();
            validAnag.data.statoCodiceFiscalePartitaIva = cfPiStato[validAnag.data.statoCodiceFiscalePartitaIva];
            validAnag.data.statoCodiceFiscale = cfPiStato[validAnag.data.statoCodiceFiscale];
            
            let cntAgg = 0;
            this.errList = [];
            this.refs.loader.showLoader();
            let request = {
                ipInput: {
                    anagrafica: validAnag.data,
                    professione: JSON.stringify(professioni),
                    indirizzo: JSON.stringify(indirizzi),
                    contatti: [] //, JSON.stringify(this.data.contatti),
                    //datiAgenzia: JSON.stringify(this.data.datiAgenzia),
                    //privacy: JSON.stringify(this.data.privacy)
                }
            };

            //AC: in caso di modifica, la nazioneNascita (ad esempio italia = 086) deve essere sovrascritta dal valore di comuneNascitaBelfiore LS inibito sviluppo precedente
            //request.ipInput.anagrafica.nazioneNascita = request.ipInput.anagrafica.comuneNascitaBelfiore;
            //request.ipInput.anagrafica.agenzia = this.data.datiAgenzia.codiceAgenziaPrevalente;
            request.ipInput.anagrafica = JSON.stringify(request.ipInput.anagrafica);
            console.log('###REQ: ' + JSON.stringify(request));
            aggiornamentoAnagrafica(request).then(response => {
                console.log("aggiornamento anagrafica", response);
                const anag = JSON.parse(request.ipInput.anagrafica);
                if (response.returnList.Messagio !== undefined) {
                    this.errList.push({id: `anagraficaModifica${anag.compagnia}`, value: `${anag.compagnia}: ${response.returnList.Messagio}`});
                } else if (response.returnList.error !== undefined) {
                    this.errList.push({id: `anagraficaModifica${anag.compagnia}`, value: `${anag.compagnia}: Errore aggiornamento servizio anagrafica`});
                } else {
                    console.log("Aggiornamento anagrafica OK");
                }
                cntAgg++;
            }).catch(err => {
                this.error(err);
            }).finally(() => {
                    this.refs.loader.hideLoader();
            });
        
            const ciAggiorna = setInterval(() => {
                clearInterval(ciAggiorna);
                this.showError = this.errList.length > 0;
                if (this.showError) {
                    this.titleError = "Errore aggiornamento anagrafica";
                } else {
                    this.handleAnnulla();
                }
            }, 20);
        } else {
            this.invalid = true;
            this.errList = []
                .concat(this.decodeError(validAnag.campi, "anagrafica", "", ""))
                .concat(this.decodeError(validProf.campi, "professione", "", ""))
                .concat(this.decodeError(validIndRes.campi, "indirizzoResidenza", "", "residenza"))
                .concat(!validIndRes.normalizzato ? [{id: "n0", value: "Normalizza indirizzo residenza"}] : [])
                .concat(!this.resiDomi ? this.decodeError(validIndDom.campi, "indirizzoDomicilio", "", "domicilio") : [])
                .concat(!this.resiDomi && !validIndDom.normalizzato ? [{id: "n1", value: "Normalizza indirizzo domicilio"}] : []);
            console.log("errore", this.errList);
        }
        this.titleError = "Errore: compila i seguenti campi";
        this.showError = this.errList.length > 0;
    }

    decodeError(source, obj, pre, post) {
        const errDecode = this.refs[obj].getErrDecode();
        return source.map((k, i) => {
            return {
                id: `${obj}${i}`,
                value: `${pre} ${errDecode[k]} ${post}`.trim()
            };
        });
    }
}