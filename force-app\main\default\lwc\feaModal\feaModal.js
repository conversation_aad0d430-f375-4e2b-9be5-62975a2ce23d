import { LightningElement, api, track, wire } from 'lwc';
import getFea from "@salesforce/apex/FeaController.getFea";
import checkAggiorna from "@salesforce/apex/FeaController.checkAggiorna";
import refreshStatoEmail from "@salesforce/apex/FeaController.refreshStatoEmail";
//import getContatti from "@salesforce/apex/FeaController.getContatti";
import getContattiForm from "@salesforce/apex/FeaController.getContattiForm";
//import getStampe from "@salesforce/apex/FeaController.getStampe";
import conferma from "@salesforce/apex/FeaController.conferma";
import revoca from "@salesforce/apex/FeaController.revoca";
import checkDocumento from "@salesforce/apex/FeaController.checkDocumento";
import certifcazioneContatti from "@salesforce/apex/FeaController.certifcazioneContatti";
//import getDocumentaleLogStampa from "@salesforce/apex/FeaController.getDocumentaleLogStampa";
//import Utils from 'c/utils';
import pubsub from "omnistudio/pubsub";
import {CurrentPageReference} from 'lightning/navigation';

const STATO_VALIDAZIONE_EMAIL_NO = "N";
const STATO_VALIDAZIONE_EMAIL_I = "I";
const STATO_VALIDAZIONE_EMAIL_SI = "V";
export default class FeaModal extends LightningElement {

    @api
    recordId = "";
    //AC: questa proprietà è necessaria per far funzinare il componente in una flex card.
    //    flex card, ad oggi (02/2025), prima inizializza lwc, poi inizializza i dati della flex card e li passa a lwc.
    //    questo fa si che ci potrebbero essere le proprietà valorizzate (nel caso di aggiornamento) ma che alla connected callback risultano ancora vuote.
    //    se setInit è valorizzata, sono certo che anche le altre proprietà saranno valorizzate.
    //    purtroppo questa proprietà dovrà essere usata anche in contesti diversi da flex card, altrimetni non partirà mai l'init.
    //    inoltre è necessario che il datasource di flex card contenga anche la proprietà setInit.
    @api
    setInit;
    @api
    adesione = ""; 
    @api
    ciu = "";
    @api
    compagnia = "";
    @api
    cellulare = "";
    @api
    email = "";
    @api
    startDate = "";
    @api
    companyForModaleFea = ""; //Arriva dalla flexcard (Valori: "Unisalute" o "Altro")

    @track 
    hideConfirmButton = false;
    @track
    btnConfermaEsegui = true;
    @track
    btnAggiorna = false;
    @track
    conferma = {
        mostra: false,
        azioneConferma: "",
        titolo: "",
        testo: ""
    };
    @track
    panel = {
        edit: false,
        detail: true,
        summary: false
    };
    @track
    options = [
        { label: 'Si', value: 'si' } //, { label: 'No', value: 'no' }
    ];
    @track
    data = {
        adesione: "No",
        dataInizio: "", //"25/10/2024"
        cellulare: "", //"+39 3356282169"
        email: "", //"<EMAIL>"
        statoValidazioneEmail: STATO_VALIDAZIONE_EMAIL_NO
    };
    @track
    form = {
        adesioneFea: "si",
        dataInizio: "",
        cellulare: "",
        cellulareReadonly: false,
        email: "",
        emailReadonly: false,
        emailConfirm: "",
        emailConfirmReadonly: false,
        mostraEmailConfirm: true,
        readonly: {
            cellulare: false,
            email: false,
            emailConfirm: false
        },
        disabled: {
            cellulare: false,
            email: false,
            emailConfirm: false
        }
    };

    @track
    errore = {
        cellulareObbligatorio: false,
        formatoCellulare: false,
        emailObbligatoria: false,
        formatoEmail: false,
        emailConfermaObbligatoria: false,
        formatoConfermaEmail: false,
        emailDiverse: false,
        aggiornamentoFEA: false
    };

    @track
    labelAnnulla = "Annulla";
    @track
    feiData = {
        mostraFeiContainer: false,
        feiId: 'SFEA.SIGN',
        fiscalCode: '',
        payload: '',
        permissionSet: ''
    };

    @track
    datiFea = null;

    @wire(CurrentPageReference)
    getStateParameters(currentPageReference) {
        console.log(currentPageReference);
        if (currentPageReference) {
            this.recordId = currentPageReference.attributes.recordId;
        }
    }

    recordId = "";
    wait20Seconds = false;

    //AC: la scelta di usare le proprietà è legata al fatto che la visibilità dovrebbe essere soggetta a logiche
    //    proprietà che gestiscono la visibilità delle icone
    /*get iconNameStampe() {
        return this.stampe.isOpen ? 'utility:chevrondown' : 'utility:chevronright';
    }
    get iconNameContatti() {
        return this.contatti.isOpen ? 'utility:chevrondown' : 'utility:chevronright';
    }*/

    //AC: proprietà che gestiscono la visibilità dei bottoni
    get btnInviaRichiesta() {
        return this.panel.summary && this.wait20Seconds && (this.data.statoValidazioneEmail === STATO_VALIDAZIONE_EMAIL_NO || this.data.statoValidazioneEmail === STATO_VALIDAZIONE_EMAIL_I);
    }

    get btnConferma() {
        return this.panel.edit && this.labelAnnulla === "Annulla";
    }

    get btnModifica() {
        return this.panel.detail;
    }

    get btnRevoca() {
        return this.panel.summary && this.companyForModaleFea != "Unisalute";
    }

    get btnRefreshStatoEmail() {
        return this.panel.summary && (this.data.statoValidazioneEmail === STATO_VALIDAZIONE_EMAIL_NO || this.data.statoValidazioneEmail === STATO_VALIDAZIONE_EMAIL_I);
    }

    //AC: proprietà che gestiscono la visibilità dei pannelli
    get pannelloDettaglio() {
        return this.panel.detail || this.panel.summary;
    }

    get pannelloModifica() {
        return this.panel.edit;
    }

    get pannelloRiepilogo() {
        return this.panel.summary;
    }

    //GP: mostra icone success o warning per cellulare ed email.
    get showIconSuccessCellulare() {
        return this.data.cellulare.trim() !== "-" && this.data.cellulare;
    }
    
    get showIconSuccess() {
        return this.data.adesione.toLowerCase() === "si" && this.panel.summary && this.data.statoValidazioneEmail === STATO_VALIDAZIONE_EMAIL_SI;
    }

    get showIconWarning() {
        return this.data.adesione.toLowerCase() === "si" && this.panel.summary && (this.data.statoValidazioneEmail === STATO_VALIDAZIONE_EMAIL_NO || this.data.statoValidazioneEmail === STATO_VALIDAZIONE_EMAIL_I);
    }

    get iconWarningClass() {
        return "icon-container icon-warning " + (this.wait20Seconds ? " cursor-pointer" : "")
    }

    
    formattaData(dataInput) {  

        if (!dataInput) {
            return '';
        }
        const data = new Date(dataInput);
        if (isNaN(data)) {
            return '';
        }

        const giorno = String(data.getDate()).padStart(2, '0');
        const mese = String(data.getMonth() + 1).padStart(2, '0');
        const anno = data.getFullYear();
        return `${giorno}/${mese}/${anno}`;
    }


    connectedCallback() {

        //EVENTO CHE RISALE DA FEICONTAINER
        window.addEventListener("message", evt => {
            console.log('EVENTOFEA --> ',evt);
            if(evt.data.type == "feiFlowFinished"){
                //this.togglePanel('summary');
                console.log('Evento FEI Container Intercettato');
                this.feiData.mostraFeiContainer = false;
                this.btnAggiorna = true;
                this.hideConfirmButton = true;
                this.labelAnnulla = 'Chiudi';
                console.log('Hide confirm Button: ', this.hideConfirmButton);
            }
        });

        console.log(this.feiData.feiId);
        //AC: come spiegato in testa, questo intervallo è necessario per verificare che gli attributi vengano passati dalla flex card.
        //    per evitare che il ciclo venga eseguito all'infinito in caso di mancata assegnazione di setInit, imposto un contatore.
        //    il contatore arriva fino a 20 (per un totale di 2 secondi), se non viene inizializzato, interrompe l'intervallo e mosta un messaggio di errore.
        let cnt = 0;
        const ci = setInterval(() => {
            if (this.setInit) {
                this.init();
                clearInterval(ci);
            } else {
                cnt++;
                if (cnt > 20) {
                    clearInterval(ci);
                    this.error("errore inzializzazione");
                }
            }
        }, 100);
    }

    error(err) {
        console.log(err);
        //this.errorInit = true;
    }

    init() {
        console.log(this.ciu, this.compagnia);
        this.refs.loader.showLoader();
        const adesioneSi = this.adesione.toLowerCase() === "si";
        const afterCheck = () => {
            if (adesioneSi) {
                this.getFea();
                // this.togglePanel('summary'); // Per testare la schermata summary scommenta
            } else {
                getContattiForm({
                    data: {
                        id: this.recordId
                    }
                }).then(response => {
                    const d = response; 
                    this.data = {
                        adesione: "No",
                        dataInizio: this.startDate,
                        cellulare: d.mobile ?? (d.otherMobile ?? ""),
                        email: d.email ?? (d.otherEmail ?? ""),
                        statoValidazioneEmail: STATO_VALIDAZIONE_EMAIL_NO
                    };
                    this.labelAnnulla = 'Chiudi';//1440059
                }).catch(err => {
                    this.error(err);
                }).finally(() => this.refs.loader.hideLoader());
            }
        };
    
        if (adesioneSi) {
            afterCheck();
            this.refs.loader.hideLoader();
        } else {
            checkDocumento({
                data: {
                    ciu: this.ciu,
                    accountId: this.recordId,
                    status: "true"
                }
            }).then(response => {
                if (!response.status) {
                    this.setConferma("Errore documenti", "Non hai un documento di identità caricato", "", "documentoAction", true, false);
                }
                afterCheck();
            }).catch(err => {
                this.error(err);
            }).finally(() => this.refs.loader.hideLoader());
        }
    }
        

    getFea() {
        this.refs.loader.showLoader();
        getFea({
            ipInput: {
                ciu: this.ciu,
                compagnia: this.compagnia,
                contactTypeFilter: "FEA"
            }
        }).then(response => {
            console.log(response);
            this.datiFea = response.returnList;
            const data = response.returnList;
            this.data.adesione = 'Si'; // Capire cosa mettere
            this.data.dataInizio = this.formattaData(data.dataInizioEffetto);
            this.data.cellulare = data.contattiFea[0].cellulare;
            this.data.email = data.contattiFea[0].email;
            this.data.statoValidazioneEmail = data.contattiFea[0].statoValidazioneMail;
            this.togglePanel('summary');
            this.labelAnnulla = 'Chiudi';
            setTimeout(() => this.wait20Seconds = true, 20000);
        }).catch(err => {
            this.error(err);
        }).finally(() => this.refs.loader.hideLoader());
    }

    documentoAction() {
        this.handleAnnulla();
    }

    //AC: azione di callback dopo la conferma invio adesione FEA
    confermaAction() {
        this.setDisabled(true);
        this.refs.loader.showLoader();
        conferma({
            data: {
                accountId: this.recordId,
                ciu: this.ciu,
                email: this.form.email,
                cellulare: this.form.cellulare,
                type: "FEA"
            }
        }).then(response => {
            /*if (response.status) {
                this.labelAnnulla = "Chiudi";
            } else {
                this.setDisabled(false);
            }*/
            if(response.status){
                this.feiData.payload = response.feiRequest;
                this.feiData.mostraFeiContainer = true;
                this.feiData.permissionSet = response.permissionSet;
                this.btnConferma = false; 
                this.btnAggiorna = true;
            }
            else 
                this.setConferma("Errore Univocità Contatti", response.error, "", "confermaAction", true, false);
        }).catch(err => {
            this.error(err);
        }).finally(() => this.refs.loader.hideLoader());
    }

    //AC: funzioni di callback dopo il conferma revoca FEA
    revocaAction() {
        this.refs.loader.showLoader();
        revoca({
            data: {
                ciu: this.datiFea.ciu,
                accountId: this.recordId,
                feaId: this.datiFea.id,
                compagnia: this.datiFea.compagnia,
                idContattoFea: this.datiFea.contattiFea[0].id, //Non è detto che si deve prendere lo 0
                email: this.datiFea.contattiFea[0].email, //Non è detto che si deve prendere lo 0
                cellulare: this.datiFea.contattiFea[0].cellulare, //Non è detto che si deve prendere lo 0
                statoValidazioneMail : this.datiFea.contattiFea[0].statoValidazioneMail //Non è detto che si deve prendere lo 0
            }
        }).then(response => {
            console.log('response: ' + JSON.stringify(response));
            console.log('response: ' + JSON.parse(JSON.stringify(response)));
            if(response.returnList.status != true){
                this.setConferma("Errore Revoca FEA", "Errore durante il processo di revoca FEA", "", "revocaAction", true, false);
            }
            else {
                // Messaggio di conferma in caso di successo
                const messaggioSuccesso = "La revoca della FEA è andata a buon fine.";
                this.setConferma("Revoca FEA completata", messaggioSuccesso, "", "", true, false);
            }
        }).catch(err => {
            this.error(err);
        }).finally(() => this.refs.loader.hideLoader());
    }

    setDisabled(status) {
        Object.keys(this.form.readonly).forEach(k => this.form.disabled[k] = status);
    }

    validazione() {
        const mobileRegex = /^(\+39\d{9,10}|\d{9,10})$/;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        this.errore.cellulareObbligatorio = this.form.cellulare === "";
        this.errore.formatoCellulare = this.form.cellulare !== "" && !mobileRegex.test(this.form.cellulare);
        this.errore.emailObbligatoria = this.form.email === "";
        this.errore.emailConfermaObbligatoria = this.form.emailConfirm === "";
        this.errore.formatoEmail = this.form.email !== "" && !emailRegex.test(this.form.email);
        this.errore.formatoConfermaEmail = this.form.emailConfirm !== "" && !emailRegex.test(this.form.emailConfirm);
        this.errore.emailDiverse = this.form.email !== this.form.emailConfirm;
        let ret = true;
        Object.keys(this.errore).forEach(k => ret = ret && !this.errore[k]);
        return ret;
    }

    //AC: imposta l'oggetto che gestisce la modale di conferma
    setConferma(titolo, testo, azione, azioneAnnulla, mostra, btnConfermaEsegui) {
        this.conferma = {titolo, testo, azione, azioneAnnulla, mostra};
        this.btnConfermaEsegui = btnConfermaEsegui === undefined && !this.btnAggiorna ? true : btnConfermaEsegui;
    }

    //AC: preleva l'id in base all'elemento html che viene cliccato
    getId(event) {
        return event.target.dataset.id ?? event.target.parentElement.dataset.id;
    }

    //AC: gestisce la visibilità dei pannelli
    togglePanel(panel) {
        Object.keys(this.panel).forEach(key => this.panel[key] = false);
        this.panel[panel] = true;
    }

    handleAggiorna() {
        this.refs.loader.showLoader();
        checkAggiorna({
            ipInput: {
                ciu: this.ciu,
                compagnia: this.compagnia
            }
        }).then(response => {
            //this.stampe.rows = response; //@GP Commentato
            if(response.status){
                this.errore.aggiornamentoFEA = false;
                this.togglePanel('summary');
                this.btnAggiorna = false;
                this.getFea();
                //setTimeout(() => this.wait20Seconds = true, 20000);
            }
            else{
                this.errore.aggiornamentoFEA = true;
            }
        }).catch(err => {
            this.error(err);
        }).finally(() => this.refs.loader.hideLoader());
    }

    //AC: chiusura della modale
    handleAnnulla() {
        pubsub.fire("close_modal", "close", null);
    }

    //AC: mostra la conferma della revoca
    handleRevoca() {
        this.setConferma("Revoca adesione FEA", "Desideri revocare l'adesione alla FEA?", "revocaAction", "", true);
    }

    //AC: mostra la conferma della FEA
    handleConferma() {
        if (this.validazione()) {
            this.setConferma("Conferma adesione FEA", "Desideri confermare l'adesione alla FEA?", "confermaAction", "", true);
        }
    }

    handleConfermaEsegui() {
        this[this.conferma.azione]();
        this.handleAnnullaEsegui();
    }

    handleAnnullaEsegui() {
        if (this.conferma.azioneAnnulla !== "") {
            this[this.conferma.azioneAnnulla]();
        }
        this.setConferma("", "", "", "", false);
    }

    handleInviaRichiesta() {
        this.refs.loader.showLoader();
        certifcazioneContatti({
            data: {
                ciu: this.ciu,
                email: this.data.email, //this.datiFea.contattiFea[0].email, //Non è detto che si deve prendere lo 0
                cellulare: this.data.cellulare //this.datiFea.contattiFea[0].cellulare, //Non è detto che si deve prendere lo 0
            }
        }).then(response => {
            console.log(response);
            this.wait20Seconds = false;
            setTimeout(() => this.wait20Seconds = true, 20000);
        }).catch(err => {
            this.error(err);
        }).finally(() => this.refs.loader.hideLoader());
    }

    handleModifica() {
        const dataInizio = new Date().toISOString().split("T")[0].split("-");
        this.form.dataInizio = `${dataInizio[2]}/${dataInizio[1]}/${dataInizio[0]}`;
        this.togglePanel("edit");
        this.refs.loader.showLoader();
        getContattiForm({
            data: {
                id: this.recordId
            }
        }).then(response => {
            const d = response; 
            this.form.cellulare = d.mobile ?? (d.otherMobile ?? "");
            this.form.email = d.email ?? (d.otherEmail ?? "");
            this.form.readonly.cellulare = d.mobile !== undefined && d.mobile !== "";
            this.form.readonly.email = d.email !== undefined && d.email !== "";
            this.form.emailConfirm = this.form.email;
            this.form.mostraEmailConfirm = d.email === undefined;
            this.labelAnnulla = 'Annulla';
            //this.form.mostraEmailConfirm = d.otherEmail ?? "";
        }).catch(err => {
            this.error(err);
        }).finally(() => {
            this.refs.loader.style.visibility = "hidden";
            this.refs.loader.style.display = "none";
        });
    }

    handleForm(event) {
        const id = this.getId(event);
        this.form[id] = event.target.value;
    }

    handleCopy(event) {
        event.preventDefault();
    }

    openScreenFlow(FEIID, recordId, feiRequestPayload) {
        this[NavigationMixin.Navigate]({
            type: 'standard__flow',
            attributes: {
                flowApiName: 'FEIQuickActionByAccount'
            },
            state: {
                FEIID, recordId, feiRequestPayload
            }
        }, true);
    }

    handleRefreshStatoEmail() {
        this.refs.loader.showLoader();
        refreshStatoEmail({
            ipInput: {
                ciu: this.ciu 
            }
        }).then(response => {
            this.data.statoValidazioneEmail = response.status ? STATO_VALIDAZIONE_EMAIL_SI : STATO_VALIDAZIONE_EMAIL_NO;
        }).catch(err => {
            this.error(err);
        }).finally(() => this.refs.loader.hideLoader());
    }

}