@isTest
public class PermissionSetAssignmentControllerTest {
    // Utility per creare un utente di test
    private static User createTestUser() {
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        User u = new User(
            Username = 'testuser' + DateTime.now().getTime() + '@test.com',
            LastName = 'Test',
            Email = '<EMAIL>',
            Alias = 'tuser',
            TimeZoneSidKey = 'Europe/Rome',
            LocaleSidKey = 'it_IT',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'it',
            ProfileId = p.Id
        );
        insert u;
        System.debug('Creato User di test: ' + u.Id);
        return u;
    }

    // Utility per creare un PermissionSet di test
    private static PermissionSet createTestPermissionSet(String name) {
        PermissionSet ps = new PermissionSet(Name = name, Label = name);
        insert ps;
        System.debug('Creato PermissionSet di test: ' + ps.Id);
        return ps;
    }

    @isTest
    static void testUpdateUserPersonas() {
        User u = createTestUser();
        Test.startTest();
        // Test: update con valore valido
        PermissionSetAssignmentController.updateUserPersonas(u.Id, 'Responsabile Totale');
        User updated = [SELECT Personas__c FROM User WHERE Id = :u.Id];
        System.assertEquals('Responsabile Totale', updated.Personas__c, 'Personas__c deve essere valorizzato');
        // Test: update con placeholder
        PermissionSetAssignmentController.updateUserPersonas(u.Id, '{Session.selectedProfile}');
        updated = [SELECT Personas__c FROM User WHERE Id = :u.Id];
        System.assertEquals('Responsabile Totale', updated.Personas__c, 'Personas__c non deve cambiare con placeholder');
        Test.stopTest();
    }

    @isTest
    static void testGetAssignedPermissionSetNames() {
        User u = createTestUser();
        PermissionSet ps1 = createTestPermissionSet('PS1');
        PermissionSet ps2 = createTestPermissionSet('PS2');
        PermissionSetAssignment psa1 = new PermissionSetAssignment(AssigneeId = u.Id, PermissionSetId = ps1.Id);
        PermissionSetAssignment psa2 = new PermissionSetAssignment(AssigneeId = u.Id, PermissionSetId = ps2.Id);
        insert new List<PermissionSetAssignment>{psa1, psa2};
        Test.startTest();
        List<String> names = PermissionSetAssignmentController.getAssignedPermissionSetNames(u.Id);
        Test.stopTest();
        System.assert(names.contains('PS1'), 'Deve contenere PS1');
        System.assert(names.contains('PS2'), 'Deve contenere PS2');
    }

    @isTest
    static void testRemovePermissionSetAssignments() {
        User u = createTestUser();
        PermissionSet ps1 = createTestPermissionSet('PS1');
        PermissionSet ps2 = createTestPermissionSet('PS2');
        PermissionSetAssignment psa1 = new PermissionSetAssignment(AssigneeId = u.Id, PermissionSetId = ps1.Id);
        PermissionSetAssignment psa2 = new PermissionSetAssignment(AssigneeId = u.Id, PermissionSetId = ps2.Id);
        insert new List<PermissionSetAssignment>{psa1, psa2};
        Test.startTest();
        PermissionSetAssignmentController.removePermissionSetAssignments(u.Id, new List<String>{'PS1'});
        Test.stopTest();
        List<PermissionSetAssignment> remaining = [SELECT PermissionSetId, permissionset.name FROM PermissionSetAssignment WHERE AssigneeId = :u.Id];
        
        System.assertEquals(2, remaining.size(), 'Deve rimanere 2 assignment di cui uno è quello di FESAULT che viene creato per lo user');
        system.debug('Remaining assignments: ' + remaining);
        // System.assertEquals(ps2.Id, remaining[0].PermissionSetId, 'Deve rimanere PS2');
    }

    @isTest
    static void testGetPermissionSetIdsByName() {
        PermissionSet ps1 = createTestPermissionSet('PS1');
        PermissionSet ps2 = createTestPermissionSet('PS2');
        Test.startTest();
        List<String> ids = PermissionSetAssignmentController.getPermissionSetIdsByName(new List<String>{'PS1', 'PS2'});
        Test.stopTest();
        System.assert(ids.contains(ps1.Id), 'Deve contenere l\'Id di PS1');
        System.assert(ids.contains(ps2.Id), 'Deve contenere l\'Id di PS2');
    }


    @isTest
    static void testSaveAssignments() {
        User u = createTestUser();
        PermissionSet ps1 = createTestPermissionSet('PS1');
        PermissionSet ps2 = createTestPermissionSet('PS2');
        PermissionSetAssignmentController.PermissionSetAssignmentWrapper w1 = new PermissionSetAssignmentController.PermissionSetAssignmentWrapper();
        w1.assigneeId = u.Id;
        w1.permissionSetId = ps1.Id;
        w1.checked = true;
        PermissionSetAssignmentController.PermissionSetAssignmentWrapper w2 = new PermissionSetAssignmentController.PermissionSetAssignmentWrapper();
        w2.assigneeId = u.Id;
        w2.permissionSetId = ps2.Id;
        w2.checked = false;
        List<PermissionSetAssignmentController.PermissionSetAssignmentWrapper> assignments = new List<PermissionSetAssignmentController.PermissionSetAssignmentWrapper>{w1, w2};
        Test.startTest();
        PermissionSetAssignmentController.saveAssignments(assignments);
        Test.stopTest();
        List<PermissionSetAssignment> result = [SELECT PermissionSetId FROM PermissionSetAssignment WHERE AssigneeId = :u.Id];
        System.assertEquals(2, result.size(), 'Deve essere assegnato PS1 + quello di default ');
        // System.assertEquals(ps1.Id, result[0].PermissionSetId, 'Deve essere assegnato PS1');
    }


    @isTest
    static void testSavePermissionSetAssignments() {
        User u = createTestUser();
        PermissionSet ps1 = createTestPermissionSet('PS1');
        PermissionSet ps2 = createTestPermissionSet('PS2');
        Test.startTest();
        PermissionSetAssignmentController.savePermissionSetAssignments(u.Id, new List<Id>{ps1.Id, ps2.Id});
        Test.stopTest();
        List<PermissionSetAssignment> result = [SELECT PermissionSetId FROM PermissionSetAssignment WHERE AssigneeId = :u.Id];
        System.assertEquals(3, result.size(), 'Devono essere assegnati entrambi i PermissionSet + il default');
    }
}
