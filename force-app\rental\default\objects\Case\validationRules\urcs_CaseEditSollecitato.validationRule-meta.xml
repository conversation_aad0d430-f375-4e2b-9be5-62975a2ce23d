<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>urcs_CaseEditSollecitato</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
	$Setup.urcs_GeneralSettings__c.SkipValidationRule__c == false,
    NOT(ISNEW()),
    ISCHANGED(Sollecitato__c),
    OR(
        AND(
            OR(
                RecordType.DeveloperName == &quot;ur_CaseCRM&quot;,
                RecordType.DeveloperName == &quot;ur_CasePQ&quot;,
                RecordType.DeveloperName == &quot;ur_CaseSitoWeb&quot;,
                RecordType.DeveloperName == &quot;ur_CaseAR&quot;,
                RecordType.DeveloperName == &quot;ur_CaseES&quot;
    ),
    OR(
        $User.Id != UtAssegnatario__c,
        IsClosed = true
    )
        )
    )
) </errorConditionFormula>
    <errorDisplayField>Sollecitato__c</errorDisplayField>
    <errorMessage>Non è possibile modificare il valore di questo campo: Sollecitato</errorMessage>
</ValidationRule>
