@IsTest
public class CustomerBuildTest {
    
    @IsTest
    static void testBuildOpportunitiesData() {
        Account acc = new Account(Name='Test Account');
        insert acc;

        Opportunity opp = new Opportunity(
            Name = 'Test Opp',
            StageName = 'Prospecting',
            CloseDate = Date.today(),
            AccountId = acc.Id
        );
        insert opp;

        Quote q = new Quote(
            Name='Test Quote',
            OpportunityId=opp.Id
        );
        insert q;

        OpportunityCoverage__c cov = new OpportunityCoverage__c(
            Name='Test Coverage',
            Quote__c=q.Id
        );
        insert cov;

        // call with full data
        List<CustomerWrapper.OpportunityData> data = CustomerBuild.buildOpportunitiesData(
            new List<Opportunity>{opp},
            new List<Quote>{q},
            new List<OpportunityCoverage__c>{cov}
        );

        // call with null values to cover null branches
        List<CustomerWrapper.OpportunityData> data2 = CustomerBuild.buildOpportunitiesData(
            null, null, null
        );
    }

    @IsTest
    static void testBuildAccountFinancialData() {
        // Mock record types
        RecordType rtSociety = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType='FinServ__AccountAccountRelation__c' LIMIT 1];
        RecordType rtAgency = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType='FinServ__AccountAccountRelation__c' LIMIT 1];
        
        Account acc = new Account(Name='Test Account');
        insert acc;
        
        FinServ__ReciprocalRole__c role1 = new FinServ__ReciprocalRole__c();
        role1.Name = 'TEST';
        role1.FinServ__RelationshipType__c = 'All';
        role1.FinServ__InverseRole__c = 'TEST2';
        insert role1;

        FinServ__AccountAccountRelation__c relSociety = new FinServ__AccountAccountRelation__c(
            RecordTypeId=rtSociety.Id,
            FinServ__ExternalId__c='X_SOC_123',
            FinServ__Account__c = acc.Id,
            FinServ__Role__c = role1.Id
        );
        
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c();
        role.Name = 'TEST2';
        role.FinServ__RelationshipType__c = 'All';
        role.FinServ__InverseRole__c = 'TEST';
        role.FinServ__InverseRelationship__c = role1.Id;
        insert role;
        
        role1.FinServ__InverseRelationship__c = role.Id;
        update role1;

        FinServ__AccountAccountRelation__c relAgency = new FinServ__AccountAccountRelation__c(
            RecordTypeId=rtAgency.Id,
            FinServ__ExternalId__c='X_AGY_456',
            FinServ__Account__c = acc.Id,
            FinServ__Role__c = role.Id
        );

        insert new List<FinServ__AccountAccountRelation__c>{relSociety, relAgency};

        AccountDetails__c det = new AccountDetails__c(
            Name='Detail',
            Relation__c=relSociety.Id
        );

        AccountDetailsNPI__c detNpi = new AccountDetailsNPI__c(
            Name='DetailNPI',
            Relation__c=relSociety.Id
        );

        insert new List<SObject>{det, detNpi};

        CustomerWrapper.AccountFinancialData result = CustomerBuild.buildAccountFinancialData(
            new List<FinServ__AccountAccountRelation__c>{relSociety, relAgency},
            new List<AccountDetails__c>{det},
            new List<AccountDetailsNPI__c>{detNpi}
        );

        // null input coverage
        CustomerWrapper.AccountFinancialData result2 = CustomerBuild.buildAccountFinancialData(
            null, null, null
        );
    }

    @IsTest
    static void testBuildAssetsData() {
        Asset a1 = new Asset(Name='Asset1', ExternalId__c='X_SOC_123_CODE');
        Asset a2 = new Asset(Name='Asset2', ExternalId__c='X_AGY_456_CODE');
        Asset a3 = new Asset(Name='Asset3'); // no external id

        insert new List<Asset>{a1, a2, a3};

        List<CustomerWrapper.AssetData> res = CustomerBuild.buildAssetsData(new List<Asset>{a1, a2, a3});

        // null coverage
        List<CustomerWrapper.AssetData> res2 = CustomerBuild.buildAssetsData(null);
    }
}