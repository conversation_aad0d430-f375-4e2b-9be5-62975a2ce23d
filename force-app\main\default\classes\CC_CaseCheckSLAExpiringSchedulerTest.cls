@isTest
private class CC_CaseCheckSLAExpiringSchedulerTest {
    // Tries to resolve the Case RecordType 'CC_Contact_Center'; returns null if missing
    private static Id findCCRecordTypeIdIfAny() {
        List<RecordType> recordTypes = [SELECT Id FROM RecordType WHERE SobjectType = 'Case' AND DeveloperName = 'CC_Contact_Center' LIMIT 1];
        return recordTypes.isEmpty() ? null : recordTypes[0].Id;
    }

    @isTest
    static void testSchedulerRunsBatch() {
        Date target = CC_CaseCheckSLAExpiring.computeNextBusinessDate(Date.today());
        Id contactCenterRecordTypeId = findCCRecordTypeIdIfAny();
        Case caseRecord = new Case(
            Subject = 'Scheduled Run',
            Origin = 'Web',
            Status = 'New',
            Expiration_Date__c = target,
            SLA_Expiration_Notice__c = false
        );
        if (contactCenterRecordTypeId != null) caseRecord.RecordTypeId = contactCenterRecordTypeId;
        insert caseRecord;

        Test.startTest();
        // Invoke scheduler execute directly; batch will run on Test.stopTest()
        new CC_CaseCheckSLAExpiringScheduler().execute(null);
        Test.stopTest();

    }
}