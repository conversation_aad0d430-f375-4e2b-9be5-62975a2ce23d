import { LightningElement, wire, api, track } from 'lwc';
import { NavigationMixin, CurrentPageReference } from 'lightning/navigation';
import { IsConsoleNavigation, openSubtab, getFocusedTabInfo, setTabIcon, getTabInfo, EnclosingTabId} from 'lightning/platformWorkspaceApi';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import Id from '@salesforce/user/Id';
import getRelatedOpportunities from '@salesforce/apex/OpportunityRelatedListController.getRelatedOpportunities';
import getAccountInfo from '@salesforce/apex/OpportunityRelatedListController.getAccountInfo';
import getUserInfo from '@salesforce/apex/OpportunityRelatedListController.getUserInfo';
import createOpportunity from '@salesforce/apex/OpportunityRelatedListControllerNS.createOpportunity';
import updateOpportunity from '@salesforce/apex/OpportunityRelatedListControllerNS.updateOpportunity';
import getTempiLavorazione from '@salesforce/apex/OpportunityRelatedListControllerNS.getTempiLavorazione';
import { registerRefreshContainer, REFRESH_ERROR, REFRESH_COMPLETE, REFRESH_COMPLETE_WITH_ERRORS } from 'lightning/refresh';

export default class OpportunityRelatedList extends NavigationMixin(LightningElement) {

    @wire(CurrentPageReference) currentPageRef;
    @wire(IsConsoleNavigation) IsConsoleNavigation;
    @wire(EnclosingTabId) tabId;
    parentTabId;

    @api recordId;
    @api columns;
    @api stage;
    userId = Id;
    account;
    userInfo = {};
    idAzienda = '';

    newOpportunityId;

    giorniLavorazione;

    isViewAll = false;
    isViewMore = false;
    isModalOpen = false;
    isFlowVisible = false;

    recordsToShow = 3;

    stageNames = ['Nuovo', 'Assegnato', 'In gestione'];

    showOtherTrattative = 'Mostra Trattative Chiuse';
    showTable = false;
    trattativeTitle = 'Trattative Aperte';
    filterNavigationName = 'Trattative Aperte';

    trattativeNumber = 0;
    noRecords = true;

    get columns() {
        const columnsAperte = [
            { label: 'Nome Trattativa', fieldName: 'nameUrl', type: 'url', typeAttributes: { label: { fieldName: 'Name' }, target: '_self' } },
            { label: 'Premio', fieldName: 'Amount', type: 'currency', typeAttributes: { currencyCode: 'EUR' }, cellAttributes: { alignment: 'left' } },
            { label: 'Canale Origine', fieldName: 'Channel__c' },
            { label: 'Ambiti di Protezione', fieldName: 'AreasImage', type: 'richText' },
            { label: 'Data Scadenza', fieldName: 'DueDateFormula__c', type: 'date-local' },
            { label: 'Assegnatario', fieldName: 'Assignee__c'}
        ];

        const columnsChiuse = [
            { label: 'Nome Trattativa', fieldName: 'nameUrl', type: 'url', typeAttributes: { label: { fieldName: 'Name' }, target: '_self' } },
            { label: 'Premio', fieldName: 'Amount', type: 'currency', typeAttributes: { currencyCode: 'EUR' }, cellAttributes: { alignment: 'left' }  },
            { label: 'Canale Origine', fieldName: 'Channel__c' },
            { label: 'Ambiti di Protezione', fieldName: 'AreasImage', type: 'richText' },
            { label: 'Data Chiusura', fieldName: 'ClosingDate__c', type: 'date-local' },
            { label: 'Assegnatario', fieldName: 'Assignee__c'}
        ];

        const viewAllColumnsAperte = [
            { label: 'Data Scadenza', fieldName: 'DueDateFormula__c', type: 'date', typeAttributes:{ year: "numeric", month: "numeric", day: "2-digit", hour: "2-digit", minute: "2-digit"} },
            { label: 'Temperatura', fieldName: 'TemperatureFormula__c',type: 'richText'},
            { label: 'Data creazione', fieldName: 'CreatedDate', type: 'date', typeAttributes:{ year: "numeric", month: "numeric", day: "2-digit", hour: "2-digit", minute: "2-digit"} },
            { label: 'Stato', fieldName: 'StageName' },
            { label: 'Nome Trattativa', fieldName: 'nameUrl', type: 'url', typeAttributes: { label: { fieldName: 'Name' }, target: '_self' } },
            { label: 'Premio', fieldName: 'Amount', type: 'currency', typeAttributes: { currencyCode: 'EUR' }, cellAttributes: { alignment: 'left' } },
            { label: 'Canale Origine', fieldName: 'Channel__c' },
            { label: 'Prodotto', fieldName: 'Products__c' },
            { label: 'Tipologia soggetto Unipol', fieldName: 'Tipologia_soggetto_Unipol__c'},
            { label: 'Soggetto', fieldName: 'AccountHyperlink__c', type: 'richText'},
            { label: 'Step digitale', fieldName: 'JourneyStep__c'},
            { label: 'Call Me Back', fieldName: 'HasCallMeBackFormula__c', type: 'richText'},
            { label: 'Ambiti di Protezione', fieldName: 'AreasImage', type: 'richText' },
            { label: 'Punto Vendita', fieldName: 'SalespointName__c' },
            { label: 'Utente Assegnatario', fieldName: 'Assignee__c'}
        ];

        const viewAllColumnsChiuse = [
            { label: 'Data Scadenza', fieldName: 'DueDateFormula__c', type: 'date', typeAttributes:{ year: "numeric", month: "numeric", day: "2-digit", hour: "2-digit", minute: "2-digit"} },
            { label: 'Data creazione', fieldName: 'CreatedDate', type: 'date', typeAttributes:{ year: "numeric", month: "numeric", day: "2-digit", hour: "2-digit", minute: "2-digit"} },
            { label: 'Stato', fieldName: 'StageName' },
            { label: 'Nome Trattativa', fieldName: 'nameUrl', type: 'url', typeAttributes: { label: { fieldName: 'Name' }, target: '_self' } },
            { label: 'Premio', fieldName: 'Amount', type: 'currency', typeAttributes: { currencyCode: 'EUR' }, cellAttributes: { alignment: 'left' } },
            { label: 'Canale Origine', fieldName: 'Channel__c' },
            { label: 'Prodotto', fieldName: 'Products__c' },
            { label: 'Tipologia soggetto Unipol', fieldName: 'Tipologia_soggetto_Unipol__c'},
            { label: 'Soggetto', fieldName: 'AccountHyperlink__c', type: 'richText'},
            { label: 'Ambiti di Protezione', fieldName: 'AreasImage', type: 'richText' },
            { label: 'Punto Vendita', fieldName: 'puntoVenditaUrl', type: 'url', typeAttributes: { label:  { fieldName: 'SalespointName__c' }, target: '_self' }},
            { label: 'Utente Assegnatario', fieldName: 'Assignee__c'},
            { label: 'Canale di contatto', fieldName: 'ContactChannel__c'},
            { label: 'Data Chiusura', fieldName: 'ClosingDate__c', type: 'date', typeAttributes:{ year: "numeric", month: "numeric", day: "2-digit", hour: "2-digit", minute: "2-digit"} },
            { label: 'Canale di Vendita', fieldName: 'PolicyChannel__c' }
        ];

        if (this.isViewAll) {
            
            return this.stageNames.includes('Chiuso') ? viewAllColumnsChiuse : viewAllColumnsAperte;
        } else {

            return this.stageNames.includes('Chiuso') ? columnsChiuse : columnsAperte;
        }
            /*
            ? [...(isClosed ? columnsChiuse : columnsAperte), ...extraColumns]
            : (isClosed ? columnsChiuse : columnsAperte);
            */
    }


    records;

    //Form Values
    closeDate;
    minDate;
    confermaDisabled = false;

    //Flow inputs
    flowInputs = [];

    connectedCallback(){

        console.log('pageRef: ' + JSON.stringify(this.currentPageRef));
        console.log('recordID: ' + this.recordId);

        if (this.stage == 'Aperte') {
            
            this.showNuovaTrattativa = true;
            this.stageNames = ['Nuovo', 'Assegnato', 'In gestione'];

        } else if (this.stage == 'Chiuse') {
            
            this.showNuovaTrattativa = false;
            this.stageNames = ['Chiuso'];
        }

        if (this.recordId === undefined && this.currentPageRef?.state?.c__recordId !== undefined) {

            this.recordId = this.currentPageRef.state.c__recordId;
        }

        if (this.currentPageRef?.state?.c__stageNames !== undefined) {

            this.stageNames = this.currentPageRef.state.c__stageNames;
        }

        if (this.currentPageRef?.state?.c__isViewAll !== undefined) {

            this.isViewAll = this.currentPageRef.state.c__isViewAll;

        }

        if (this.isViewAll == true){
            this.recordsToShow = 20;
        }

        if (this.stageNames.includes('Chiuso')){

            this.trattativeTitle = 'Trattative Chiuse';
            this.showOtherTrattative = 'Mostra Trattative Aperte';
        }

        this.getAccountInfo();
        this.getUserInfo();
        this.setDefaultDate();

        this.showTable = false;
        this.getOpportunities();
        this.refreshContainerID = registerRefreshContainer(this, this.refreshContainer);
    }

    getOpportunities(){
        console.log('accountId: ' + this.recordId + 'stageNames: ' + this.stageNames + 'isViewAll: ' + this.isViewAll);
        getRelatedOpportunities({accountId: this.recordId, stageNames: this.stageNames})
        .then(result=>{

            this.records = result;
            this.trattativeNumber = this.records.length? this.records.length : 0;
            console.log('records.length: ' + this.records.length);
            console.log('records pre ' + JSON.stringify(this.records));

            if(this.records.length > 0){

                this.showNuovaTrattativa = false;

                if (this.isViewAll == true) {
                    
                    if(this.records.length <= this.recordsToShow){
                        this.isViewMore = false;    
                    } else {
                        this.isViewMore = true;
                    }
                } else {

                    this.noRecords = false;
                }

                if(this.records.length > this.recordsToShow){
                    this.records = this.records.slice(0, this.recordsToShow);
                }
                
                this.records = this.records.map(item =>
                    ({...item,
                        AreasImage: item.AreasOfNeedFormula__c? '<div>' + item.AreasOfNeedFormula__c + '</div>' : '',
                        nameUrl: '/' + item.Id,
                        userUrl: item.AssignedTo__c? '/' + item.AssignedTo__c : '',
                        userName: item.AssignedTo__r?.Name? item.AssignedTo__r.Name : '',
                        puntoVenditaUrl: item.Salespoint__c? '/' + item.Salespoint__c : '',
                        PolicyChannel__c: item.PolicyChannel__c? this.capitalizeFirstLetter(item.PolicyChannel__c): ''
                    })
                )

                console.log('records: '+JSON.stringify(this.records));
                console.log('data.length post slice: ' + this.records.length);
                
                this.showTable = true;
                
            } else {

            }
        
        }).catch(error=>{
            console.log(JSON.stringify(error));
        });
    }

    getAccountInfo(){
        getAccountInfo({accountId: this.recordId}).then(result=>{
            this.account = result;
        })
    }

    getUserInfo(){
        getUserInfo({userId: this.userId})
            .then(result=>{
            this.userInfo = result;

            if(this.userInfo.IdAzienda__c !== undefined){

                this.idAzienda = this.userInfo.IdAzienda__c;
            }

            console.log('idAzienda: ' + this.idAzienda);
        })
    }

    capitalizeFirstLetter(text){
        return String(text).toLowerCase().charAt(0).toUpperCase() + String(text).toLowerCase().slice(1);
    }

    async clickViewAll(){
        console.log('viewAll');

        if(!this.IsConsoleNavigation){
            console.log('check console false');
            return;
        }

        if (!this.tabId) {
            return;
        }

        console.log('tab id: ' + this.tabId );

        const tabInfo = await getTabInfo(this.tabId);
        const primaryTabId = tabInfo.isSubtab ? tabInfo.parentTabId : tabInfo.tabId;
        console.log('TABINFO: ' + JSON.stringify(tabInfo));

        try{
            await openSubtab( 
                primaryTabId, {
                    pageReference: {
                        type: "standard__component",
                        attributes: {
                            componentName: "c__opportunityRelatedList",
                        },
                        state: {
                            c__recordId: this.recordId,
                            c__stageNames: this.stageNames,
                            c__isViewAll: true
                        }   
                    },
                    focus: true,
                    icon: "standard:opportunity",
                    label: this.stageNames.includes('Chiuso') ? "Trattative Chiuse" : "Trattative Aperte",
                }
            );
        } catch (error) {
            console.log(JSON.stringify(error))
        }
        /*

        try{
            await openTab({
                pageReference: {
                    type: "standard__component",
                    attributes: {
                        componentName: "c__opportunityRelatedList",
                    },
                    state: {
                        c__recordId: this.recordId,
                        c__stageNames: this.stageNames,
                        c__isViewAll: true
                    }   
                },
                icon: "standard:opportunity",
                label: "Tutte le Trattative",
            });
        } catch (error) {
            console.log(JSON.stringify(error))
        }

        

        //this.stageNames = this.stage == 'Aperte' ? ['Nuovo', 'Assegnato', 'In gestione'] : ['Chiuso'];


        this[NavigationMixin.Navigate]({
            type: 'standard__component',
            attributes: {
                componentName: "c__opportunityRelatedList"
            },
            state: {
                c__recordId: this.recordId,
                c__stageNames: this.stageNames,
                c__isViewAll: true
            }
        });

        
        */
        
    }

    clickLoadMore(){

        console.log('MORE');

        this.recordsToShow += 20;
        this.getOpportunities();
    }

    /*
    async setTabIcon() {
        if (!this.isConsoleNavigation) {
            return;
        }
        console.log('setTabIcon');

        try {

            const { tabId } = await getFocusedTabInfo();
            console.log(JSON.stringify(tabId));
            setTabIcon(tabId, 'standard:case', {
                iconAlt: 'Tutte le Attività'
            });

        } catch (error) {
            console.log(JSON.stringify(error));
            // handle error))
        }
        
    }
    */

    createOpportunity(){
        this.confermaDisabled = true;
        createOpportunity({ accountId: this.recordId, closeDate: this.closeDate, /*giorniLavorazione: this.giorniLavorazione,  */idAgenzia: this.idAzienda})
            .then(result => {

                console.log('oppo id: ' + result);

                this.newOpportunityId = result;
                this.isModalOpen = false;

                this.isFlowVisible = true;
                this.flowInputs = [
                    {name: 'opportunityId', type: 'String', value: this.newOpportunityId}
                ];
                this.newOppFlow = true;

            })
            .catch(error => {
                this.confermaDisabled = false;
                console.log(JSON.stringify(error));
            });
    }
            
    
    setDefaultDate() {
        const today = new Date();
        //today.setFullYear(today.getFullYear() + 1);
        this.minDate = today.toISOString().split('T')[0];
        this.closeDate = today.toISOString().split('T')[0];
    }

    handleCloseDateChange(event){

        this.closeDate = event.target.value;
        if (this.closeDate < this.minDate) {
            this.confermaDisabled = true;
        } else {
            this.confermaDisabled = false;
        }
    }
    
    openModal() {
        console.log('openModal');
        this.isModalOpen = true;

        console.log('id azienda:' + this.idAzienda);
        
        /*getTempiLavorazione({accountId: this.recordId, idAzienda: this.idAzienda})
        .then(result => {
            
            this.giorniLavorazione = result;
            console.log('giorniLavorazione: ' + JSON.stringify(this.giorniLavorazione));

        })
        .catch(error => {
            console.log('error2: ' + JSON.stringify(error));
        });*/

    }
    
    closeModal() {
        this.isModalOpen = false;
    }
    
    handleFlowStatusChange(event){

        console.log('status change: ' + JSON.stringify(event.detail.outputVariables));

        if(event.detail.status === 'FINISHED_SCREEN'){

            console.log('flow finished');
            const outputVariables = event.detail.outputVariables;

            outputVariables.forEach(element => {
                
                console.log(JSON.stringify(element));
                if(element.name = 'outputName'){
                    
                    updateOpportunity({opportunityId: this.newOpportunityId, newName: element.value})
                    .then(result => {

                        this.dispatchEvent(
                            new ShowToastEvent({
                                title: 'Successo',
                                message: `Opportunità creata con ID: ${this.newOpportunityId}`,
                                variant: 'success'
                            })
                        );

                        console.log('opportunity updated');
                        this.newOppFlow = false;
                        this.isFlowVisible = false;

                        setTimeout(() => {
                            this[NavigationMixin.Navigate]({
                                type: 'standard__recordPage',
                                attributes: {
                                    recordId: this.newOpportunityId,
                                    objectApiName: 'Opportunity',
                                    actionName: 'view'
                                },
                            });
                        }, 1000);

                        this.getOpportunities();

                    })
                    .catch(error => {
                        console.log('error:' + JSON.stringify(error));
                    })
                }
            });
        }

    }

    refreshContainer(refreshPromise) {
        console.log("refreshing of opportunityRelatedList");
        console.log('pageRef: ' + JSON.stringify(this.currentPageRef));
        console.log('recordID: ' + this.recordId);

        if (this.stage == 'Aperte') {
            
            this.showNuovaTrattativa = true;
            this.stageNames = ['Nuovo', 'Assegnato', 'In gestione'];

        } else if (this.stage == 'Chiuse') {
            
            this.showNuovaTrattativa = false;
            this.stageNames = ['Chiuso'];
        }

        if (this.recordId === undefined && this.currentPageRef?.state?.c__recordId !== undefined) {

            this.recordId = this.currentPageRef.state.c__recordId;
        }

        if (this.currentPageRef?.state?.c__stageNames !== undefined) {

            this.stageNames = this.currentPageRef.state.c__stageNames;
        }

        if (this.currentPageRef?.state?.c__isViewAll !== undefined) {

            this.isViewAll = this.currentPageRef.state.c__isViewAll;

        }

        if (this.isViewAll == true){
            this.recordsToShow = 20;
        }

        if (this.stageNames.includes('Chiuso')){

            this.trattativeTitle = 'Trattative Chiuse';
            this.showOtherTrattative = 'Mostra Trattative Aperte';
        }

        this.getAccountInfo();
        this.getUserInfo();
        this.setDefaultDate();

        this.showTable = false;
        this.getOpportunities();
        return refreshPromise.then((status) => {
            if (status === REFRESH_COMPLETE) {
                console.log("refresh of opportunityRelatedList Done!");
            } else if (status === REFRESH_COMPLETE_WITH_ERRORS) {
                console.warn("refresh of opportunityRelatedList Done, with issues refreshing some components");
            } else if (status === REFRESH_ERROR) {
                console.error("refresh of opportunityRelatedList Major error with refresh.");
            }
        });
    }
}