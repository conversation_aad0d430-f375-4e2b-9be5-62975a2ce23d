<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <description><PERSON><PERSON><PERSON>, <PERSON>: rimosse vecchie versioni</description>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;KPI&quot; : {
    &quot;AccountKeyAgency&quot; : {
      &quot;&quot; : &quot;&quot;,
      &quot;PP_AGENZIA_STATO&quot; : {
        &quot;Value&quot; : &quot;Cliente&quot;
      },
      &quot;PP_AGENZIA_CLIENTE_DAL&quot; : {
        &quot;Value&quot; : &quot;22-04-2024&quot;
      },
      &quot;PP_AGENZIA_SOCIETA_STATO&quot; : {
        &quot;Value&quot; : &quot;Cliente&quot;
      },
      &quot;PP_AGENZIA_SOCIETA_CLIENTE_DAL&quot; : {
        &quot;Value&quot; : &quot;12-10-2022&quot;
      },
      &quot;PP_CONVENZIONI&quot; : {
        &quot;Value&quot; : &quot;Si&quot;
      },
      &quot;CRM_CLIENTE_VIP&quot; : {
        &quot;Value&quot; : &quot;Si&quot;
      },
      &quot;ConditionalBlock3Status&quot; : true,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;LoopBlockIterationIndex&quot; : 6
    },
    &quot;AccountKey&quot; : {
      &quot;&quot; : &quot;&quot;,
      &quot;CRMA_VALUS&quot; : null,
      &quot;MDM_CONTATTABILITA&quot; : null,
      &quot;TPD_DATAULTIMOACCESSO&quot; : null,
      &quot;CRMA_CAPACITADISPESA&quot; : {
        &quot;Value&quot; : &quot;Alta&quot;
      },
      &quot;PP_TITOLARITACLIENTE&quot; : null,
      &quot;MEDAGLIA_VOC&quot; : null,
      &quot;TPD_ACCESSO&quot; : null,
      &quot;ANAG2_STATOCIVILE&quot; : null,
      &quot;ANAG2_FLAGFIGLI&quot; : null,
      &quot;ANAG2_TITOLODISTUDIO&quot; : null,
      &quot;ANAG2_TIPORISPARMIO&quot; : null,
      &quot;ANAG2_FLAGCONIUGECARICO&quot; : null,
      &quot;ANAG2_NUMEROFIGLIOCARICO&quot; : null,
      &quot;ANAG2_REDDITOLAVORO&quot; : null,
      &quot;ANAG2_EVOLUZIONEREDDITOLAVORO&quot; : null,
      &quot;ANAG2_EVOLUZIONEALTRIREDDITI&quot; : null,
      &quot;ANAG2_SPESEINCOMPRIMIBILI&quot; : null,
      &quot;ANAG2_SPESECOMPRIMIBILI&quot; : null,
      &quot;ANAG2_DATANASCITACONIUGE&quot; : null,
      &quot;ANAG2_NUMERONUCLEOFAMIGLIA&quot; : null,
      &quot;ANAG2_FLAGANIMALIDOMESTICI&quot; : null,
      &quot;ANAG2_FLAGCOLLABORATORIDOMESTICI&quot; : null,
      &quot;ANAG2_FLAGCASAPROPRIETA&quot; : null,
      &quot;ANAG2_FLAGCASAAFFITTO&quot; : null,
      &quot;ANAG2_FLAGSECONDACASA&quot; : null,
      &quot;ANAG2_FLAGMUTUO&quot; : null,
      &quot;CRIBIS_AZIENDAINUTILE_ULTIMOANNO&quot; : null,
      &quot;CRIBIS_AZIENDAINUTILE_ULTIMI3ANNI&quot; : null,
      &quot;CRIBIS_NDIPENDENTIPERINQUADRAMENTO&quot; : null,
      &quot;CRIBIS_DETTAGLIOTFR&quot; : null,
      &quot;ConditionalBlock2Status&quot; : true,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;LoopBlockIterationIndex&quot; : 5
    }
  },
  &quot;Value&quot; : {
    &quot;account_Name&quot; : &quot;Beatrice Rizzo&quot;,
    &quot;accountAgencyDetail_AdesioneCauzione&quot; : &quot;No&quot;,
    &quot;accountAccountRelationSociey_Id&quot; : &quot;a009X00000RHhdtQAD&quot;,
    &quot;account_LastName&quot; : &quot;Rizzo&quot;,
    &quot;accountDetailPrivateArea_Email&quot; : &quot;<EMAIL>&quot;,
    &quot;account_FirstName&quot; : &quot;Beatrice&quot;,
    &quot;account_Age&quot; : &quot;46&quot;,
    &quot;accountDetailMDM_Cellulare&quot; : &quot;+39 328 9873451&quot;,
    &quot;accountAgencyDetail__DataCreazione&quot; : &quot;01/17/2024&quot;,
    &quot;accountDetailPrivateArea_Cellulare&quot; : &quot;340 1134567&quot;,
    &quot;accountDetailMDM_FonteCellulare&quot; : &quot;Work&quot;,
    &quot;accountDetail_Occupation&quot; : &quot;Insegnante&quot;,
    &quot;accountDetailMDM_FonteEmail&quot; : &quot;Other&quot;,
    &quot;accountAccountRelationAgency_Id&quot; : &quot;a009X00000RHUgaQAH&quot;,
    &quot;account_ExternalId&quot; : &quot;****************&quot;,
    &quot;accountAgencyDetail_Cellulare&quot; : &quot;+39 **********&quot;,
    &quot;accountDetailMDM_Email&quot; : &quot;<EMAIL>&quot;,
    &quot;accountAgencyDetail_Email&quot; : &quot;<EMAIL>&quot;,
    &quot;accountDetail_Residenza&quot; : &quot;Via Carlo Farini, Milano, Italy&quot;,
    &quot;accountAgencyDetail_CellularePreferred&quot; : &quot;Boolean&quot;,
    &quot;accountAgencyDetail_CellulareUsage&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_CellulareId&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_EmailPreferred&quot; : &quot;Boolean&quot;,
    &quot;accountAgencyDetail_EmailUsage&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_EmailId&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_PEC&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_PECPreferred&quot; : &quot;Boolean&quot;,
    &quot;accountAgencyDetail_PECUsage&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_PECId&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_Fax&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_FaxPreferred&quot; : &quot;Boolean&quot;,
    &quot;accountAgencyDetail_FaxUsage&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_FaxId&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_Telefono&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_TelefonoPreferred&quot; : &quot;Boolean&quot;,
    &quot;accountAgencyDetail_TelefonoUsage&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_TelefonoId&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_TelefonoReferente&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_TelefonoReferentePreferred&quot; : &quot;Boolean&quot;,
    &quot;accountAgencyDetail_TelefonoReferenteUsage&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_TelefonoReferenteId&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_Referente&quot; : &quot;Text&quot;
  }
}</expectedInputJson>
    <expectedOutputJson>{
  &quot;datiAnagrafici&quot; : {
    &quot;Cognome&quot; : &quot;Text&quot;,
    &quot;modifica&quot; : {
      &quot;anagrafica&quot; : {
        &quot;codiceFiscale&quot; : &quot;Text&quot;,
        &quot;nome&quot; : &quot;Text&quot;,
        &quot;cognome&quot; : &quot;Text&quot;,
        &quot;ciu&quot; : &quot;Text&quot;,
        &quot;compagnia&quot; : &quot;Text&quot;,
        &quot;agenzia&quot; : &quot;Text&quot;,
        &quot;tipoAnagrafica&quot; : &quot;Text&quot;,
        &quot;tipoSoggetto&quot; : &quot;Text&quot;,
        &quot;statoCodiceFiscale&quot; : &quot;Text&quot;,
        &quot;sesso&quot; : &quot;Text&quot;,
        &quot;dataNascita&quot; : &quot;Text&quot;,
        &quot;dataDecesso&quot; : { },
        &quot;tipoAnagraficaRichiesto&quot; : &quot;Text&quot;,
        &quot;statoCodiceDiscalePartitaIva&quot; : &quot;Text&quot;,
        &quot;comuneNascitaBelfiore&quot; : &quot;Text&quot;,
        &quot;statoAnagrafica&quot; : &quot;Text&quot;,
        &quot;nazioneNascita&quot; : &quot;Text&quot;,
        &quot;comuneNascita&quot; : &quot;Text&quot;,
        &quot;provinciaNascita&quot; : &quot;Text&quot;,
        &quot;statoCodiceFiscalePartitaIva&quot; : &quot;Text&quot;
      },
      &quot;Residenza&quot; : &quot;Text&quot;,
      &quot;Professione&quot; : &quot;Text&quot;,
      &quot;Propaga&quot; : true,
      &quot;DisableCompany&quot; : true,
      &quot;indirizzi&quot; : { },
      &quot;contatti&quot; : [ { } ],
      &quot;professione&quot; : &quot;Text&quot;,
      &quot;privacy&quot; : { }
    },
    &quot;Eta&quot; : &quot;Text&quot;,
    &quot;Professione&quot; : &quot;Text&quot;,
    &quot;Residenza&quot; : &quot;Text&quot;,
    &quot;StatoSoggetto&quot; : &quot;Text&quot;,
    &quot;ClienteInPerimetro&quot; : &quot;Text&quot;,
    &quot;AnzianitaRelazione&quot; : &quot;Text&quot;,
    &quot;AnzianitaRelAltraAgenzia&quot; : &quot;Text&quot;,
    &quot;GestoreAnagrafica&quot; : &quot;Text&quot;,
    &quot;RuoloInAzienda&quot; : &quot;Text&quot;,
    &quot;Compagnia&quot; : &quot;Text&quot;,
    &quot;CodiceFiscale&quot; : &quot;Text&quot;,
    &quot;StatoCodiceFiscale&quot; : &quot;Text&quot;,
    &quot;Sesso&quot; : &quot;Text&quot;,
    &quot;FlagDomicilio&quot; : true,
    &quot;Originator&quot; : &quot;Text&quot;,
    &quot;PaeseDiNascita&quot; : &quot;Text&quot;,
    &quot;ProvinciaDiNascita&quot; : &quot;Text&quot;,
    &quot;ComuneDiNascita&quot; : &quot;Text&quot;,
    &quot;DataNascita&quot; : &quot;Text&quot;,
    &quot;Nome&quot; : &quot;Text&quot;
  },
  &quot;datiAgenzia&quot; : {
    &quot;StatoCliente&quot; : &quot;Text&quot;,
    &quot;AdesioneCauzione&quot; : &quot;Text&quot;,
    &quot;DataInizio&quot; : &quot;Text&quot;,
    &quot;DataCessazione&quot; : &quot;Text&quot;,
    &quot;AgenziaPrevalente&quot; : &quot;Text&quot;,
    &quot;Compagnia&quot; : &quot;Text&quot;,
    &quot;Subagenzia&quot; : &quot;Text&quot;,
    &quot;Modifica&quot; : {
      &quot;Subagenzia&quot; : &quot;Text&quot;
    }
  },
  &quot;recapiti&quot; : {
    &quot;RecAgenziaCellPersonale&quot; : &quot;Text&quot;,
    &quot;RecAgenziaCellPersonalePreferred&quot; : &quot;Boolean&quot;,
    &quot;RecAgenziaCellPersonaleUsage&quot; : &quot;Text&quot;,
    &quot;RecAgenziaCellPersonaleId&quot; : &quot;Text&quot;,
    &quot;RecAgenziaEmailPers&quot; : &quot;Text&quot;,
    &quot;RecAgenziaEmailPersPreferred&quot; : &quot;Boolean&quot;,
    &quot;RecAgenziaEmailPersUsage&quot; : &quot;Text&quot;,
    &quot;RecAgenziaEmailPersId&quot; : &quot;Text&quot;,
    &quot;RecAgenziaPEC&quot; : &quot;Text&quot;,
    &quot;RecAgenziaPECPreferred&quot; : &quot;Boolean&quot;,
    &quot;RecAgenziaPECUsage&quot; : &quot;Text&quot;,
    &quot;RecAgenziaPECId&quot; : &quot;Text&quot;,
    &quot;RecAgenziaFax&quot; : &quot;Text&quot;,
    &quot;RecAgenziaFaxPreferred&quot; : &quot;Boolean&quot;,
    &quot;RecAgenziaFaxUsage&quot; : &quot;Text&quot;,
    &quot;RecAgenziaFaxId&quot; : &quot;Text&quot;,
    &quot;RecAgenziaTelefono&quot; : &quot;Text&quot;,
    &quot;RecAgenziaTelefonoPreferred&quot; : &quot;Boolean&quot;,
    &quot;RecAgenziaTelefonoUsage&quot; : &quot;Text&quot;,
    &quot;RecAgenziaTelefonoId&quot; : &quot;Text&quot;,
    &quot;RecAgenziaTelefonoReferente&quot; : &quot;Text&quot;,
    &quot;RecAgenziaTelefonoReferentePreferred&quot; : &quot;Boolean&quot;,
    &quot;RecAgenziaTelefonoReferenteUsage&quot; : &quot;Text&quot;,
    &quot;RecAgenziaTelefonoReferenteId&quot; : &quot;Text&quot;,
    &quot;RecAgenziaReferente&quot; : &quot;Text&quot;,
    &quot;ARCellulare&quot; : &quot;Text&quot;,
    &quot;AREmail&quot; : &quot;Text&quot;,
    &quot;MigliorRecapitoCell&quot; : &quot;Text&quot;,
    &quot;MigliorRecFonteCellulare&quot; : &quot;Text&quot;,
    &quot;MigliorRecEmail&quot; : &quot;Text&quot;,
    &quot;MigliorRecFonteEmail&quot; : &quot;Text&quot;,
    &quot;MigliorRecEmailStato&quot; : &quot;Text&quot;,
    &quot;MigliorRecCellulareStato&quot; : &quot;Text&quot;
  },
  &quot;debug&quot; : {
    &quot;accountAgencyDetailId&quot; : &quot;Text&quot;,
    &quot;accountDetailId&quot; : &quot;Text&quot;
  }
}</expectedOutputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>AnagDetailTransform</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem109</globalKey>
        <inputFieldName>Value:accountDetail_BirthPlace</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:comuneNascitaBelfiore</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem140</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoPreferred</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaTelefonoPreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_Telefono ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_Telefono))</formulaExpression>
        <formulaResultPath>ShowTelefono</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem5</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountAgencyDetail_TelefonoReferenteUsage &quot;PER&quot; == &quot;Personale&quot; | var:Value:accountAgencyDetail_TelefonoReferenteUsage &quot;LAV&quot; == &quot;Lavoro&quot; &quot;&quot; IF IF</formulaConverted>
        <formulaExpression>IF(Value:accountAgencyDetail_TelefonoReferenteUsage == &quot;PER&quot;, &quot;Personale&quot;, IF(Value:accountAgencyDetail_TelefonoReferenteUsage == &quot;LAV&quot;, &quot;Lavoro&quot;, &quot;&quot;))</formulaExpression>
        <formulaResultPath>UsageTELREFConcat</formulaResultPath>
        <formulaSequence>16.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem63</globalKey>
        <inputFieldName>Value:accountDetail_LastName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:cognome</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem151</globalKey>
        <inputFieldName>Contatti</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:contatti</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem62</globalKey>
        <inputFieldName>Value:accountDetail_specializzazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:professione:specializzazione:codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem55</globalKey>
        <inputFieldName>Value:accountAgencyDetail_Email</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaEmailPers</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem53</globalKey>
        <inputFieldName>Value:accountDetailMDM_FonteEmail</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:MigliorRecFonteEmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem112</globalKey>
        <inputFieldName>KPI:AccountKey:PP_CLIENTE_DAL:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:AnzianitaRelazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountAgencyDetail_PECUsage &quot;PER&quot; == &quot;Personale&quot; | var:Value:accountAgencyDetail_PECUsage &quot;LAV&quot; == &quot;Lavoro&quot; &quot;&quot; IF IF</formulaConverted>
        <formulaExpression>IF(Value:accountAgencyDetail_PECUsage == &quot;PER&quot;, &quot;Personale&quot;, IF(Value:accountAgencyDetail_PECUsage == &quot;LAV&quot;, &quot;Lavoro&quot;, &quot;&quot;))</formulaExpression>
        <formulaResultPath>UsagePECConcat</formulaResultPath>
        <formulaSequence>14.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem15</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem108</globalKey>
        <inputFieldName>Value:accountDetail_flagDomicilio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:FlagDomicilio2</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>$Vlocity.false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem17</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:Propaga</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem73</globalKey>
        <inputFieldName>Value:accountDetail_ProfessioneDescrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:Professione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem59</globalKey>
        <inputFieldName>Value:accountDetail_BirthCountry</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:nazioneNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem52</globalKey>
        <inputFieldName>Value:accountDetail_Residenza</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:Residenza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem75</globalKey>
        <inputFieldName>Value:accountDetailMDM_SourceSystemOrigin</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:Originator</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;1&quot; : &quot;Unipol Assicurazioni S.p.A&quot;,
  &quot;2&quot; : &quot;BIM&quot;,
  &quot;4&quot; : &quot;UNISALUTE&quot;,
  &quot;5&quot; : &quot;LINEAR&quot;,
  &quot;7&quot; : &quot;FATTORIE DEL CERRO&quot;,
  &quot;8&quot; : &quot;LINEAR LIFE&quot;,
  &quot;10&quot; : &quot;ALG&quot;,
  &quot;11&quot; : &quot;NOVAAEG&quot;,
  &quot;12&quot; : &quot;BPER&quot;,
  &quot;13&quot; : &quot;TIM&quot;,
  &quot;14&quot; : &quot;SANTINI&quot;,
  &quot;15&quot; : &quot;CONAD&quot;,
  &quot;16&quot; : &quot;FINITALIA&quot;,
  &quot;17&quot; : &quot;UNIPOLTECH&quot;,
  &quot;18&quot; : &quot;UNA_NAXOS&quot;,
  &quot;19&quot; : &quot;CRIF/CRIBIS&quot;,
  &quot;20&quot; : &quot;PRONTO ASSISTANCE&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem133</globalKey>
        <inputFieldName>Privacy</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:privacy</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem42</globalKey>
        <inputFieldName>UsageTELREFConcat</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:UsageTELREFConcat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem122</globalKey>
        <inputFieldName>Value:accountAgencyDetail_CellulareId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaCellPersonaleId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem38</globalKey>
        <inputFieldName>Value:accountAgencyDetail_DataCessazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:DataCessazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem72</globalKey>
        <inputFieldName>Value:accountDetail_BirthDate</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:DataNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem21</globalKey>
        <inputFieldName>Value:accountDetail_FirstName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:nome</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem127</globalKey>
        <inputFieldName>Value:accountDetail_BirthPlace</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:ComuneDiNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem74</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoReferente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaTelefonoReferente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem132</globalKey>
        <inputFieldName>Value:accountDetail_TipoProfessioneDescrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:TipoProfessione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem22</globalKey>
        <inputFieldName>Value:accountAgencyDetail_dataInizioEffetto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:dataInizioEffetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem126</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoReferenteUsage</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaTelefonoReferenteUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem142</globalKey>
        <inputFieldName>Value:accountDetail_Gender</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:Sesso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;Male&quot; : &quot;Maschio&quot;,
  &quot;Female&quot; : &quot;Femmina&quot;,
  &quot;Other&quot; : &quot;Altro&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem131</globalKey>
        <inputFieldName>KPI:AccountKeyAgency:PP_AGENZIA_STATO:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:StatoSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem24</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:codiceSoggettoCanale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem77</globalKey>
        <inputFieldName>Value:accountAgencyDetail_SubAgencyCode</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:Modifica:SubDropDef:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem129</globalKey>
        <inputFieldName>Value:accountDetail_Eta</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:Eta</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem69</globalKey>
        <inputFieldName>Value:accountDetail_FiscalCode</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:CodiceFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>true</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem80</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:DisableCompany</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>agenzia</defaultValue>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem143</globalKey>
        <inputFieldName>Value:accountAgencyDetail_AgenziaPrevalente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:agenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem123</globalKey>
        <inputFieldName>Value:accountDetail_BirthProvince</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:extra:provinciaNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem128</globalKey>
        <inputFieldName>Value:accountDetail_SpecializzazioneDescrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:Specializzazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem145</globalKey>
        <inputFieldName>Value:accountAgencyDetail_SubAgencyCode</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:Subagenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem71</globalKey>
        <inputFieldName>Value:accountDetailMDM_FonteCellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:MigliorRecFonteCellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem23</globalKey>
        <inputFieldName>Value:accountAgencyDetail_EmailUsage</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaEmailPersUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_Cellulare ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_Cellulare))</formulaExpression>
        <formulaResultPath>ShowCellulare</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem11</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem130</globalKey>
        <inputFieldName>Value:accountAgencyDetail_FaxPreferred</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaFaxPreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem68</globalKey>
        <inputFieldName>Value:accountDetailMDM_Email</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:MigliorRecEmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem147</globalKey>
        <inputFieldName>Value:accountDetail_id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>debug:accountDetailId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_Fax ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_Fax))</formulaExpression>
        <formulaResultPath>ShowFax</formulaResultPath>
        <formulaSequence>7.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem16</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>compagnia</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem58</globalKey>
        <inputFieldName>Value:accountDetail_compagnia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem76</globalKey>
        <inputFieldName>Value:accountDetailMDM_Cellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:MigliorRecapitoCell</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem61</globalKey>
        <inputFieldName>Value:accountDetail_RegistryStatus</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:StatoCliente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem144</globalKey>
        <inputFieldName>GetBirthCountryName:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:PaeseDiNascitaParlante</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem82</globalKey>
        <inputFieldName>Value:accountDetail_tipoAnagrafica</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:tipoAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>PO</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem60</globalKey>
        <inputFieldName>Value:accountAgencyDetail_statoSoggetto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:statoSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem48</globalKey>
        <inputFieldName>Value:accountAgencyDetail_CellulareUsage</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaCellPersonaleUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>No</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem79</globalKey>
        <inputFieldName>KPI:AccountKey:CRMA_CLIENTEPATTO30:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:ClienteInPerimetro</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem138</globalKey>
        <inputFieldName>Value:accountAgencyDetail_Telefono</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaTelefono</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem125</globalKey>
        <inputFieldName>Value:accountAgengyDetail_flagClienteTop</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:flagClienteTop</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem99</globalKey>
        <inputFieldName>Value:accountDetail_FiscalCodeStatus</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:statoCodiceFiscalePartitaIva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountAgencyDetail_EmailUsage &quot;PER&quot; == &quot;Personale&quot; | var:Value:accountAgencyDetail_EmailUsage &quot;LAV&quot; == &quot;Lavoro&quot; &quot;&quot; IF IF</formulaConverted>
        <formulaExpression>IF(Value:accountAgencyDetail_EmailUsage == &quot;PER&quot;, &quot;Personale&quot;, IF(Value:accountAgencyDetail_EmailUsage == &quot;LAV&quot;, &quot;Lavoro&quot;, &quot;&quot;))</formulaExpression>
        <formulaResultPath>UsageMailConcat</formulaResultPath>
        <formulaSequence>12.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem6</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem139</globalKey>
        <inputFieldName>GetBirthCityName:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:ComuneDiNascitaParlante</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem33</globalKey>
        <inputFieldName>Value:accountDetail_professione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:professione:professione:codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem124</globalKey>
        <inputFieldName>Value:accountAgencyDetail_FaxUsage</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaFaxUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>M</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem118</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:tipoAnagraficaRichiesto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem157</globalKey>
        <inputFieldName>Value:accountAgencyDetail_clientInOtherAgency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:ClientePressoAltraAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem39</globalKey>
        <inputFieldName>UsagePECConcat</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:UsagePECConcat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem70</globalKey>
        <inputFieldName>Value:accountAgencyDetail_PECPreferred</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaPECPreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountAgencyDetail_TelefonoUsage &quot;PER&quot; == &quot;Personale&quot; | var:Value:accountAgencyDetail_TelefonoUsage &quot;LAV&quot; == &quot;Lavoro&quot; &quot;&quot; IF IF</formulaConverted>
        <formulaExpression>IF(Value:accountAgencyDetail_TelefonoUsage == &quot;PER&quot;, &quot;Personale&quot;, IF(Value:accountAgencyDetail_TelefonoUsage == &quot;LAV&quot;, &quot;Lavoro&quot;, &quot;&quot;))</formulaExpression>
        <formulaResultPath>UsageTELConcat</formulaResultPath>
        <formulaSequence>15.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem14</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem156</globalKey>
        <inputFieldName>ShowPEC</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:ShowPEC</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem135</globalKey>
        <inputFieldName>Value:accountDetail_settore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:professione:settore:codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem146</globalKey>
        <inputFieldName>showSpecializzazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:showSpecializzazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem121</globalKey>
        <inputFieldName>KPI:AccountKeyAgency:PP_AGENZIA_SOCIETA_CLIENTE_DAL:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:AnzianitaRelAltraAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>true</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem85</globalKey>
        <inputFieldName>FlagDomicilio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:FlagDomicilio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem134</globalKey>
        <inputFieldName>Value:accountDetail_BirthCountry</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:PaeseDiNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem84</globalKey>
        <inputFieldName>Value:accountAgencyDetail_codiceSubagenzia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:codiceSubAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem56</globalKey>
        <inputFieldName>Value:accountAgencyDetail_Referente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaReferente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem78</globalKey>
        <inputFieldName>UsageMailConcat</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:UsageMailConcat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>AGE</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem65</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:codiceCanale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem148</globalKey>
        <inputFieldName>showTipoProfessione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:showTipoProfessione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem50</globalKey>
        <inputFieldName>Value:codiceGestore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:GestoreAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>A</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem111</globalKey>
        <inputFieldName>Value:accountDetail_statoAnagrafica</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:statoAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem137</globalKey>
        <inputFieldName>Value:accountAgencyDetail_flagAdesioneFEA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:flagAdesioneFEA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem45</globalKey>
        <inputFieldName>Value:accountDetailPrivateArea_Email</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:AREmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem51</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:professione:mercatoPreferenziale:codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem149</globalKey>
        <inputFieldName>Value:accountAgencyDetail_PEC</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaPEC</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem64</globalKey>
        <inputFieldName>company</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:Compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem86</globalKey>
        <inputFieldName>Value:accountDetail_id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:extra:id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem158</globalKey>
        <inputFieldName>Value:codiceGestore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:Modifica:GestoreAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountDetail_PubPrivDescrizione ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountDetail_PubPrivDescrizione))</formulaExpression>
        <formulaResultPath>showPubPriv</formulaResultPath>
        <formulaSequence>10.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem136</globalKey>
        <inputFieldName>Value:accountDetail_PubPrivDescrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:PubPriv</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem153</globalKey>
        <inputFieldName>Value:accountDetailMDM_StatoCellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:MigliorRecCellulareStato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| &quot;SELECT/\/\/Name/\/\/FROM/\/\/Unita_Territoriali__c/\/\//\/\/WHERE/\/\/Sigla_automobilistica__c/\/\/=/\/\/&apos;{0}&apos;/\/\/and/\/\//\/\/RecordType.Name/\/\/=/\/\/&apos;Provincia&apos;/\/\/LIMIT/\/\/1/\/\/&quot; var:Value:accountDetail_BirthProvince QUERY</formulaConverted>
        <formulaExpression>QUERY(&quot;SELECT Name FROM Unita_Territoriali__c  WHERE Sigla_automobilistica__c = &apos;{0}&apos; and  RecordType.Name = &apos;Provincia&apos; LIMIT 1 &quot;, %Value:accountDetail_BirthProvince%)</formulaExpression>
        <formulaResultPath>ProvinciaEstesa</formulaResultPath>
        <formulaSequence>17.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem13</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem40</globalKey>
        <inputFieldName>UsageFaxConcat</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:UsageFaxConcat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem67</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:Propaga</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_TelefonoReferente ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_TelefonoReferente))</formulaExpression>
        <formulaResultPath>ShowTelefonoReferente</formulaResultPath>
        <formulaSequence>6.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem8</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem92</globalKey>
        <inputFieldName>Value:accountDetail_dataDecesso</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:dataDecesso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem110</globalKey>
        <inputFieldName>Value:accountDetailPrivateArea_Cellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:ARCellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem106</globalKey>
        <inputFieldName>ProvinciaEstesa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:ProvinciaDiNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;&quot; : &quot;&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem91</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoReferentePreferred</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaTelefonoReferentePreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem44</globalKey>
        <inputFieldName>UsageCellConcat</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:UsageCellConcat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>tipo soggetto</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem96</globalKey>
        <inputFieldName>Value:accountDetail_tipoSoggetto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:tipoSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem120</globalKey>
        <inputFieldName>Value:accountAgencyDetail_AgenziaPrevalente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:AgenziaPrevalente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem35</globalKey>
        <inputFieldName>Value:accountAgencyDetail_CellularePreferred</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaCellPersonalePreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountDetail_TipoProfessioneDescrizione ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountDetail_TipoProfessioneDescrizione))</formulaExpression>
        <formulaResultPath>showTipoProfessione</formulaResultPath>
        <formulaSequence>9.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem10</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem66</globalKey>
        <inputFieldName>ShowTelefonoReferente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:ShowTelefonoReferente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem115</globalKey>
        <inputFieldName>Value:account_ExternalId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:codiceFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem88</globalKey>
        <inputFieldName>Value:accountDetail_Residenza</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:Residenza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem105</globalKey>
        <inputFieldName>ShowEmail</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:ShowEmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem47</globalKey>
        <inputFieldName>Value:accountDetail_Gender</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:sesso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;Male&quot; : &quot;M&quot;,
  &quot;Female&quot; : &quot;F&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem87</globalKey>
        <inputFieldName>Value:accountDetail_Ciu</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem152</globalKey>
        <inputFieldName>ShowTelefono</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:ShowTelefono</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem95</globalKey>
        <inputFieldName>Value:accountAgencyDetail_EmailId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaEmailPersId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem94</globalKey>
        <inputFieldName>Value:accountAgencyDetail_SubAgencyCode</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:Modifica:SubDropDef:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem104</globalKey>
        <inputFieldName>Value:accountAgencyDetail_PECUsage</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaPECUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem117</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:RuoloInAzienda</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem93</globalKey>
        <inputFieldName>Value:accountDetail_FiscalCodeStatus</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:StatoCodiceFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_PEC ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_PEC))</formulaExpression>
        <formulaResultPath>ShowPEC</formulaResultPath>
        <formulaSequence>5.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem7</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>unipolsai</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem26</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>{}</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem81</globalKey>
        <inputFieldName>Indirizzi</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:indirizzi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem54</globalKey>
        <inputFieldName>Value:accountAgencyDetail_AgenziaPrevalente</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:codiceAgenziaPrevalente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem90</globalKey>
        <inputFieldName>ShowCellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:ShowCellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem41</globalKey>
        <inputFieldName>Value:accountDetail_DeathDate</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:DataDecesso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem19</globalKey>
        <inputFieldName>Value:accountAgencyDetail_AdesioneCauzione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:AdesioneCauzione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;true&quot; : &quot;SI&quot;,
  &quot;false&quot; : &quot;NO&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem20</globalKey>
        <inputFieldName>Value:accountAgencyDetail_DataCessazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:dataCessazioneCliente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem89</globalKey>
        <inputFieldName>Value:accountDetail_BirthPlace</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:comuneNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem114</globalKey>
        <inputFieldName>Value:accountDetail_Domicilio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:Domicilio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem25</globalKey>
        <inputFieldName>CompagnieMandato:list_mandato</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:extra:mandato:listaCompagnie</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem83</globalKey>
        <inputFieldName>Value:accountAgencyDetail_FaxId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaFaxId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem98</globalKey>
        <inputFieldName>Value:accountAgencyDetail_dataClienteTop</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:dataClienteTop</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountDetail_Residenza var:Value:accountAgencyDetail_Domicilio == true false IF</formulaConverted>
        <formulaExpression>IF(Value:accountDetail_Residenza == Value:accountAgencyDetail_Domicilio, true, false)</formulaExpression>
        <formulaResultPath>FlagDomicilio</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem4</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem57</globalKey>
        <inputFieldName>Value:accountAgencyDetail_Cellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:RecAgenziaCellPersonale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem155</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoUsage</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaTelefonoUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem27</globalKey>
        <inputFieldName>CompagnieMandato:societa_attuale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:extra:mandato:compagniaAttuale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountAgencyDetail_FaxUsage &quot;PER&quot; == &quot;Personale&quot; | var:Value:accountAgencyDetail_FaxUsage &quot;LAV&quot; == &quot;Lavoro&quot; &quot;&quot; IF IF</formulaConverted>
        <formulaExpression>IF(Value:accountAgencyDetail_FaxUsage == &quot;PER&quot;, &quot;Personale&quot;, IF(Value:accountAgencyDetail_FaxUsage == &quot;LAV&quot;, &quot;Lavoro&quot;, &quot;&quot;))</formulaExpression>
        <formulaResultPath>UsageFaxConcat</formulaResultPath>
        <formulaSequence>13.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem9</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem150</globalKey>
        <inputFieldName>Value:accountAgencyDetail_id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>debug:accountAgencyDetailId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem46</globalKey>
        <inputFieldName>Value:accountDetail_BirthProvince</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:provinciaNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem100</globalKey>
        <inputFieldName>Value:accountAgencyDetail_flagProprietaContattiFea</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:flagProprietaContattiFea</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem116</globalKey>
        <inputFieldName>Value:accountDetail_FirstName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:Nome</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem28</globalKey>
        <inputFieldName>Value:accountAgencyDetail_dataFineEffetto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:dataFineEffetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem154</globalKey>
        <inputFieldName>ShowFax</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:ShowFax</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem102</globalKey>
        <inputFieldName>Value:accountDetail_LastName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:Cognome</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Value:accountAgencyDetail_CellulareUsage &quot;PER&quot; == &quot;Personale&quot; | var:Value:accountAgencyDetail_CellulareUsage &quot;LAV&quot; == &quot;Lavoro&quot; &quot;&quot; IF IF</formulaConverted>
        <formulaExpression>IF(Value:accountAgencyDetail_CellulareUsage == &quot;PER&quot;, &quot;Personale&quot;, IF(Value:accountAgencyDetail_CellulareUsage == &quot;LAV&quot;, &quot;Lavoro&quot;, &quot;&quot;))</formulaExpression>
        <formulaResultPath>UsageCellConcat</formulaResultPath>
        <formulaSequence>11.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem12</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem101</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaTelefonoId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem49</globalKey>
        <inputFieldName>Value:accountAgencyDetail_AdesioneCauzione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:datiAgenzia:flagAutorizzazioneCauzione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>$Vlocity.true</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem97</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>recapiti:DisableCompany</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem119</globalKey>
        <inputFieldName>Value:accountDetail_dataNascita</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:dataNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem103</globalKey>
        <inputFieldName>Value:accountDetail_FiscalCodeStatus</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:anagrafica:statoCodiceFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountAgencyDetail_Email ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountAgencyDetail_Email))</formulaExpression>
        <formulaResultPath>ShowEmail</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem29</globalKey>
        <inputFieldName>Value:accountAgencyDetail_TelefonoReferenteId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaTelefonoReferenteId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem37</globalKey>
        <inputFieldName>UsageTELConcat</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:UsageTELConcat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem34</globalKey>
        <inputFieldName>Value:accountDetail_impiego</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAnagrafici:modifica:professione:impiego:codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem107</globalKey>
        <inputFieldName>Value:accountAgencyDetail_EmailPreferred</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaEmailPersPreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem31</globalKey>
        <inputFieldName>Value:accountAgencyDetail__DataCreazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>datiAgenzia:DataInizio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem36</globalKey>
        <inputFieldName>Value:accountDetailMDM_StatoEmail</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:MigliorRecEmailStato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem30</globalKey>
        <inputFieldName>showPubPriv</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:showPubPriv</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>true</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem32</globalKey>
        <inputFieldName>FlagDomicilio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>datiAnagrafici:modifica:extra:resiDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem113</globalKey>
        <inputFieldName>Value:accountAgencyDetail_PECId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaPECId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem18</globalKey>
        <inputFieldName>Value:accountAgencyDetail_Fax</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>recapiti:RecAgenziaFax</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem43</globalKey>
        <inputFieldName>Value:accountAgencyDetail_SubAgencyCode</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>datiAgenzia:Modifica:Subagenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Value:accountDetail_SpecializzazioneDescrizione ISBLANK NOT</formulaConverted>
        <formulaExpression>NOT(ISBLANK(Value:accountDetail_SpecializzazioneDescrizione))</formulaExpression>
        <formulaResultPath>showSpecializzazione</formulaResultPath>
        <formulaSequence>8.0</formulaSequence>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem2</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailTransformCustom0jI9V000000tE3xUAEItem141</globalKey>
        <inputFieldName>Value:accountDetail_id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>recapiti:accountDetailId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;KPI&quot; : {
    &quot;AccountKeyAgency&quot; : {
      &quot;&quot; : &quot;&quot;,
      &quot;PP_AGENZIA_STATO&quot; : {
        &quot;Value&quot; : &quot;Cliente&quot;
      },
      &quot;PP_AGENZIA_CLIENTE_DAL&quot; : {
        &quot;Value&quot; : &quot;22-04-2024&quot;
      },
      &quot;PP_AGENZIA_SOCIETA_STATO&quot; : {
        &quot;Value&quot; : &quot;Cliente&quot;
      },
      &quot;PP_AGENZIA_SOCIETA_CLIENTE_DAL&quot; : {
        &quot;Value&quot; : &quot;12-10-2022&quot;
      },
      &quot;PP_CONVENZIONI&quot; : {
        &quot;Value&quot; : &quot;Si&quot;
      },
      &quot;CRM_CLIENTE_VIP&quot; : {
        &quot;Value&quot; : &quot;Si&quot;
      },
      &quot;ConditionalBlock3Status&quot; : true,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;LoopBlockIterationIndex&quot; : 6
    },
    &quot;AccountKey&quot; : {
      &quot;&quot; : &quot;&quot;,
      &quot;CRMA_VALUS&quot; : null,
      &quot;MDM_CONTATTABILITA&quot; : null,
      &quot;TPD_DATAULTIMOACCESSO&quot; : null,
      &quot;CRMA_CAPACITADISPESA&quot; : {
        &quot;Value&quot; : &quot;Alta&quot;
      },
      &quot;PP_TITOLARITACLIENTE&quot; : null,
      &quot;MEDAGLIA_VOC&quot; : null,
      &quot;TPD_ACCESSO&quot; : null,
      &quot;ANAG2_STATOCIVILE&quot; : null,
      &quot;ANAG2_FLAGFIGLI&quot; : null,
      &quot;ANAG2_TITOLODISTUDIO&quot; : null,
      &quot;ANAG2_TIPORISPARMIO&quot; : null,
      &quot;ANAG2_FLAGCONIUGECARICO&quot; : null,
      &quot;ANAG2_NUMEROFIGLIOCARICO&quot; : null,
      &quot;ANAG2_REDDITOLAVORO&quot; : null,
      &quot;ANAG2_EVOLUZIONEREDDITOLAVORO&quot; : null,
      &quot;ANAG2_EVOLUZIONEALTRIREDDITI&quot; : null,
      &quot;ANAG2_SPESEINCOMPRIMIBILI&quot; : null,
      &quot;ANAG2_SPESECOMPRIMIBILI&quot; : null,
      &quot;ANAG2_DATANASCITACONIUGE&quot; : null,
      &quot;ANAG2_NUMERONUCLEOFAMIGLIA&quot; : null,
      &quot;ANAG2_FLAGANIMALIDOMESTICI&quot; : null,
      &quot;ANAG2_FLAGCOLLABORATORIDOMESTICI&quot; : null,
      &quot;ANAG2_FLAGCASAPROPRIETA&quot; : null,
      &quot;ANAG2_FLAGCASAAFFITTO&quot; : null,
      &quot;ANAG2_FLAGSECONDACASA&quot; : null,
      &quot;ANAG2_FLAGMUTUO&quot; : null,
      &quot;CRIBIS_AZIENDAINUTILE_ULTIMOANNO&quot; : null,
      &quot;CRIBIS_AZIENDAINUTILE_ULTIMI3ANNI&quot; : null,
      &quot;CRIBIS_NDIPENDENTIPERINQUADRAMENTO&quot; : null,
      &quot;CRIBIS_DETTAGLIOTFR&quot; : null,
      &quot;ConditionalBlock2Status&quot; : true,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;LoopBlockIterationIndex&quot; : 5
    }
  },
  &quot;Value&quot; : {
    &quot;account_Name&quot; : &quot;Beatrice Rizzo&quot;,
    &quot;accountDetail_BirthProvince&quot; : &quot;RM&quot;,
    &quot;accountAgencyDetail_AdesioneCauzione&quot; : &quot;No&quot;,
    &quot;accountAccountRelationSociey_Id&quot; : &quot;a009X00000RHhdtQAD&quot;,
    &quot;account_LastName&quot; : &quot;Rizzo&quot;,
    &quot;accountDetailPrivateArea_Email&quot; : &quot;<EMAIL>&quot;,
    &quot;account_FirstName&quot; : &quot;Beatrice&quot;,
    &quot;account_Age&quot; : &quot;46&quot;,
    &quot;accountDetailMDM_Cellulare&quot; : &quot;+39 328 9873451&quot;,
    &quot;accountAgencyDetail__DataCreazione&quot; : &quot;01/17/2024&quot;,
    &quot;accountDetailPrivateArea_Cellulare&quot; : &quot;340 1134567&quot;,
    &quot;accountDetailMDM_FonteCellulare&quot; : &quot;Work&quot;,
    &quot;accountDetail_Occupation&quot; : &quot;Insegnante&quot;,
    &quot;accountDetailMDM_FonteEmail&quot; : &quot;Other&quot;,
    &quot;accountAccountRelationAgency_Id&quot; : &quot;a009X00000RHUgaQAH&quot;,
    &quot;account_ExternalId&quot; : &quot;****************&quot;,
    &quot;accountAgencyDetail_Cellulare&quot; : &quot;+39 **********&quot;,
    &quot;accountDetailMDM_Email&quot; : &quot;<EMAIL>&quot;,
    &quot;accountAgencyDetail_Email&quot; : &quot;<EMAIL>&quot;,
    &quot;accountDetail_Residenza&quot; : &quot;Via Carlo Farini, Milano, Italy&quot;,
    &quot;accountAgencyDetail_CellularePreferred&quot; : &quot;Boolean&quot;,
    &quot;accountAgencyDetail_CellulareUsage&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_CellulareId&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_EmailPreferred&quot; : &quot;Boolean&quot;,
    &quot;accountAgencyDetail_EmailUsage&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_EmailId&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_PEC&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_PECPreferred&quot; : &quot;Boolean&quot;,
    &quot;accountAgencyDetail_PECUsage&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_PECId&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_Fax&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_FaxPreferred&quot; : &quot;Boolean&quot;,
    &quot;accountAgencyDetail_FaxUsage&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_FaxId&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_Telefono&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_TelefonoPreferred&quot; : &quot;Boolean&quot;,
    &quot;accountAgencyDetail_TelefonoUsage&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_TelefonoId&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_TelefonoReferente&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_TelefonoReferentePreferred&quot; : &quot;Boolean&quot;,
    &quot;accountAgencyDetail_TelefonoReferenteUsage&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_TelefonoReferenteId&quot; : &quot;Text&quot;,
    &quot;accountAgencyDetail_Referente&quot; : &quot;Text&quot;
  }
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>AnagDetailTransform_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
