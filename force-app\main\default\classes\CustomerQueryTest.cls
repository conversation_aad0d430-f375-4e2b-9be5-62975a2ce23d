@IsTest
private class CustomerQueryTest {
    
    @TestSetup
    static void setupData() {
        // Account di test
        Account acc = new Account(
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Person Account').getRecordTypeId(),
            LastName = 'Rossi', 
            FirstName = 'Mario'
        );
        insert acc;
        
        Account acc2 = new Account(
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Person Account').getRecordTypeId(),
            LastName = 'TEST', 
            FirstName = 'TEST'
        );
        insert acc2;

        // Opportunity collegata
        Opportunity opp = new Opportunity(
            Name = 'Test Opp',
            AccountId = acc.Id,
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(30)
        );
        insert opp;

        // Quote collegato
        Quote q = new Quote(
            Name = 'Test Quote',
            OpportunityId = opp.Id,
            Status = 'Draft'
        );
        insert q;

        // Coverage collegato al Quote
        OpportunityCoverage__c cov = new OpportunityCoverage__c(
            Name = 'Coverage Test',
            Quote__c = q.Id,
            AreaOfNeed__c = 'Casa'
        );
        insert cov;
        
        FinServ__ReciprocalRole__c role1 = new FinServ__ReciprocalRole__c();
        role1.Name = 'TEST';
        role1.FinServ__RelationshipType__c = 'All';
        role1.FinServ__InverseRole__c = 'TEST2';
        insert role1;
        
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c();
        role.Name = 'TEST2';
        role.FinServ__RelationshipType__c = 'All';
        role.FinServ__InverseRole__c = 'TEST';
        role.FinServ__InverseRelationship__c = role1.Id;
        insert role;
        
        role1.FinServ__InverseRelationship__c = role.Id;
        update role1;

        // Relazione finanziaria
        FinServ__AccountAccountRelation__c rel = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = acc.Id,
            FinServ__RelatedAccount__c = acc2.Id,
            FinServ__Role__c = role.Id
        );
        insert rel;

        // AccountDetails
        AccountDetails__c accDet = new AccountDetails__c(
            Name = 'Details Test',
            Relation__c = rel.Id,
            Account__c = acc.Id
        );
        insert accDet;

        // AccountDetailsNPI
        AccountDetailsNPI__c accDetNpi = new AccountDetailsNPI__c(
            Name = 'Details NPI Test',
            Relation__c = rel.Id
        );
        insert accDetNpi;
    }

    @IsTest
    static void testQueryAccountById() {
        Account acc = [SELECT Id FROM Account LIMIT 1];
        CustomerQuery.queryAccountById(acc.Id);
        CustomerQuery.queryAccountById(null);
    }

    @IsTest
    static void testQueryContactById() {
        Contact con = [SELECT Id FROM Contact LIMIT 1];
        CustomerQuery.queryContactById(con.Id);
        CustomerQuery.queryContactById(null);
    }

    @IsTest
    static void testQueryOpportunitiesByAccountId() {
        Account acc = [SELECT Id FROM Account LIMIT 1];
        CustomerQuery.queryOpportunitiesByAccountId(acc.Id);
        CustomerQuery.queryOpportunitiesByAccountId(null);
    }

    @IsTest
    static void testQueryQuotesByOpportunities() {
        Opportunity opp = [SELECT Id FROM Opportunity LIMIT 1];
        CustomerQuery.queryQuotesByOpportunities(new List<Opportunity>{opp});
        CustomerQuery.queryQuotesByOpportunities(new List<Opportunity>());
    }

    @IsTest
    static void testQueryOpportunityCoveragesByQuotes() {
        Quote q = [SELECT Id FROM Quote LIMIT 1];
        CustomerQuery.queryOpportunityCoveragesByQuotes(new List<Quote>{q});
    }

    @IsTest
    static void testQueryAccountFinancialsByAccountId() {
        Account acc = [SELECT Id FROM Account LIMIT 1];
        CustomerQuery.queryAccountFinancialsByAccountId(acc.Id);
    }

    @IsTest
    static void testQueryAccountDetailsByAccountId() {
        Account acc = [SELECT Id FROM Account LIMIT 1];
        CustomerQuery.queryAccountDetailsByAccountId(acc.Id);
    }

    @IsTest
    static void testQueryAccountDetailsNPIByAccountId() {
        Account acc = [SELECT Id FROM Account LIMIT 1];
        CustomerQuery.queryAccountDetailsNPIByAccountId(acc.Id);
    }
}