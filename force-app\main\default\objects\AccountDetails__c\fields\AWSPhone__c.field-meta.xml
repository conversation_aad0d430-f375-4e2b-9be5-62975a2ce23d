<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>AWSPhone__c</fullName>
    <externalId>false</externalId>
    <formula>IF(
ISBLANK(Phone__c),
&quot;&quot;,
IF(
OR(
/* --- whitelist esistente (con +39) --- */
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393499361766&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393459758722&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393485356027&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393891227200&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393913289105&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393398145808&quot;,

/* --- whitelist esistente (senza +39) --- */
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3499361766&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3459758722&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3485356027&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3891227200&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3398145808&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3913289105&quot;,

/* --- NUOVI NUMERI (con +39) --- */
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393488201316&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393666350444&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393666344309&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393384937659&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393316363739&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393458843948&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393392814726&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;+393387901064&quot;,

/* --- NUOVI NUMERI (senza +39) --- */
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3488201316&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3666350444&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3666344309&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3384937659&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3316363739&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3458843948&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3392814726&quot;,
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;) = &quot;3387901064&quot;
),
/* Se è nella whitelist → restituisci sempre in E.164 (+39...) */
IF(
BEGINS(SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;), &quot;+39&quot;),
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;),
&quot;+39&quot; &amp; SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;)
),
/* Se NON è in whitelist → togli +39 se presente, altrimenti lascia com&apos;è (senza spazi) */
IF(
BEGINS(SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;), &quot;+39&quot;),
MID(SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;), 4, LEN(SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;)) - 3),
SUBSTITUTE(Phone__c,&quot; &quot;,&quot;&quot;)
)
)
)</formula>
    <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    <label>AWSPhone</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
