@IsTest
public class VoiceCallUpdaterTest {
    
    @TestSetup
    static void setupTestData() {
        //CallCenter cc = [SELECT Id FROM CallCenter WHERE Name = 'UnipolDEV1ContactCenter'];
        VoiceCall vc = new VoiceCall();
        vc.CallType = 'Inbound';
        vc.OwnerId = UserInfo.getUserId();
        //vc.CallcenterId = cc.Id;
        vc.CallStartDateTime = System.now();
        vc.CallEndDateTime = System.now(); 
        vc.FromPhoneNumber= 'Yes'; 
        vc.ToPhoneNumber = '3333333333';
        vc.CallType = 'Callback';
        try{ insert vc; }catch(Exception ex){}
    }

    @IsTest
    static void testUpdateVoiceCall() {
        try{
            VoiceCall vc = [SELECT Id FROM VoiceCall LIMIT 1];

            VoiceCallUpdater.VoiceCallUpdateRequest req = new VoiceCallUpdater.VoiceCallUpdateRequest();
            req.voiceCallId = vc.Id;
            req.CustomFieldApiName = 'intermediate_outcome__c';
            req.CustomFieldValue = 'Tentato Contatto';
    
            Test.startTest();
            VoiceCallUpdater.updateVoiceCall(new List<VoiceCallUpdater.VoiceCallUpdateRequest>{ req });
            Test.stopTest();
        }catch(Exception ex){}
    }

    @IsTest
    static void testUpdateVoiceCallInvalidField() {
        try{
        VoiceCall vc = [SELECT Id FROM VoiceCall LIMIT 1];

        VoiceCallUpdater.VoiceCallUpdateRequest req = new VoiceCallUpdater.VoiceCallUpdateRequest();
        req.voiceCallId = vc.Id;
        req.CustomFieldApiName = 'Invalid_Field__c';
        req.CustomFieldValue = 'SomeValue';

        Test.startTest();
        VoiceCallUpdater.updateVoiceCall(new List<VoiceCallUpdater.VoiceCallUpdateRequest>{ req });
        Test.stopTest();
            
        }catch(Exception ex){}
    }
}