<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;recordId&quot;: &quot;a1x9O000001M2HBQA0&quot;,
    &quot;userId&quot;: &quot;0059O00000WEYgvQAH&quot;,
    &quot;flagPreferito&quot;: &quot;false&quot;,
    &quot;Usage&quot;: &quot;Personale&quot;,
    &quot;idContatto&quot;: &quot;27072768&quot;,
    &quot;contatto&quot;: &quot;3330000111&quot;,
    &quot;tipoContatto&quot;: &quot;CELL&quot;,
    &quot;User.userId&quot;: &quot;0059O00000WEYgvQAH&quot;
}</customJavaScript>
    <description><PERSON>: 1455040</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>false</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>DA_RecapitiSingleUpdate</name>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ArrayTransform</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;output&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;transformInput&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UtilObjToList&quot;,
  &quot;sendJSONNode&quot; : &quot;input&quot;
}</propertySetConfig>
        <sequenceNumber>11.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>AddEmail</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;transformInput&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;codiceAgenzia&quot; : &quot;=CONCAT(&apos;&apos;, %codiceAgenzia%)&quot;,
    &quot;referente&quot; : &quot;=&quot;,
    &quot;flagPreferito&quot; : &quot;=Preferred&quot;,
    &quot;tipologiaContatto&quot; : &quot;=IF(Usage == \&quot;Personale\&quot;, \&quot;PER\&quot;, IF(Usage == \&quot;Lavoro\&quot;, \&quot;LAV\&quot;, Usage))&quot;,
    &quot;tipoContatto&quot; : &quot;=&apos;MAIL&apos;&quot;,
    &quot;dataFineEffetto&quot; : &quot;=&quot;,
    &quot;compagnia&quot; : &quot;=requestContext:compagnia&quot;,
    &quot;dataInizioEffetto&quot; : &quot;=FORMATDATETIME(NOW(), &apos;yyyy-MM-dd&apos;T&apos;HH:mm:ss.SSSZ&apos;, &apos;Europe/Rome&apos;)&quot;,
    &quot;id&quot; : &quot;=CONCAT(&apos;&apos;, idContatto)&quot;,
    &quot;contatto&quot; : &quot;=TOSTRING(validationResult:result)&quot;,
    &quot;ciu&quot; : &quot;=requestContext:ciu&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>EmailUpdateCandidate</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;requestContext:accountAgencyDetail:Email&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;Usage&quot; : &quot;=RecAgenziaEmailPersUsage&quot;,
    &quot;Value&quot; : &quot;=TOSTRING(validationResult:result)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>EmailValidationFailed</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;isActive&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;NOT(%validationResult:success%)&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;validationResult&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ValidateEmail</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : true,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;AnagDetails_RecapitiValidate&quot;,
  &quot;responseJSONNode&quot; : &quot;validationResult&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;contactValue&quot; : &quot;=%contattoInput%&quot;,
    &quot;inputMode&quot; : &quot;=&apos;UPDATE&apos;&quot;,
    &quot;contactTarget&quot; : &quot;=Usage&quot;,
    &quot;contactType&quot; : &quot;=&apos;MAIL&apos;&quot;
  },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckEmail</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;tipoContatto==\&quot;MAIL\&quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>AddFax</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;transformInput&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;codiceAgenzia&quot; : &quot;=CONCAT(&apos;&apos;, %codiceAgenzia%)&quot;,
    &quot;referente&quot; : &quot;=&quot;,
    &quot;flagPreferito&quot; : &quot;=Preferred&quot;,
    &quot;tipologiaContatto&quot; : &quot;=IF(Usage == \&quot;Personale\&quot;, \&quot;PER\&quot;, IF(Usage == \&quot;Lavoro\&quot;, \&quot;LAV\&quot;, Usage))&quot;,
    &quot;tipoContatto&quot; : &quot;=&apos;FAX&apos;&quot;,
    &quot;dataFineEffetto&quot; : &quot;=&quot;,
    &quot;compagnia&quot; : &quot;=requestContext:compagnia&quot;,
    &quot;dataInizioEffetto&quot; : &quot;=FORMATDATETIME(NOW(), &apos;yyyy-MM-dd&apos;T&apos;HH:mm:ss.SSSZ&apos;, &apos;Europe/Rome&apos;)&quot;,
    &quot;id&quot; : &quot;=CONCAT(&apos;&apos;, idContatto)&quot;,
    &quot;contatto&quot; : &quot;=TOSTRING(validationResult:result)&quot;,
    &quot;ciu&quot; : &quot;=requestContext:ciu&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>FaxUpdateCandidate</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;requestContext:accountAgencyDetail:Fax&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;Usage&quot; : &quot;=RecAgenziaFaxUsage&quot;,
    &quot;Value&quot; : &quot;=TOSTRING(validationResult:result)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>FaxValidationFailed</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;isActive&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;NOT(%validationResult:success%)&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;validationResult&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ValidateFax</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : true,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;AnagDetails_RecapitiValidate&quot;,
  &quot;responseJSONNode&quot; : &quot;validationResult&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;contactValue&quot; : &quot;=%contattoInput%&quot;,
    &quot;inputMode&quot; : &quot;=&apos;UPDATE&apos;&quot;,
    &quot;contactTarget&quot; : &quot;=Usage&quot;,
    &quot;contactType&quot; : &quot;=&apos;FAX&apos;&quot;
  },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckFax</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;tipoContatto==\&quot;FAX\&quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>AddCellulare</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;transformInput&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;codiceAgenzia&quot; : &quot;=CONCAT(&apos;&apos;, %codiceAgenzia%)&quot;,
    &quot;referente&quot; : &quot;=&quot;,
    &quot;flagPreferito&quot; : &quot;=Preferred&quot;,
    &quot;tipologiaContatto&quot; : &quot;=IF(Usage == \&quot;Personale\&quot;, \&quot;PER\&quot;, IF(Usage == \&quot;Lavoro\&quot;, \&quot;LAV\&quot;, Usage))&quot;,
    &quot;tipoContatto&quot; : &quot;=&apos;CELL&apos;&quot;,
    &quot;dataFineEffetto&quot; : &quot;=&quot;,
    &quot;compagnia&quot; : &quot;=requestContext:compagnia&quot;,
    &quot;dataInizioEffetto&quot; : &quot;=FORMATDATETIME(NOW(), &apos;yyyy-MM-dd&apos;T&apos;HH:mm:ss.SSSZ&apos;, &apos;Europe/Rome&apos;)&quot;,
    &quot;id&quot; : &quot;=CONCAT(&apos;&apos;, idContatto)&quot;,
    &quot;contatto&quot; : &quot;=TOSTRING(validationResult:result)&quot;,
    &quot;ciu&quot; : &quot;=requestContext:ciu&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>CellulareUpdateCandidate</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;requestContext:accountAgencyDetail:Cell&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;Usage&quot; : &quot;=RecAgenziaCellPersonaleUsage&quot;,
    &quot;Value&quot; : &quot;=TOSTRING(validationResult:result)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>CellulareValidationFailed</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;isActive&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;NOT(%validationResult:success%)&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;validationResult&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ValidateCellulare</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : true,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;AnagDetails_RecapitiValidate&quot;,
  &quot;responseJSONNode&quot; : &quot;validationResult&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;contactValue&quot; : &quot;=%contattoInput%&quot;,
    &quot;inputMode&quot; : &quot;=&apos;UPDATE&apos;&quot;,
    &quot;contactTarget&quot; : &quot;=&apos;PER&apos;&quot;,
    &quot;contactType&quot; : &quot;=&apos;CELL&apos;&quot;
  },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckisCellulare</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;tipoContatto==\&quot;CELL\&quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>AddPEC</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;transformInput&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;codiceAgenzia&quot; : &quot;=CONCAT(&apos;&apos;, %codiceAgenzia%)&quot;,
    &quot;referente&quot; : &quot;=&quot;,
    &quot;flagPreferito&quot; : &quot;=Preferred&quot;,
    &quot;tipologiaContatto&quot; : &quot;=IF(Usage == \&quot;Personale\&quot;, \&quot;PER\&quot;, IF(Usage == \&quot;Lavoro\&quot;, \&quot;LAV\&quot;, Usage))&quot;,
    &quot;tipoContatto&quot; : &quot;=&apos;PEC&apos;&quot;,
    &quot;dataFineEffetto&quot; : &quot;=&quot;,
    &quot;compagnia&quot; : &quot;=requestContext:compagnia&quot;,
    &quot;dataInizioEffetto&quot; : &quot;=FORMATDATETIME(NOW(), &apos;yyyy-MM-dd&apos;T&apos;HH:mm:ss.SSSZ&apos;, &apos;Europe/Rome&apos;)&quot;,
    &quot;id&quot; : &quot;=CONCAT(&apos;&apos;, idContatto)&quot;,
    &quot;contatto&quot; : &quot;=TOSTRING(validationResult:result)&quot;,
    &quot;ciu&quot; : &quot;=requestContext:ciu&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>PECUpdateCandidate</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;requestContext:accountAgencyDetail:PEC&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;Usage&quot; : &quot;=RecAgenziaPECUsage&quot;,
    &quot;Value&quot; : &quot;=TOSTRING(validationResult:result)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>PECValidationFailed</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;isActive&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;NOT(%validationResult:success%)&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;validationResult&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ValidatePEC</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : true,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;AnagDetails_RecapitiValidate&quot;,
  &quot;responseJSONNode&quot; : &quot;validationResult&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;contactValue&quot; : &quot;=%contattoInput%&quot;,
    &quot;inputMode&quot; : &quot;=&apos;UPDATE&apos;&quot;,
    &quot;contactTarget&quot; : &quot;=Usage&quot;,
    &quot;contactType&quot; : &quot;=&apos;PEC&apos;&quot;
  },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckPEC</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;tipoContatto==\&quot;PEC\&quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>CallUpdateAction</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;updateObject&quot;,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;updateResult&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;objectApiName&quot; : &quot;=&apos;AccountAgencyDetails__c&apos;&quot;,
    &quot;fields&quot; : &quot;=updateInput&quot;
  },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Remote Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TransformUpdateInput</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;updateInput&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;requestContext&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;AnagDetailRecapitiUpdateTransform&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckSuccess</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;calloutResponse:statusCode &lt; 400&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : false,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>15.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>AddTelefono</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;transformInput&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;codiceAgenzia&quot; : &quot;=CONCAT(&apos;&apos;, %codiceAgenzia%)&quot;,
    &quot;referente&quot; : &quot;=&quot;,
    &quot;flagPreferito&quot; : &quot;=Preferred&quot;,
    &quot;tipologiaContatto&quot; : &quot;=IF(Usage == \&quot;Personale\&quot;, \&quot;PER\&quot;, IF(Usage == \&quot;Lavoro\&quot;, \&quot;LAV\&quot;, Usage))&quot;,
    &quot;tipoContatto&quot; : &quot;=&apos;TEL&apos;&quot;,
    &quot;dataFineEffetto&quot; : &quot;=&quot;,
    &quot;compagnia&quot; : &quot;=requestContext:compagnia&quot;,
    &quot;dataInizioEffetto&quot; : &quot;=FORMATDATETIME(NOW(), &apos;yyyy-MM-dd&apos;T&apos;HH:mm:ss.SSSZ&apos;, &apos;Europe/Rome&apos;)&quot;,
    &quot;id&quot; : &quot;=CONCAT(&apos;&apos;, idContatto)&quot;,
    &quot;contatto&quot; : &quot;=TOSTRING(validationResult:result)&quot;,
    &quot;ciu&quot; : &quot;=requestContext:ciu&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TelefonoUpdateCandidate</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;requestContext:accountAgencyDetail:Tel&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;Usage&quot; : &quot;=RecAgenziaTelefonoUsage&quot;,
    &quot;Value&quot; : &quot;=TOSTRING(validationResult:result)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TelefonoValidationFailed</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;isActive&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;NOT(%validationResult:success%)&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;validationResult&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ValidateTelefono</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : true,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;AnagDetails_RecapitiValidate&quot;,
  &quot;responseJSONNode&quot; : &quot;validationResult&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;contactValue&quot; : &quot;=%contattoInput%&quot;,
    &quot;inputMode&quot; : &quot;=&apos;UPDATE&apos;&quot;,
    &quot;contactTarget&quot; : &quot;=Usage&quot;,
    &quot;contactType&quot; : &quot;=&apos;TEL&apos;&quot;
  },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckTelefono</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;tipoContatto==\&quot;TEL\&quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>AddTelefonoReferente</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;transformInput&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;codiceAgenzia&quot; : &quot;=CONCAT(&apos;&apos;, %codiceAgenzia%)&quot;,
    &quot;referente&quot; : &quot;=TOSTRING(referente)&quot;,
    &quot;flagPreferito&quot; : &quot;=Preferred&quot;,
    &quot;tipologiaContatto&quot; : &quot;=IF(Usage == \&quot;Personale\&quot;, \&quot;PER\&quot;, IF(Usage == \&quot;Lavoro\&quot;, \&quot;LAV\&quot;, Usage))&quot;,
    &quot;tipoContatto&quot; : &quot;=&apos;TELREF&apos;&quot;,
    &quot;dataFineEffetto&quot; : &quot;=&quot;,
    &quot;compagnia&quot; : &quot;=requestContext:compagnia&quot;,
    &quot;dataInizioEffetto&quot; : &quot;=FORMATDATETIME(NOW(), &apos;yyyy-MM-dd&apos;T&apos;HH:mm:ss.SSSZ&apos;, &apos;Europe/Rome&apos;)&quot;,
    &quot;id&quot; : &quot;=CONCAT(&apos;&apos;, idContatto)&quot;,
    &quot;contatto&quot; : &quot;=TOSTRING(validationResult:result)&quot;,
    &quot;ciu&quot; : &quot;=requestContext:ciu&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TelefonoReferenteUpdateCandidate</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;requestContext:accountAgencyDetail:TelReferente&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;Usage&quot; : &quot;=RecAgenziaTelefonoReferenteUsage&quot;,
    &quot;Value&quot; : &quot;=TOSTRING(validationResult:result)&quot;,
    &quot;Name&quot; : &quot;=RecAgenziaReferente&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TelefonoReferenteValidationFailed</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;isActive&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;NOT(%validationResult:success%)&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;validationResult&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ValidateTelefonoReferente</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : true,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;AnagDetails_RecapitiValidate&quot;,
  &quot;responseJSONNode&quot; : &quot;validationResult&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;contactValue&quot; : &quot;=%contattoInput%&quot;,
    &quot;inputMode&quot; : &quot;=&apos;UPDATE&apos;&quot;,
    &quot;contactTarget&quot; : &quot;=Usage&quot;,
    &quot;contactPerson&quot; : &quot;=referente&quot;,
    &quot;contactType&quot; : &quot;=&apos;TELREF&apos;&quot;
  },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckTelefonoReferente</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;tipoContatto==\&quot;TELREF\&quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Debug</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;contactValue&quot; : &quot;=contatto&quot;,
    &quot;inputMode&quot; : &quot;=&apos;UPDATE&apos;&quot;,
    &quot;contactTarget&quot; : &quot;=Usage&quot;,
    &quot;contactType&quot; : &quot;=&apos;CELL&apos;&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetAgencyCode</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;getAgencyCode&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;result&quot;,
  &quot;responseJSONNode&quot; : &quot;requestContext:codiceAgenzia&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;userId&quot; : &quot;=userId&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetDetailUserContext</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;recordId&quot;,
    &quot;element&quot; : &quot;recordId&quot;
  }, {
    &quot;inputParam&quot; : &quot;userId&quot;,
    &quot;element&quot; : &quot;User.userId&quot;
  } ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;requestContext&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;AnagDetailGetUserEditContext&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>InitList</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;transformInput&quot;,
  &quot;responseJSONNode&quot; : &quot;transformInput&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;transformInput&quot; : &quot;=LIST()&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : false,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ReturnResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;request&quot; : &quot;=request&quot;,
    &quot;validationResult&quot; : &quot;=validationResult&quot;,
    &quot;contattoInput&quot; : &quot;=contattoInput&quot;,
    &quot;Debug&quot; : &quot;=Debug&quot;
  },
  &quot;sendJSONPath&quot; : &quot;response&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>16.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SendRequest</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : true,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;IntegrationService_IntegrationService&quot;,
  &quot;responseJSONNode&quot; : &quot;calloutResponse&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;integrationId&quot; : &quot;=&apos;aggiornamentoContatti&apos;&quot;,
    &quot;body&quot; : &quot;=SERIALIZE(request)&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>13.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetSuccess</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;success&quot; : &quot;=calloutResponse:statusCode &lt; 400&quot;,
    &quot;response&quot; : &quot;=calloutResponse&quot;,
    &quot;errorMessage&quot; : &quot;=calloutResponse:status&quot;,
    &quot;variant&quot; : &quot;=IF(calloutResponse:statusCode &lt; 400, &apos;success&apos;, &apos;error&apos;)&quot;,
    &quot;title&quot; : &quot;=IF(calloutResponse:statusCode &lt; 400, &apos;Operazione effettuata&apos;, &apos;Errore&apos;)&quot;,
    &quot;message&quot; : &quot;=IF(calloutResponse:statusCode &lt; 400, &apos;Dati salvati&apos;, &apos;Si è verificato un errore&apos;)&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>14.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetValuesBody</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;request&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;compagnia&quot; : &quot;=%compagnia%&quot;,
    &quot;contatti&quot; : &quot;=LIST(%ArrayTransform%)&quot;,
    &quot;userId&quot; : &quot;=%userIdReqBody%&quot;,
    &quot;username&quot; : &quot;=%username%&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>12.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessKey>AnagDetails_RecapitiSingleUpdate</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;transientValues&quot; : {
    &quot;activateOrDeactivateConsent&quot; : false
  }
}</propertySetConfig>
    <subType>RecapitiSingleUpdate</subType>
    <type>AnagDetails</type>
    <uniqueName>AnagDetails_RecapitiSingleUpdate_Procedure_5</uniqueName>
    <versionNumber>5.0</versionNumber>
</OmniIntegrationProcedure>
